<!-- H5即时受理-关联配置管理 -->
<template>
  <div class="ele-body">
    <el-card shadow="never">
      <div class="config-header">
        <h2>H5即时受理-关联配置管理</h2>
        <div class="config-desc">
          <p>在此页面可以配置地市、模块分类、模块、商品属性类别、商品标签之间的关联关系</p>
        </div>
      </div>

      <!-- 操作区域 -->
      <div class="config-operation">
        <el-steps :active="currentStep" finish-status="success" simple>
          <el-step title="选择地市" icon="el-icon-map-location"></el-step>
          <el-step title="配置模块分类" icon="el-icon-s-grid"></el-step>
          <el-step title="配置模块" icon="el-icon-s-platform"></el-step>
          <el-step title="配置商品属性和标签" icon="el-icon-s-goods"></el-step>
        </el-steps>
      </div>

      <!-- 步骤内容区域 -->
      <div class="config-content">
        <!-- 步骤1: 选择地市 -->
        <div v-show="currentStep === 1" class="step-content">
          <div class="step-title">第一步: 选择需要配置的地市</div>
          <div class="city-selection">
            <div class="city-select-header">
              <el-checkbox v-model="selectAllCities" :indeterminate="isIndeterminate"
                @change="handleCheckAllCitiesChange">全选</el-checkbox>
            </div>
            <div class="city-checkbox-group">
              <el-checkbox-group v-model="selectedCities" @change="handleCitiesChange">
                <el-checkbox v-for="city in cities" :key="city.cityCode" :label="city.cityCode">
                  {{ city.cityName }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="step-actions">
            <el-button type="primary" @click="nextStep" :disabled="selectedCities.length === 0">下一步</el-button>
          </div>
        </div>

        <!-- 步骤2: 配置模块分类 -->
        <div v-show="currentStep === 2" class="step-content">
          <div class="step-title">第二步: 为选中的地市配置模块分类</div>

          <!-- 地市分组显示 -->
          <div class="city-groups">
            <el-collapse v-model="expandedCities">
              <el-collapse-item v-for="cityCode in selectedCities" :key="cityCode" :name="cityCode">
                <template slot="title">
                  <div class="group-header">
                    <span class="group-title">{{ getCityName(cityCode) }}</span>
                    <el-tag size="mini" type="info" class="count-tag">
                      已选{{ getCityCategoryCount(cityCode) }}项
                    </el-tag>
                  </div>
                </template>

                <!-- 每个地市的模块分类配置 -->
                <div class="category-selection">
                  <div class="category-transfer">
                    <el-transfer :value="cityCategories[cityCode] ? cityCategories[cityCode] : []"
                      @input="val => updateCityCategories(cityCode, val)" :data="categoryData"
                      :titles="['可选模块分类', '已选模块分类']" :button-texts="['移除', '添加']" :format="{
                        noChecked: '${total}',
                        hasChecked: '${checked}/${total}'
                      }" @remove="handleRemoveCategories(cityCode, $event, 'left', $event)">
                      <span slot-scope="{ option }" class="transfer-item">
                        {{ option.label }}
                      </span>
                    </el-transfer>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" @click="syncAllCategories(); nextStep()"
              :disabled="!hasCategorySelected">下一步</el-button>
          </div>
        </div>

        <!-- 步骤3: 配置模块 -->
        <div v-show="currentStep === 3" class="step-content">
          <div class="step-title">第三步: 为选中的地市和模块分类配置模块</div>

          <!-- 地市+分类分组显示 -->
          <div class="city-category-groups">
            <el-collapse v-model="expandedCityCategories">
              <template v-for="cityCode in selectedCities">
                <template v-if="(cityCategories[cityCode] || []).length > 0">
                  <el-collapse-item v-for="categoryId in cityCategories[cityCode]" :key="`${cityCode}-${categoryId}`"
                    :name="`${cityCode}-${categoryId}`">
                    <template slot="title">
                      <div class="group-header">
                        <span class="group-title">{{ getCityName(cityCode) }} - {{ getCategoryName(categoryId) }}</span>
                        <el-tag size="mini" type="info" class="count-tag">
                          已选{{ getCityCategoryModuleCount(cityCode, categoryId) }}项
                        </el-tag>
                      </div>
                    </template>

                    <!-- 每个地市+分类的模块配置 -->
                    <div class="module-selection">
                      <div class="module-transfer">
                        <el-transfer :value="getCityModulesArray(cityCode, categoryId)"
                          @input="val => updateCityModules(cityCode, categoryId, val)" :data="moduleData"
                          :titles="['可选模块', '已选模块']" :button-texts="['移除', '添加']" :format="{
                            noChecked: '${total}',
                            hasChecked: '${checked}/${total}'
                          }" @remove="handleRemoveModules(cityCode, categoryId, $event, 'left', $event)">
                          <span slot-scope="{ option }" class="transfer-item">
                            <span>{{ option.label }}</span>
                          </span>
                        </el-transfer>
                      </div>
                    </div>
                  </el-collapse-item>
                </template>
              </template>
            </el-collapse>
          </div>

          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" @click="syncAllModules(); nextStep()"
              :disabled="!hasModuleSelected">下一步</el-button>
          </div>
        </div>

        <!-- 步骤4: 配置商品属性和标签 -->
        <div v-show="currentStep === 4" class="step-content">
          <div class="step-title">第四步: 为选中的模块配置商品属性类别和标签</div>

          <!-- 地市+分类+模块分组显示 -->
          <div class="city-category-module-groups">
            <el-collapse v-model="expandedCityModules">
              <template v-for="cityCode in selectedCities">
                <template v-if="(cityCategories[cityCode] || []).length > 0">
                  <template v-for="categoryId in cityCategories[cityCode]">
                    <template v-if="getCityModulesArray(cityCode, categoryId).length > 0">
                      <el-collapse-item v-for="moduleId in getCityModulesArray(cityCode, categoryId)"
                        :key="`${cityCode}-${categoryId}-${moduleId}`" :name="`${cityCode}-${categoryId}-${moduleId}`">
                        <template slot="title">
                          <div class="group-header">
                            <span class="group-title">{{ getCityName(cityCode) }} - {{ getCategoryName(categoryId) }} -
                              {{ getModuleName(moduleId) }}</span>
                            <div>
                              <el-tag size="mini" type="success" class="count-tag">
                                属性{{ getModuleAttributeCount(cityCode, categoryId, moduleId) }}项
                              </el-tag>
                              <el-tag size="mini" type="warning" class="count-tag">
                                标签{{ getModuleTagCount(cityCode, categoryId, moduleId) }}项
                              </el-tag>
                            </div>
                          </div>
                        </template>

                        <!-- 每个模块的属性类别和标签配置 -->
                        <div class="attribute-tag-selection">
                          <el-tabs :value="getModuleTabsValue(cityCode, categoryId, moduleId)"
                            @input="val => updateModuleTab(cityCode, categoryId, moduleId, val)" type="border-card"
                            class="attribute-tag-tabs">
                            <el-tab-pane label="商品属性类别配置" name="attribute">
                              <div class="drag-sort-tip">
                                <i class="el-icon-sort"></i>
                                <span>在右侧已选择区域，您可以通过拖拽 <i class="el-icon-rank"></i> 图标调整属性类别的显示顺序</span>
                              </div>
                              <div class="attribute-transfer">
                                <el-transfer ref="sortTransfer" target-order="unshift"
                                  :value="getModuleAttributesArray(cityCode, categoryId, moduleId)"
                                  @input="val => updateModuleAttributes(cityCode, categoryId, moduleId, val)"
                                  :data="attributeTypeData" :titles="['可选商品属性类别', '已选商品属性类别']"
                                  :button-texts="['移除', '添加']" :format="{
                                    noChecked: '${total}',
                                    hasChecked: '${checked}/${total}'
                                  }"
                                  @remove="handleRemoveAttributeTypes(cityCode, categoryId, moduleId, $event, 'left', $event)">

                                  <span slot-scope="{ option }"
                                    class="transfer-item draggable-item"
                                    draggable="!option.disabled"
                                    @dragstart="dragStart($event, option)"
                                    @dragover.prevent
                                    @dragenter.prevent
                                    @drop="drop($event, option, cityCode, categoryId, moduleId)"
                                    :title="'拖拽可调整排序：' + option.label">
                                    <i v-if="getModuleAttributesArray(cityCode, categoryId, moduleId).includes(option.key)" class="el-icon-rank drag-handle"></i>
                                    {{ option.label }}
                                  </span>

                                </el-transfer>
                              </div>
                            </el-tab-pane>
                            <el-tab-pane label="商品标签配置" name="tag">
                              <div class="drag-sort-tip">
                                <i class="el-icon-sort"></i>
                                <span>在右侧已选择区域，您可以通过拖拽 <i class="el-icon-rank"></i> 图标调整标签的显示顺序</span>
                              </div>
                              <div class="tag-transfer">
                                <el-transfer
                                  :value="getModuleTagsArray(cityCode, categoryId, moduleId)" target-order="unshift"
                                  @input="val => updateModuleTags(cityCode, categoryId, moduleId, val)" :data="tagData"
                                  :titles="['可选商品标签', '已选商品标签']" :button-texts="['移除', '添加']" :format="{
                                    noChecked: '${total}',
                                    hasChecked: '${checked}/${total}'
                                  }"
                                  @remove="handleRemoveTags(cityCode, categoryId, moduleId, $event, 'left', $event)">
                                  <span slot-scope="{ option }"
                                    class="transfer-item draggable-item"
                                    draggable="!option.disabled"
                                    @dragstart="dragStart2($event, option)"
                                    @dragover.prevent
                                    @dragenter.prevent
                                    @drop="drop2($event, option, cityCode, categoryId, moduleId)"
                                    :title="'拖拽可调整排序：' + option.label">
                                    <i v-if="getModuleTagsArray(cityCode, categoryId, moduleId).includes(option.key)" class="el-icon-rank drag-handle"></i>
                                    {{ option.label }}
                                  </span>
                                </el-transfer>
                              </div>
                            </el-tab-pane>
                          </el-tabs>
                        </div>
                      </el-collapse-item>
                    </template>
                  </template>
                </template>
              </template>
            </el-collapse>
          </div>

          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button type="primary" @click="syncAllAttributesAndTags(); saveConfiguration()">保存配置</el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  getAllCities,
  getCategories,
  getModules,
  getAttributeTypes,
  getTags,
  getCategoriesByCityCode,
  getModulesByCityCategoryRefId,
  saveCityCategoryRef,
  batchSaveCityCategoryRef,
  saveCityCategoryModuleRef,
  batchSaveCityCategoryModuleRef,
  saveModuleAttributeTypeRef,
  batchSaveModuleAttributeTypeRef,
  saveModuleTagRef,
  batchSaveModuleTagRef,
  getAttributeTypesByModuleRefId,
  getTagsByModuleRefId,
  deleteCityCategoryRef,
  deleteCityCategoryModuleRef,
  deleteModuleAttributeTypeRef,
  deleteModuleTagRef,
  batchDeleteCityCategoryRef,
  batchDeleteCityCategoryModuleRef,
  batchDeleteModuleAttributeTypeRef,
  batchDeleteModuleTagRef,
  batchUpdateRank,
  batchUpdateRankSx
} from '@/api/hnzsxH5/configRelation';
import { configRelationDataLoader, configRelationSaveOptimizer } from '@/utils/configRelationOptimizer';
import Sortable from 'sortablejs'
export default {
  name: 'ConfigRelation',
  data() {
    return {
      // 当前步骤
      currentStep: 1,

      // 地市相关数据
      cities: [], // 所有地市
      selectedCities: [], // 选中的地市
      selectAllCities: false, // 是否全选地市
      isIndeterminate: false, // 是否半选状态

      // 模块分类相关数据
      categories: [], // 所有模块分类
      categoryData: [], // 穿梭框数据
      selectedCategories: [], // 选中的模块分类

      // 模块相关数据
      modules: [], // 所有模块
      moduleData: [], // 穿梭框数据
      selectedModules: [], // 选中的模块

      // 商品属性和标签相关数据
      attributeTagTab: 'attribute', // 当前选中的选项卡
      attributeTypes: [], // 所有商品属性类别
      attributeTypeData: [], // 穿梭框数据
      selectedAttributeTypes: [], // 选中的商品属性类别
      tags: [], // 所有商品标签
      tagData: [], // 穿梭框数据
      selectedTags: [], // 选中的商品标签

      // 保存中间数据的映射关系
      cityCategoryRefMap: {}, // 地市-模块分类关系映射
      cityCategoryModuleRefMap: {}, // 地市分类-模块关系映射

      // 用于存储ID与关联关系的映射
      categoryRefIdMap: {}, // 分类ID到关联ID的映射
      moduleRefIdMap: {}, // 模块ID到关联ID的映射 
      attributeTypeRefIdMap: {}, // 属性类型ID到关联ID的映射
      tagRefIdMap: {}, // 标签ID到关联ID的映射

      // 新增的变量
      cityCategories: {}, // 存储每个地市的模块分类
      cityModules: {}, // 存储每个地市和分类的模块
      attributeTagTabs: {}, // 存储每个模块的属性标签选项卡
      moduleAttributes: {}, // 存储每个模块的商品属性类别
      moduleTags: {}, // 存储每个模块的商品标签
      expandedCities: [], // 存储展开的地市
      expandedCityCategories: [], // 存储展开的地市和分类
      expandedCityModules: [], // 存储展开的地市和模块
      sortableRight: null, // 用于保存sortable实例
      draggedItem: null,  // 当前拖拽项
      draggedItem2: null,  // 当前拖拽项
      transferKey: 0
    };
  },
  computed: {
    hasCategorySelected() {
      for (const cityCode in this.cityCategories) {
        if ((this.cityCategories[cityCode] || []).length > 0) {
          return true;
        }
      }
      return false;
    },

    hasModuleSelected() {
      for (const key in this.cityModules) {
        if ((this.cityModules[key] || []).length > 0) {
          return true;
        }
      }
      return false;
    }
  },
  created() {
    this.fetchInitialData();
  },
  mounted() {
    // 确保页面加载后能看到分类数据
    if (this.categories.length === 0 || this.categoryData.length === 0) {
      this.fetchCategoryData();
    }
  },
  methods: {
    // 排序
    // 开始拖拽
    dragStart(event, option) {
      console.log(event, '================', option);

      this.draggedItem = option;
      event.dataTransfer.effectAllowed = 'move';

      // 添加拖拽样式
      event.target.classList.add('dragging');

      // 设置拖拽数据
      event.dataTransfer.setData('text/plain', JSON.stringify(option));
    },

    // 放置 - 属性类别
    async drop(event, targetOption, cityCode, categoryId, moduleId) {
      event.preventDefault();
      console.log(targetOption, '==targetOption');

      if (!this.draggedItem || this.draggedItem.key === targetOption.key) return;

      const key = `${cityCode}-${categoryId}-${moduleId}`;
      const currentAttributes = this.moduleAttributes[key] || [];

      const currentIndex = currentAttributes.indexOf(this.draggedItem.key);
      const targetIndex = currentAttributes.indexOf(targetOption.key);
      console.log(targetIndex, 'targetIndex');
      console.log(currentIndex, 'currentIndex');
      console.log(currentAttributes, '==currentAttributes');

      if (currentIndex > -1 && targetIndex > -1) {
        // 重新排序数组
        const newAttributes = [...currentAttributes];
        newAttributes.splice(currentIndex, 1);
        newAttributes.splice(targetIndex, 0, this.draggedItem.key);

        // 更新属性数组
        this.updateModuleAttributes(cityCode, categoryId, moduleId, newAttributes);

        // 保存排序到后台
        await this.saveAttributeSort(cityCode, categoryId, moduleId, newAttributes);
      }

      // 移除拖拽样式
      const draggingElements = document.querySelectorAll('.dragging');
      draggingElements.forEach(el => el.classList.remove('dragging'));

      this.draggedItem = null;
    },
    dragStart2(event, option) {
      console.log(event, '================', option);
      this.draggedItem2 = option;
      event.dataTransfer.effectAllowed = 'move';
      event.dataTransfer.setData('text/plain', JSON.stringify(option));

      // 添加拖拽样式
      event.target.classList.add('dragging');
    },
    // 放置 - 标签
    async drop2(event, targetOption, cityCode, categoryId, moduleId) {
      event.preventDefault();

      if (!this.draggedItem2 || this.draggedItem2.key === targetOption.key) return;

      const key = `${cityCode}-${categoryId}-${moduleId}`;
      // 创建当前数据的深拷贝
      const currentTags = JSON.parse(JSON.stringify(this.moduleTags[key] || []));
      console.log(currentTags,'=currentTags');

      const currentIndex = currentTags.indexOf(this.draggedItem2.key);
      const targetIndex = currentTags.indexOf(targetOption.key);


      if (currentIndex > -1 && targetIndex > -1) {
        // 创建新数组
        const newTags = [...currentTags];
        // 移除拖拽项
        newTags.splice(currentIndex, 1);
        // 插入到目标位置
        newTags.splice(targetIndex, 0, this.draggedItem2.key);
        console.log(newTags,'==newTags');

        // 更新数据
        await this.updateModuleTags(cityCode, categoryId, moduleId, newTags);

        // 保存排序到后台
        await this.saveTagSort(cityCode, categoryId, moduleId, newTags);
      }

      // 移除拖拽样式
      const draggingElements = document.querySelectorAll('.dragging');
      draggingElements.forEach(el => el.classList.remove('dragging'));

      this.draggedItem2 = null;
    },

    
    // 获取分类数据
    async fetchCategoryData() {
      try {
        const loading = this.$loading({ lock: true, text: '加载分类数据中...' });
        const categoriesRes = await getCategories({
          status: 1 // 有效状态
        });

        console.log('单独获取分类数据结果:', categoriesRes);

        if (categoriesRes && categoriesRes.rows) {
          this.categories = categoriesRes.rows;
          this.categoryData = this.categories.map(item => ({
            key: item.id,
            label: item.moduleTypeName,
            disabled: false
          }));
        } else if (Array.isArray(categoriesRes)) {
          this.categories = categoriesRes;
          this.categoryData = this.categories.map(item => ({
            key: item.id,
            label: item.moduleTypeName,
            disabled: false
          }));
        }

        loading.close();
      } catch (error) {
        console.error('获取分类数据失败:', error);
        this.$message.error('加载分类数据失败');
      }
    },
    // 获取初始数据
    async fetchInitialData() {
      try {
        const loading = this.$loading({ lock: true, text: '加载数据中...' });
        // 获取地市数据
        this.cities = await getAllCities();

        // 获取模块分类数据
        const categoriesRes = await getCategories();
        console.log('分类数据结果:', categoriesRes);
        if (categoriesRes && categoriesRes.rows) {
          this.categories = categoriesRes.rows;
          this.categoryData = this.categories.map(item => ({
            key: item.id,
            label: item.moduleTypeName,
            disabled: false
          }));
        } else if (Array.isArray(categoriesRes)) {
          this.categories = categoriesRes;
          this.categoryData = this.categories.map(item => ({
            key: item.id,
            label: item.moduleTypeName,
            disabled: false
          }));
        }

        // 获取模块数据
        const modulesRes = await getModules();
        if (modulesRes && modulesRes.rows) {
          this.modules = modulesRes.rows;
          this.moduleData = modulesRes.rows.map(item => ({
            key: item.id,
            label: item.moduleName,
            disabled: false
          }));
        } else if (Array.isArray(modulesRes)) {
          this.modules = modulesRes;
          this.moduleData = modulesRes.map(item => ({
            key: item.id,
            label: item.moduleName,
            disabled: false
          }));
        }

        // 获取商品属性类别数据
        const attributeTypesRes = await getAttributeTypes();
        if (attributeTypesRes && attributeTypesRes.rows) {
          this.attributeTypes = attributeTypesRes.rows;
          this.attributeTypeData = attributeTypesRes.rows.map(item => ({
            key: item.id,
            label: item.attributeTypeName,
            disabled: false
          }));
        } else if (Array.isArray(attributeTypesRes)) {
          this.attributeTypes = attributeTypesRes;
          this.attributeTypeData = attributeTypesRes.map(item => ({
            key: item.id,
            label: item.attributeTypeName,
            disabled: false
          }));
        }

        // 获取商品标签数据
        const tagsRes = await getTags();
        if (tagsRes && tagsRes.rows) {
          this.tags = tagsRes.rows;
          this.tagData = tagsRes.rows.map(item => ({
            key: item.id,
            label: item.tagName,
            disabled: false
          }));
        } else if (Array.isArray(tagsRes)) {
          this.tags = tagsRes;
          this.tagData = tagsRes.map(item => ({
            key: item.id,
            label: item.tagName,
            disabled: false
          }));
        }

        loading.close();
      } catch (error) {
        this.$message.error(error.message || '数据加载失败');
      }
    },

    // 全选地市
    handleCheckAllCitiesChange(checked) {
      this.selectedCities = checked ? this.cities.map(item => item.cityCode) : [];
      this.isIndeterminate = false;
    },

    // 地市选择变化
    handleCitiesChange(value) {
      const checkedCount = value.length;
      this.selectAllCities = checkedCount === this.cities.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;
    },

    // 获取地市名称
    getCityName(cityCode) {
      const city = this.cities.find(item => item.cityCode === cityCode);
      return city ? city.cityName : cityCode;
    },

    // 获取模块分类名称
    getCategoryName(categoryId) {
      const category = this.categories.find(item => item.id === categoryId);
      return category ? category.moduleTypeName : categoryId;
    },

    // 获取模块名称
    getModuleName(moduleId) {
      const module = this.modules.find(item => item.id === moduleId);
      return module ? module.moduleName : moduleId;
    },

    // 上一步
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
      }
    },

    // 下一步
    async nextStep() {
      if (this.currentStep < 4) {
        // 根据当前步骤进行相应的预加载操作
        if (this.currentStep === 1) {
          // 从步骤1到步骤2：预加载已选地市的模块分类关系
          const loading = this.$loading({ lock: true, text: '加载地市关联的模块分类中...' });
          try {
            // 清空之前的关联映射
            this.cityCategoryRefMap = {};
            this.cityCategories = {}; // 重置cityCategories

            // 加载地市-模块分类关系
            await this.loadCategoryRelations();

            // 展开有分类的地市
            for (const cityCode of this.selectedCities) {
              if ((this.cityCategories[cityCode] || []).length > 0 && !this.expandedCities.includes(cityCode)) {
                this.expandedCities.push(cityCode);
              }
            }

            // 同步到selectedCategories
            this.syncAllCategories();
          } catch (error) {
            console.error('预加载地市-分类关系失败:', error);
            this.$message.error('加载关联数据失败，请重试');
          } finally {
            loading.close();
          }
        } else if (this.currentStep === 2) {
          // 从步骤2到步骤3：预加载已选地市和分类的模块关系
          const loading = this.$loading({ lock: true, text: '加载分类关联的模块中...' });
          try {
            // 清空之前的模块关联映射
            this.cityCategoryModuleRefMap = {};
            this.cityModules = {}; // 重置cityModules

            // 加载地市分类-模块关系
            await this.loadModuleRelations();

            // 展开有模块的地市-分类
            for (const cityCode of this.selectedCities) {
              for (const categoryId of this.cityCategories[cityCode] || []) {
                const key = `${cityCode}-${categoryId}`;
                if ((this.cityModules[key] || []).length > 0 && !this.expandedCityCategories.includes(key)) {
                  this.expandedCityCategories.push(key);
                }
              }
            }

            // 同步到selectedModules
            this.syncAllModules();
          } catch (error) {
            console.error('预加载分类-模块关系失败:', error);
            this.$message.error('加载关联数据失败，请重试');
          } finally {
            loading.close();
          }
        } else if (this.currentStep === 3) {
          // 从步骤3到步骤4：预加载已选模块的商品属性和标签关系
          const loading = this.$loading({ lock: true, text: '加载模块关联的属性和标签中...' });
          try {
            // 重置属性和标签相关数据
            this.moduleAttributes = {};
            this.moduleTags = {};
            this.attributeTagTabs = {};

            // 加载模块-商品属性类别和标签关系
            await this.loadAttributeAndTagRelations();

            // 展开有属性或标签的模块组合
            for (const cityCode of this.selectedCities) {
              for (const categoryId of this.cityCategories[cityCode] || []) {
                const cityCategoryKey = `${cityCode}-${categoryId}`;

                for (const moduleId of this.cityModules[cityCategoryKey] || []) {
                  const key = `${cityCode}-${categoryId}-${moduleId}`;

                  if (((this.moduleAttributes[key] || []).length > 0 || (this.moduleTags[key] || []).length > 0) &&
                    !this.expandedCityModules.includes(key)) {
                    this.expandedCityModules.push(key);
                  }
                }
              }
            }

            // 同步到selectedAttributeTypes和selectedTags
            this.syncAllAttributesAndTags();
          } catch (error) {
            console.error('预加载模块-属性标签关系失败:', error);
            this.$message.error('加载关联数据失败，请重试');
          } finally {
            loading.close();
          }
        }

        this.currentStep++;
      }
    },

    // 优化后的保存配置
    async saveConfiguration() {
      try {
        const loading = this.$loading({ lock: true, text: '保存配置中...' });

        // 使用优化的保存流程
        await configRelationSaveOptimizer.optimizedSaveConfiguration(this);

        loading.close();
        this.$message.success('配置保存成功');

        // 重置表单
        this.resetForm();

        // 清除优化器缓存
        configRelationSaveOptimizer.clearCache();
        configRelationDataLoader.clearCache();
      } catch (error) {
        console.error('保存配置失败:', error);
        this.$message.error(error.message || '配置保存失败');
      }
    },

    // 保留原有的保存配置方法作为备用
    async saveConfigurationLegacy() {
      try {
        const loading = this.$loading({ lock: true, text: '保存配置中...' });

        // 1. 保存前准备，预加载现有关系数据减少后续查询
        await this.prepareForSave();

        // 2. 保存地市-模块分类关系
        await this.saveCityCategoryRelations();

        // 3. 保存地市分类-模块关系
        await this.saveCityCategoryModuleRelations();

        // 4. 保存模块-商品属性类别关系和模块-商品标签关系
        await this.saveModuleAttributeTagRelations();

        // 5. 删除不再需要的关联关系
        await this.deleteUnusedRelations();

        loading.close();
        this.$message.success('配置保存成功');

        // 重置表单
        this.resetForm();
      } catch (error) {
        this.$message.error(error.message || '配置保存失败');
      }
    },

    // 保存前准备，预加载现有关系数据减少后续查询
    async prepareForSave() {
      // 预先获取已有的地市-分类关联关系
      await Promise.all(this.selectedCities.map(async cityCode => {
        const existingCategoryRefs = await getCategoriesByCityCode(cityCode);

        // 将已有关联关系存入映射中
        for (const ref of existingCategoryRefs) {
          const key = `${cityCode}_${ref.categoryId}`;
          this.cityCategoryRefMap[key] = ref.refId;

          // 预先加载分类对应的模块关系
          if (this.selectedCategories.includes(ref.categoryId)) {
            const existingModuleRefs = await getModulesByCityCategoryRefId(ref.refId);

            // 将已有模块关联存入映射中
            for (const moduleRef of existingModuleRefs) {
              const key = `${ref.refId}_${moduleRef.moduleId}`;
              this.cityCategoryModuleRefMap[key] = moduleRef.refId;
            }
          }
        }
      }));
    },

    // 删除不再需要的关联关系
    async deleteUnusedRelations() {
      try {
        const loading = this.$loading({ lock: true, text: '正在优化关联关系...' });

        // 预先获取所有关系数据，减少API调用次数
        const cityRelationsMap = new Map(); // 存储地市-分类关系
        const moduleRelationsMap = new Map(); // 存储地市分类-模块关系
        const attributeRelationsMap = new Map(); // 存储模块-属性关系
        const tagRelationsMap = new Map(); // 存储模块-标签关系

        // 记录模块组合的属性和标签集合 (使用组合标识符确保每个分组都被正确处理)
        const groupAttributeMap = new Map(); // 组合标识符 -> 属性ID集合
        const groupTagMap = new Map(); // 组合标识符 -> 标签ID集合
        const moduleToGroupsMap = new Map(); // 模块ID -> 组合标识符列表

        // 构建组合->属性/标签映射，记录每个组合应该保留的属性和标签
        console.log('开始构建组合->属性/标签映射...');
        for (const cityCode of this.selectedCities) {
          for (const categoryId of this.cityCategories[cityCode] || []) {
            const cityCategoryKey = `${cityCode}-${categoryId}`;

            for (const moduleId of this.cityModules[cityCategoryKey] || []) {
              const groupKey = `${cityCode}-${categoryId}-${moduleId}`;

              // 维护一个模块ID到所有包含它的组合的映射
              if (!moduleToGroupsMap.has(moduleId)) {
                moduleToGroupsMap.set(moduleId, []);
              }
              moduleToGroupsMap.get(moduleId).push(groupKey);

              // 记录该组合选中的属性
              const selectedAttributes = this.moduleAttributes[groupKey] || [];
              if (!groupAttributeMap.has(groupKey)) {
                groupAttributeMap.set(groupKey, new Set());
              }
              selectedAttributes.forEach(attrId => {
                groupAttributeMap.get(groupKey).add(attrId);
              });

              // 记录该组合选中的标签
              const selectedTags = this.moduleTags[groupKey] || [];
              if (!groupTagMap.has(groupKey)) {
                groupTagMap.set(groupKey, new Set());
              }
              selectedTags.forEach(tagId => {
                groupTagMap.get(groupKey).add(tagId);
              });

              console.log(`组合 ${groupKey} 选中了 ${selectedAttributes.length} 个属性和 ${selectedTags.length} 个标签`);
            }
          }
        }

        console.log('开始获取现有关系数据...');
        // 一次性获取所有地市的关系数据
        await Promise.all(this.selectedCities.map(async cityCode => {
          const categoryRefs = await getCategoriesByCityCode(cityCode);
          cityRelationsMap.set(cityCode, categoryRefs);

          // 同时获取每个地市-分类下的模块关系
          await Promise.all(categoryRefs.map(async ref => {
            if (ref.refId) {
              const moduleRefs = await getModulesByCityCategoryRefId(ref.refId);
              moduleRelationsMap.set(ref.refId, moduleRefs);

              // 获取每个模块关系下的属性和标签关系
              await Promise.all(moduleRefs.map(async moduleRef => {
                if (moduleRef.refId) {
                  const [attrTypes, tags] = await Promise.all([
                    getAttributeTypesByModuleRefId(moduleRef.refId),
                    getTagsByModuleRefId(moduleRef.refId)
                  ]);
                  attributeRelationsMap.set(moduleRef.refId, attrTypes);
                  tagRelationsMap.set(moduleRef.refId, tags);
                }
              }));
            }
          }));
        }));

        // 处理数据，确定要删除的项
        const toDeleteCategoryRefIds = []; // 地市-分类关系
        const toDeleteModuleRefIds = []; // 地市分类-模块关系
        const toDeleteAttrRefIds = []; // 模块-属性关系
        const toDeleteTagRefIds = []; // 模块-标签关系

        // 处理地市-分类关系
        for (const [cityCode, categoryRefs] of cityRelationsMap.entries()) {
          for (const ref of categoryRefs) {
            // 检查该地市下是否还有此分类
            const isCategorySelected = (this.cityCategories[cityCode] || []).includes(ref.categoryId);

            // 如果分类不在该地市选中列表中，则标记该关系需要删除
            if (!isCategorySelected) {
              toDeleteCategoryRefIds.push(ref.refId);

              // 从映射中移除
              const key = `${cityCode}_${ref.categoryId}`;
              delete this.cityCategoryRefMap[key];
            }
          }
        }

        // 处理地市分类-模块关系
        for (const [cityCategoryRefId, moduleRefs] of moduleRelationsMap.entries()) {
          for (const moduleRef of moduleRefs) {
            // 找到对应的地市和分类
            let found = false;
            let cityCategoryKey = '';

            // 寻找该cityCategoryRefId对应的cityCode和categoryId
            for (const cityCode of this.selectedCities) {
              for (const categoryId of this.cityCategories[cityCode] || []) {
                const key = `${cityCode}_${categoryId}`;
                if (this.cityCategoryRefMap[key] === cityCategoryRefId) {
                  cityCategoryKey = `${cityCode}-${categoryId}`;
                  found = true;
                  break;
                }
              }
              if (found) break;
            }

            // 如果找到对应的地市-分类组合，检查模块是否在该组合下选中
            if (found) {
              const isModuleSelected = (this.cityModules[cityCategoryKey] || []).includes(moduleRef.moduleId);

              // 如果模块不在该组合的选中列表中，则标记该关系需要删除
              if (!isModuleSelected) {
                toDeleteModuleRefIds.push(moduleRef.refId);

                // 从映射中移除
                for (const key in this.cityCategoryModuleRefMap) {
                  if (this.cityCategoryModuleRefMap[key] === moduleRef.refId) {
                    delete this.cityCategoryModuleRefMap[key];
                    break;
                  }
                }
              }
            }
          }
        }

        // 获取城市代码-分类ID-模块ID的映射，用于后续定位模块所属的组
        const moduleRefToGroupKeyMap = new Map(); // 模块关系ID -> 所属组key
        for (const cityCode of this.selectedCities) {
          for (const categoryId of this.cityCategories[cityCode] || []) {
            const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];
            if (!cityCategoryRefId) continue;

            for (const moduleId of this.cityModules[`${cityCode}-${categoryId}`] || []) {
              const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];
              if (!moduleRefId) continue;

              moduleRefToGroupKeyMap.set(moduleRefId, `${cityCode}-${categoryId}-${moduleId}`);
            }
          }
        }

        // 处理模块-属性关系
        for (const [moduleRefId, attrTypes] of attributeRelationsMap.entries()) {
          // 找到该模块关系对应的组合key
          const groupKey = moduleRefToGroupKeyMap.get(moduleRefId);

          if (groupKey) {
            // 获取该组合中选中的属性ID集合
            const selectedAttributeIds = groupAttributeMap.get(groupKey) || new Set();

            // 处理当前模块的属性关系
            for (const attrType of attrTypes) {
              // 如果属性不在该组合选中列表中，则标记该关系需要删除
              if (!selectedAttributeIds.has(attrType.attributeTypeId)) {
                toDeleteAttrRefIds.push(attrType.refId);
                console.log(`标记要删除的属性关系: 组合=${groupKey}, 属性ID=${attrType.attributeTypeId}, 关系ID=${attrType.refId}`);
              }
            }
          }
        }

        // 处理模块-标签关系
        for (const [moduleRefId, tags] of tagRelationsMap.entries()) {
          // 找到该模块关系对应的组合key
          const groupKey = moduleRefToGroupKeyMap.get(moduleRefId);

          if (groupKey) {
            // 获取该组合中选中的标签ID集合
            const selectedTagIds = groupTagMap.get(groupKey) || new Set();

            // 处理当前模块的标签关系
            for (const tag of tags) {
              // 如果标签不在该组合选中列表中，则标记该关系需要删除
              if (!selectedTagIds.has(tag.tagId)) {
                toDeleteTagRefIds.push(tag.refId);
                console.log(`标记要删除的标签关系: 组合=${groupKey}, 标签ID=${tag.tagId}, 关系ID=${tag.refId}`);
              }
            }
          }
        }

        console.log({
          '要删除的地市-分类关系': toDeleteCategoryRefIds.length,
          '要删除的地市分类-模块关系': toDeleteModuleRefIds.length,
          '要删除的模块-属性关系': toDeleteAttrRefIds.length,
          '要删除的模块-标签关系': toDeleteTagRefIds.length
        });

        // 批量删除关系，并行执行以提高速度
        const deletePromises = [];

        if (toDeleteCategoryRefIds.length > 0) {
          deletePromises.push(batchDeleteCityCategoryRef(toDeleteCategoryRefIds)
            .then(() => console.log(`批量删除地市-模块分类关系, 共${toDeleteCategoryRefIds.length}条`)));
        }

        if (toDeleteModuleRefIds.length > 0) {
          deletePromises.push(batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds)
            .then(() => console.log(`批量删除地市分类-模块关系, 共${toDeleteModuleRefIds.length}条`)));
        }

        if (toDeleteAttrRefIds.length > 0) {
          deletePromises.push(batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds)
            .then(() => console.log(`批量删除模块-商品属性类别关系, 共${toDeleteAttrRefIds.length}条`)));
        }

        if (toDeleteTagRefIds.length > 0) {
          deletePromises.push(batchDeleteModuleTagRef(toDeleteTagRefIds)
            .then(() => console.log(`批量删除模块-商品标签关系, 共${toDeleteTagRefIds.length}条`)));
        }

        await Promise.all(deletePromises);
        loading.close();

      } catch (error) {
        console.error('删除不再需要的关联关系失败:', error);
        throw new Error('删除不再需要的关联关系失败');
      }
    },

    // 保存地市-模块分类关系
    async saveCityCategoryRelations() {
      // 构建需要保存的数据
      const cityCategoryRefs = [];

      // 使用选中的地市和每个地市下的分类，确保所有分组都被考虑
      for (const cityCode of this.selectedCities) {
        // 获取该地市下选中的所有分类
        const categories = this.cityCategories[cityCode] || [];

        // 只为尚未关联的分类创建新的关联关系
        for (const categoryId of categories) {
          const key = `${cityCode}_${categoryId}`;

          // 如果此地市和分类的关联关系不存在，则创建新的
          if (!this.cityCategoryRefMap[key]) {
            cityCategoryRefs.push({
              cityCode: cityCode,
              categoryId: categoryId,
              status: 1, // 有效状态
              sort: 1 // 默认排序
            });
          }
        }
      }

      // 批量保存新增的关联关系
      if (cityCategoryRefs.length > 0) {
        console.log(`准备保存${cityCategoryRefs.length}个地市-模块分类关系`);
        const result = await batchSaveCityCategoryRef(cityCategoryRefs);
        console.log('保存地市-模块分类关系结果:', result);

        // 重新获取关联ID，确保映射表包含新创建的关联关系
        await this.fetchCityCategoriesAfterSave();
      }
    },

    // 保存地市-模块分类关系后重新获取关联ID
    async fetchCityCategoriesAfterSave() {
      // 只获取新添加的关系
      const promises = this.selectedCities.map(async cityCode => {
        // 查询当前地市下的所有关系
        const categoryRefs = await getCategoriesByCityCode(cityCode);

        // 更新映射表
        for (const ref of categoryRefs) {
          const key = `${cityCode}_${ref.categoryId}`;
          if (!this.cityCategoryRefMap[key]) {
            this.cityCategoryRefMap[key] = ref.refId;
          }
        }
      });

      await Promise.all(promises);
    },

    // 保存地市分类-模块关系
    async saveCityCategoryModuleRelations() {
      // 构建需要保存的数据
      const cityCategoryModuleRefs = [];
      const processedGroups = new Set(); // 记录已处理的地市-分类组合

      // 遍历所有地市和分类组合
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const cityCategoryKey = `${cityCode}-${categoryId}`;
          const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];

          if (!refId) {
            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);
            continue;
          }

          // 标记该地市-分类组合为已处理
          processedGroups.add(cityCategoryKey);

          // 获取该地市-分类下选中的所有模块
          const modules = this.cityModules[cityCategoryKey] || [];

          // 只为尚未关联的模块创建新的关联关系
          for (const moduleId of modules) {
            const key = `${refId}_${moduleId}`;

            // 如果此地市分类和模块的关联关系不存在，则创建新的
            if (!this.cityCategoryModuleRefMap[key]) {
              cityCategoryModuleRefs.push({
                cityCategoryRefId: refId,
                moduleId: moduleId,
                status: 1, // 有效状态
                isOneBeat: 1, // 一号一拍开关，默认开启
                // sort: 1 // 默认排序
              });
            }
          }
        }
      }

      // 批量保存新增的关联关系
      if (cityCategoryModuleRefs.length > 0) {
        console.log(`准备保存${cityCategoryModuleRefs.length}个地市分类-模块关系，涉及${processedGroups.size}个地市-分类组合`);
        const result = await batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);
        console.log('保存地市分类-模块关系结果:', result);

        // 重新获取关联ID，确保映射表包含新创建的关联关系
        await this.fetchModuleRefsAfterSave();
      }
    },

    // 保存模块关系后重新获取关联ID
    async fetchModuleRefsAfterSave() {
      const promises = [];
      const processedRefIds = new Set(); // 避免重复查询

      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.selectedCategories) {
          const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];

          if (refId && !processedRefIds.has(refId)) {
            processedRefIds.add(refId); // 标记为已处理

            promises.push((async () => {
              const moduleRefs = await getModulesByCityCategoryRefId(refId);

              // 更新映射表
              for (const moduleRef of moduleRefs) {
                const key = `${refId}_${moduleRef.moduleId}`;
                if (!this.cityCategoryModuleRefMap[key]) {
                  this.cityCategoryModuleRefMap[key] = moduleRef.refId;
                }
              }
            })());
          }
        }
      }

      await Promise.all(promises);
    },
 
    // 保存模块相关的属性和标签关系
    async saveModuleAttributeTagRelations() {
      console.log(this.selectedCities,'===selectedCities');
      
      // 构建需要保存的商品属性类别关系数据
      const moduleAttributeTypeRefs = [];

      // 构建需要保存的商品标签关系数据
      const moduleTagRefs = [];

      // 收集已处理的城市-分类-模块组合，确保每个组都被处理
      const processedGroups = new Set();

      const allAttributesPromises = [];
      const allTagsPromises = [];
      const attributeTypeRefMap = {};
      const tagRefMap = {};

      // 1. 先获取所有的已有关系数据，减少重复API调用
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const cityCategoryKey = `${cityCode}-${categoryId}`;
          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];

          if (!cityCategoryRefId) {
            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);
            continue;
          }

          // 处理该地市-分类下的所有模块
          for (const moduleId of this.cityModules[cityCategoryKey] || []) {
            const key = `${cityCode}-${categoryId}-${moduleId}`;
            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];

            if (!moduleRefId) {
              console.warn(`未找到模块${moduleId}的关联ID，跳过处理`);
              continue;
            }

            // 预加载该模块的属性和标签关系数据
            const attributePromise = getAttributeTypesByModuleRefId(moduleRefId).then(attributes => {
              attributeTypeRefMap[moduleRefId] = attributes;
            });

            const tagPromise = getTagsByModuleRefId(moduleRefId).then(tags => {
              tagRefMap[moduleRefId] = tags;
            });

            allAttributesPromises.push(attributePromise);
            allTagsPromises.push(tagPromise);
          }
        }
      }

      // 等待所有数据加载完成
      await Promise.all([...allAttributesPromises, ...allTagsPromises]);

      // 2. 遍历所有模块组合，确保每个组都被考虑
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const cityCategoryKey = `${cityCode}-${categoryId}`;
          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];

          if (!cityCategoryRefId) {
            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);
            continue;
          }

          // 处理该地市-分类下的所有模块
          for (const moduleId of this.cityModules[cityCategoryKey] || []) {
            const key = `${cityCode}-${categoryId}-${moduleId}`;
            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];

            if (!moduleRefId) {
              console.warn(`未找到模块${moduleId}的关联ID，跳过处理`);
              continue;
            }

            // 标记该组合为已处理
            processedGroups.add(key);

            // 获取为该模块选择的属性和标签
            const selectedAttributes = this.moduleAttributes[key] || [];
            const selectedTags = this.moduleTags[key] || [];

            console.log(`处理组合：${key}, 选择的属性数量: ${selectedAttributes.length}, 标签数量: ${selectedTags.length}`);

            // 获取已有的商品属性类别关系
            const existingAttributeTypes = attributeTypeRefMap[moduleRefId] || [];
            const existingAttributeTypeIds = existingAttributeTypes.map(item => item.attributeTypeId);

            // 获取已有的商品标签关系
            const existingTags = tagRefMap[moduleRefId] || [];
            const existingTagIds = existingTags.map(item => item.tagId);

            console.log(`已有属性数量: ${existingAttributeTypeIds.length}, 标签数量: ${existingTagIds.length}`);

            // 只为尚未关联的商品属性类别创建新的关联关系
            for (const attributeTypeId of selectedAttributes) {
              if (!existingAttributeTypeIds.includes(attributeTypeId)) {
                moduleAttributeTypeRefs.push({
                  cityCategoryModuleRefId: moduleRefId,
                  goodsAttributeTypeId: attributeTypeId,
                  status: 1, // 有效状态
                  sort: 1 // 默认排序
                });
              }
            }

            // 只为尚未关联的商品标签创建新的关联关系
            for (const tagId of selectedTags) {
              if (!existingTagIds.includes(tagId)) {
                moduleTagRefs.push({
                  goodsTagId: tagId,
                  cityCategoryModuleRefId: moduleRefId,
                  status: 1, // 有效状态
                  sort: 1 // 默认排序
                });
              }
            }
          }
        }
      }

      // 并行批量保存新增的关系
      const savePromises = [];

      if (moduleAttributeTypeRefs.length > 0) {
        console.log(`准备保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);
        savePromises.push(
          batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs)
            .then(result => console.log('保存模块-商品属性类别关系结果:', result))
        );
      }

      if (moduleTagRefs.length > 0) {
        console.log(`准备保存${moduleTagRefs.length}个模块-商品标签关系`);
        savePromises.push(
          batchSaveModuleTagRef(moduleTagRefs)
            .then(result => console.log('保存模块-商品标签关系结果:', result))
        );
      }

      await Promise.all(savePromises);
      console.log(`总共处理了${processedGroups.size}个城市-分类-模块组合`);
    },

    // 重置表单
    resetForm() {
      this.currentStep = 1;
      this.selectedCities = [];
      this.selectAllCities = false;
      this.isIndeterminate = false;
      this.selectedCategories = [];
      this.selectedModules = [];
      this.attributeTagTab = 'attribute';
      this.selectedAttributeTypes = [];
      this.selectedTags = [];
      this.cityCategoryRefMap = {};
      this.cityCategoryModuleRefMap = {};
      this.categoryRefIdMap = {};
      this.moduleRefIdMap = {};
      this.attributeTypeRefIdMap = {};
      this.tagRefIdMap = {};
      this.cityCategories = {};
      this.cityModules = {};
      this.attributeTagTabs = {};
      this.moduleAttributes = {};
      this.moduleTags = {};
      this.expandedCities = [];
      this.expandedCityCategories = [];
      this.expandedCityModules = [];
    },

    // 加载地市-模块分类关系
    async loadCategoryRelations() {
      this.categoryRefIdMap = {}; // 重置映射

      // 首先清空所有地市的分类数据
      for (const cityCode of this.selectedCities) {
        if (!this.cityCategories[cityCode]) {
          this.$set(this.cityCategories, cityCode, []);
        } else {
          this.cityCategories[cityCode] = [];
        }
      }

      try {
        // 使用优化的批量加载器 - 一次性获取所有地市的分类关系
        const cityCategories = await configRelationDataLoader.batchLoadCityCategories(this.selectedCities);

        // 处理返回的数据
        for (const cityCode of this.selectedCities) {
          const cityName = this.getCityName(cityCode);
          const categoryRefs = cityCategories[cityCode] || [];
          console.log(`加载地市 ${cityName}(${cityCode}) 的分类关系，获取到 ${categoryRefs.length} 条数据`);

          // 将已有关联关系存入映射中
          for (const ref of categoryRefs) {
            const key = `${cityCode}_${ref.categoryId}`;
            this.cityCategoryRefMap[key] = ref.refId;

            // 直接将分类ID添加到对应地市的分类列表中
            if (!this.cityCategories[cityCode].includes(ref.categoryId)) {
              this.cityCategories[cityCode].push(ref.categoryId);
            }

            // 添加到ID映射表，使用cityCode+categoryId作为唯一键
            const mapKey = `${cityCode}_${ref.categoryId}`;
            this.categoryRefIdMap[mapKey] = {
              refId: ref.refId,
              cityCode: cityCode,
              cityName: cityName,
              categoryId: ref.categoryId,
              categoryName: ref.categoryName
            };
          }
        }
      } catch (error) {
        console.error('批量加载地市分类关系失败:', error);
        throw error;
      }

      // 打印每个地市的分类数据，用于调试
      for (const cityCode of this.selectedCities) {
        console.log(`地市 ${this.getCityName(cityCode)}(${cityCode}) 的分类数量: ${this.cityCategories[cityCode].length}`);
      }
    },

    // 加载地市分类-模块关系
    async loadModuleRelations() {
      this.moduleRefIdMap = {}; // 重置映射

      // 清空所有组合的模块数据
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const key = `${cityCode}-${categoryId}`;
          if (!this.cityModules[key]) {
            this.$set(this.cityModules, key, []);
          } else {
            this.cityModules[key] = [];
          }
        }
      }

      try {
        // 收集所有需要查询的地市分类关系ID
        const cityCategoryRefIds = [];
        const refIdToInfoMap = {}; // 关系ID到地市分类信息的映射

        for (const cityCode of this.selectedCities) {
          for (const categoryId of this.cityCategories[cityCode] || []) {
            const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];
            if (refId) {
              cityCategoryRefIds.push(refId);
              refIdToInfoMap[refId] = {
                cityCode,
                categoryId,
                cityName: this.getCityName(cityCode),
                categoryName: this.getCategoryName(categoryId),
                cityCategoryKey: `${cityCode}-${categoryId}`
              };
            }
          }
        }

        if (cityCategoryRefIds.length > 0) {
          // 使用优化的批量加载器 - 一次性获取所有模块关系
          const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds);

          // 处理返回的数据
          for (const refId of cityCategoryRefIds) {
            const moduleRefs = categoryModules[refId] || [];
            const info = refIdToInfoMap[refId];

            console.log(`加载组合 ${info.cityName}(${info.cityCode})-${info.categoryName}(${info.categoryId}) 的模块关系，获取到 ${moduleRefs.length} 条数据`);

            // 添加到ID映射表，并更新该组合的模块列表
            for (const moduleRef of moduleRefs) {
              const key = `${refId}_${moduleRef.moduleId}`;
              this.cityCategoryModuleRefMap[key] = moduleRef.refId;

              // 直接将模块ID添加到对应地市分类组合的模块列表中
              if (!this.cityModules[info.cityCategoryKey].includes(moduleRef.moduleId)) {
                this.cityModules[info.cityCategoryKey].push(moduleRef.moduleId);
              }

              // 使用完整的组合键来确保不同地市分类组合下的同一模块被分别处理
              const mapKey = `${info.cityCode}_${info.categoryId}_${moduleRef.moduleId}`;
              this.moduleRefIdMap[mapKey] = {
                refId: moduleRef.refId,
                cityCode: info.cityCode,
                cityName: info.cityName,
                categoryId: info.categoryId,
                categoryName: info.categoryName,
                moduleId: moduleRef.moduleId,
                moduleName: moduleRef.moduleName
              };
            }
          }
        }
      } catch (error) {
        console.error('批量加载模块关系失败:', error);
        throw error;
      }

      // 打印每个组合的模块数据，用于调试
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const key = `${cityCode}-${categoryId}`;
          console.log(`组合 ${this.getCityName(cityCode)}(${cityCode})-${this.getCategoryName(categoryId)}(${categoryId}) 的模块数量: ${(this.cityModules[key] || []).length}`);
        }
      }
    },

    // 加载模块-商品属性类别和标签关系
    async loadAttributeAndTagRelations() {
      this.attributeTypeRefIdMap = {}; // 重置映射
      this.tagRefIdMap = {}; // 重置映射

      // 清空所有组合的属性和标签数据
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const cityCategoryKey = `${cityCode}-${categoryId}`;

          for (const moduleId of this.cityModules[cityCategoryKey] || []) {
            const key = `${cityCode}-${categoryId}-${moduleId}`;

            // 初始化该组合的属性和标签列表
            if (!this.moduleAttributes[key]) {
              this.$set(this.moduleAttributes, key, []);
            } else {
              this.moduleAttributes[key] = [];
            }

            if (!this.moduleTags[key]) {
              this.$set(this.moduleTags, key, []);
            } else {
              this.moduleTags[key] = [];
            }

            if (!this.attributeTagTabs[key]) {
              this.$set(this.attributeTagTabs, key, 'attribute');
            }
          }
        }
      }

      // 为每个地市-分类-模块组合单独获取属性和标签关系
      const loadPromises = [];

      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const cityCategoryKey = `${cityCode}-${categoryId}`;
          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];

          if (!cityCategoryRefId) {
            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);
            continue;
          }

          for (const moduleId of this.cityModules[cityCategoryKey] || []) {
            const moduleKey = `${cityCode}-${categoryId}-${moduleId}`;
            const moduleName = this.getModuleName(moduleId);
            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];

            if (moduleRefId) {
              loadPromises.push((async () => {
                // 加载商品属性类别关系
                const attributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);
                console.log(`加载组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${moduleName} 的属性关系，获取到 ${attributeTypes.length} 条数据`);

                for (const attrType of attributeTypes) {
                  // 将属性ID添加到该组合的属性列表中
                  if (!this.moduleAttributes[moduleKey].includes(attrType.attributeTypeId)) {
                    this.moduleAttributes[moduleKey].push(attrType.attributeTypeId);
                  }

                  // 添加到ID映射表，使用组合键确保不同组合下的同一属性被分别处理
                  const mapKey = `${cityCode}_${categoryId}_${moduleId}_${attrType.attributeTypeId}`;
                  this.attributeTypeRefIdMap[mapKey] = {
                    refId: attrType.refId,
                    moduleId: moduleId,
                    moduleName: moduleName,
                    attributeTypeId: attrType.attributeTypeId,
                    attributeTypeName: attrType.attributeTypeName,
                    groupKey: moduleKey
                  };
                }

                // 加载商品标签关系
                const tags = await getTagsByModuleRefId(moduleRefId);
                console.log(`加载组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${moduleName} 的标签关系，获取到 ${tags.length} 条数据`);

                for (const tag of tags) {
                  // 将标签ID添加到该组合的标签列表中
                  if (!this.moduleTags[moduleKey].includes(tag.tagId)) {
                    this.moduleTags[moduleKey].push(tag.tagId);
                  }

                  // 添加到ID映射表，使用组合键确保不同组合下的同一标签被分别处理
                  const mapKey = `${cityCode}_${categoryId}_${moduleId}_${tag.tagId}`;
                  this.tagRefIdMap[mapKey] = {
                    refId: tag.refId,
                    moduleId: moduleId,
                    moduleName: moduleName,
                    tagId: tag.tagId,
                    tagName: tag.tagName,
                    groupKey: moduleKey
                  };
                }
              })());
            }
          }
        }
      }

      await Promise.all(loadPromises);

      // 打印每个组合的属性和标签数据，用于调试
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const cityCategoryKey = `${cityCode}-${categoryId}`;

          for (const moduleId of this.cityModules[cityCategoryKey] || []) {
            const key = `${cityCode}-${categoryId}-${moduleId}`;
            console.log(`组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${this.getModuleName(moduleId)} 的属性数量: ${(this.moduleAttributes[key] || []).length}, 标签数量: ${(this.moduleTags[key] || []).length}`);
          }
        }
      }
    },

    // 处理移除模块分类
    async handleRemoveCategories(cityCode, value, direction, movedKeys) {
      if (direction === 'left') {
        return; // 只处理从右向左移除（删除）的情况
      }

      // 获取被移除的分类ID
      const removedCategoryIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];

      if (removedCategoryIds.length === 0) {
        return;
      }

      try {
        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });

        // 收集需要删除的关系ID
        const toDeleteRefIds = [];

        for (const categoryId of removedCategoryIds) {
          // 查找该分类在当前地市下的关系ID
          const key = `${cityCode}_${categoryId}`;
          const refId = this.cityCategoryRefMap[key];

          if (refId) {
            toDeleteRefIds.push(refId);

            // 从映射中移除
            delete this.cityCategoryRefMap[key];

            // 从地市分类列表中移除
            const index = this.cityCategories[cityCode].indexOf(categoryId);
            if (index !== -1) {
              this.cityCategories[cityCode].splice(index, 1);
            }
          }
        }

        // 批量删除关系
        if (toDeleteRefIds.length > 0) {
          await batchDeleteCityCategoryRef(toDeleteRefIds);
        }

        // 同步到selectedCategories
        this.syncAllCategories();

        loading.close();
        this.$message.success(`已删除${toDeleteRefIds.length}个模块分类的关联关系`);
      } catch (error) {
        this.$message.error(error.message || '删除关联关系失败');
      }
    },

    // 处理移除模块
    async handleRemoveModules(cityCode, categoryId, value, direction, movedKeys) {
      if (direction === 'left') {
        return; // 只处理从右向左移除（删除）的情况
      }

      // 获取被移除的模块ID
      const removedModuleIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];

      if (removedModuleIds.length === 0) {
        return;
      }

      try {
        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });

        // 收集需要删除的关系ID
        const toDeleteRefIds = [];

        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];
        if (cityCategoryRefId) {
          for (const moduleId of removedModuleIds) {
            const key = `${cityCategoryRefId}_${moduleId}`;
            const refId = this.cityCategoryModuleRefMap[key];

            if (refId) {
              toDeleteRefIds.push(refId);

              // 从映射中移除
              delete this.cityCategoryModuleRefMap[key];

              // 从地市-分类-模块列表中移除
              const cityCategoryKey = `${cityCode}-${categoryId}`;
              const index = this.cityModules[cityCategoryKey].indexOf(moduleId);
              if (index !== -1) {
                this.cityModules[cityCategoryKey].splice(index, 1);
              }
            }
          }
        }

        // 批量删除关系
        if (toDeleteRefIds.length > 0) {
          await batchDeleteCityCategoryModuleRef(toDeleteRefIds);
        }

        // 同步到selectedModules
        this.syncAllModules();

        loading.close();
        this.$message.success(`已删除${toDeleteRefIds.length}个模块的关联关系`);
      } catch (error) {
        this.$message.error(error.message || '删除关联关系失败');
      }
    },

    // 处理移除商品属性类别
    async handleRemoveAttributeTypes(cityCode, categoryId, moduleId, value, direction, movedKeys) {
      if (direction === 'left') {
        return; // 只处理从右向左移除（删除）的情况
      }

      // 获取被移除的属性类别ID
      const removedAttributeTypeIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];

      if (removedAttributeTypeIds.length === 0) {
        return;
      }

      try {
        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });

        // 收集需要删除的关系ID
        const toDeleteRefIds = [];

        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];
        if (cityCategoryRefId) {
          const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];
          if (moduleRefId) {
            // 获取当前模块的属性类别关系
            const existingAttributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);

            for (const attributeTypeId of removedAttributeTypeIds) {
              // 在现有关系中查找对应的refId
              const attrType = existingAttributeTypes.find(item => item.attributeTypeId === attributeTypeId);
              if (attrType && attrType.refId) {
                toDeleteRefIds.push(attrType.refId);

                // 从模块-属性列表中移除
                const key = `${cityCode}-${categoryId}-${moduleId}`;
                const index = this.moduleAttributes[key].indexOf(attributeTypeId);
                if (index !== -1) {
                  this.moduleAttributes[key].splice(index, 1);
                }
              }
            }
          }
        }

        // 批量删除关系
        if (toDeleteRefIds.length > 0) {
          await batchDeleteModuleAttributeTypeRef(toDeleteRefIds);
        }

        // 同步到selectedAttributeTypes
        this.syncAllAttributesAndTags();

        loading.close();
        this.$message.success(`已删除${toDeleteRefIds.length}个商品属性类别的关联关系`);
      } catch (error) {
        this.$message.error(error.message || '删除关联关系失败');
      }
    },

    // 处理移除商品标签
    async handleRemoveTags(cityCode, categoryId, moduleId, value, direction, movedKeys) {
      if (direction === 'left') {
        return; // 只处理从右向左移除（删除）的情况
      }

      // 获取被移除的标签ID
      const removedTagIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];

      if (removedTagIds.length === 0) {
        return;
      }

      try {
        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });

        // 收集需要删除的关系ID
        const toDeleteRefIds = [];

        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];
        if (cityCategoryRefId) {
          const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];
          if (moduleRefId) {
            // 获取当前模块的标签关系
            const existingTags = await getTagsByModuleRefId(moduleRefId);

            for (const tagId of removedTagIds) {
              // 在现有关系中查找对应的refId
              const tag = existingTags.find(item => item.tagId === tagId);
              if (tag && tag.refId) {
                toDeleteRefIds.push(tag.refId);

                // 从模块-标签列表中移除
                const key = `${cityCode}-${categoryId}-${moduleId}`;
                const index = this.moduleTags[key].indexOf(tagId);
                if (index !== -1) {
                  this.moduleTags[key].splice(index, 1);
                }
              }
            }
          }
        }

        // 批量删除关系
        if (toDeleteRefIds.length > 0) {
          await batchDeleteModuleTagRef(toDeleteRefIds);
        }

        // 同步到selectedTags
        this.syncAllAttributesAndTags();

        loading.close();
        this.$message.success(`已删除${toDeleteRefIds.length}个商品标签的关联关系`);
      } catch (error) {
        this.$message.error(error.message || '删除关联关系失败');
      }
    },

    // 处理地市下的模块分类变化
    handleCityCategoriesChange(cityCode, value) {
      // 更新对应地市的模块分类
      this.$set(this.cityCategories, cityCode, value);

      // 展开刚刚选择的地市
      if (value.length > 0 && !this.expandedCities.includes(cityCode)) {
        this.expandedCities.push(cityCode);
      }
    },

    // 处理地市和分类下的模块变化
    handleCityModulesChange(cityCode, categoryId, value) {
      const key = `${cityCode}-${categoryId}`;

      // 更新对应地市和分类的模块
      this.$set(this.cityModules, key, value);

      // 展开刚刚选择的地市-分类
      if (value.length > 0 && !this.expandedCityCategories.includes(key)) {
        this.expandedCityCategories.push(key);
      }
    },

    // 处理模块下的商品属性类别变化
    handleModuleAttributesChange(cityCode, categoryId, moduleId, value) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;

      // 更新对应模块的商品属性类别
      this.$set(this.moduleAttributes, key, value);

      // 展开刚刚选择的地市-分类-模块
      if (value.length > 0 && !this.expandedCityModules.includes(key)) {
        this.expandedCityModules.push(key);
      }
    },

    // 处理模块下的商品标签变化
    handleModuleTagsChange(cityCode, categoryId, moduleId, value) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      
      // 更新对应模块的商品标签
      this.$set(this.moduleTags, key, value);
     console.log(this.expandedCityModules,'=this.expandedCityModules');
     
      // 展开刚刚选择的地市-分类-模块
      if (value.length > 0 && !this.expandedCityModules.includes(key)) {
        this.expandedCityModules.push(key);
      }
 
    },

    // 同步所有模块分类到selectedCategories
    syncAllCategories() {
      // 重置selectedCategories
      this.selectedCategories = [];

      // 收集所有地市下的模块分类
      for (const cityCode in this.cityCategories) {
        const categories = this.cityCategories[cityCode] || [];

        // 将未添加的分类添加到总分类列表中
        for (const categoryId of categories) {
          if (!this.selectedCategories.includes(categoryId)) {
            this.selectedCategories.push(categoryId);
          }
        }
      }
    },

    // 同步所有模块到selectedModules
    syncAllModules() {
      // 重置selectedModules
      this.selectedModules = [];

      // 收集所有地市-分类下的模块
      for (const key in this.cityModules) {
        const modules = this.cityModules[key] || [];

        // 将未添加的模块添加到总模块列表中
        for (const moduleId of modules) {
          if (!this.selectedModules.includes(moduleId)) {
            this.selectedModules.push(moduleId);
          }
        }
      }
    },

    // 同步所有属性类别和标签
    syncAllAttributesAndTags() {
      // 清空选中的属性和标签
      this.selectedAttributeTypes = [];
      this.selectedTags = [];

      // 收集所有组合中已选的属性和标签，避免重复
      const attributeSet = new Set();
      const tagSet = new Set();

      // 遍历所有模块组合
      for (const cityCode of this.selectedCities) {
        for (const categoryId of this.cityCategories[cityCode] || []) {
          const cityCategoryKey = `${cityCode}-${categoryId}`;

          for (const moduleId of this.cityModules[cityCategoryKey] || []) {
            const moduleKey = `${cityCode}-${categoryId}-${moduleId}`;

            // 添加该组合下的属性
            for (const attributeTypeId of this.moduleAttributes[moduleKey] || []) {
              attributeSet.add(attributeTypeId);
            }

            // 添加该组合下的标签
            for (const tagId of this.moduleTags[moduleKey] || []) {
              tagSet.add(tagId);
            }
          }
        }
      }

      // 转换为数组
      this.selectedAttributeTypes = Array.from(attributeSet);
      this.selectedTags = Array.from(tagSet);

      console.log('同步后的全局属性数量:', this.selectedAttributeTypes.length);
      console.log('同步后的全局标签数量:', this.selectedTags.length);
    },

    // 获取地市下选中的模块分类数量
    getCityCategoryCount(cityCode) {
      return (this.cityCategories[cityCode] || []).length;
    },

    // 获取地市和分类下选中的模块数量
    getCityCategoryModuleCount(cityCode, categoryId) {
      const key = `${cityCode}-${categoryId}`;
      return (this.cityModules[key] || []).length;
    },

    // 获取模块下选中的商品属性类别数量
    getModuleAttributeCount(cityCode, categoryId, moduleId) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      return (this.moduleAttributes[key] || []).length;
    },

    // 获取模块下选中的商品标签数量
    getModuleTagCount(cityCode, categoryId, moduleId) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      return (this.moduleTags[key] || []).length;
    },

    // 获取地市下的模块分类数组
    getCityModulesArray(cityCode, categoryId) {
      const key = `${cityCode}-${categoryId}`;
      return this.cityModules[key] || [];
    },

    // 获取模块的标签页值
    getModuleTabsValue(cityCode, categoryId, moduleId) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      return this.attributeTagTabs[key] || 'attribute';
    },

    // 获取模块的商品属性类别数组
    getModuleAttributesArray(cityCode, categoryId, moduleId) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      return this.moduleAttributes[key] || [];
    },

    // 获取模块的商品标签数组
    getModuleTagsArray(cityCode, categoryId, moduleId) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      return this.moduleTags[key] || [];
    },

    // 批量更新某个地市的模块分类选择
    updateCityCategories(cityCode, selectedCategories) {
      // 更新数据
      this.$set(this.cityCategories, cityCode, selectedCategories);

      // 同步到全局的selectedCategories
      this.syncAllCategories();
    },

    // 更新地市+分类的模块
    updateCityModules(cityCode, categoryId, value) {
      const key = `${cityCode}-${categoryId}`;
      this.$set(this.cityModules, key, value);
      this.handleCityModulesChange(cityCode, categoryId, value);
    },

    // 更新模块的标签页
    updateModuleTab(cityCode, categoryId, moduleId, value) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      this.$set(this.attributeTagTabs, key, value);
    },

    // 更新模块的商品属性类别
    updateModuleAttributes(cityCode, categoryId, moduleId, value) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      this.$set(this.moduleAttributes, key, value);
      this.handleModuleAttributesChange(cityCode, categoryId, moduleId, value);
    },

    // 更新模块的商品标签
    async updateModuleTags(cityCode, categoryId, moduleId, value) {
      const key = `${cityCode}-${categoryId}-${moduleId}`;
      // 使用Vue.set确保响应式更新
      this.$set(this.moduleTags, key, [...value]);
      // this.$set(this.moduleTags, key, value);



      // 触发后续处理
      this.handleModuleTagsChange(cityCode, categoryId, moduleId, value);
    },

    // 保存属性类别排序
    async saveAttributeSort(cityCode, categoryId, moduleId, sortedAttributeIds) {
      try {
        // 获取模块关联ID
        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];
        if (!cityCategoryRefId) {
          console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID`);
          return;
        }

        const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];
        if (!moduleRefId) {
          console.warn(`未找到模块${moduleId}的关联ID`);
          return;
        }

        // 获取当前的属性关联关系
        const attributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);

        // 构建排序更新数据
        const updateList = [];
        sortedAttributeIds.forEach((attributeTypeId, index) => {
          const existingRef = attributeTypes.find(attr => attr.attributeTypeId === attributeTypeId);
          if (existingRef) {
            updateList.push({
              id: existingRef.refId,
              sort: index + 1 // 排序从1开始
            });
          }
        });

        if (updateList.length > 0) {
          await batchUpdateRankSx(updateList);
          console.log(`属性排序保存成功，更新了${updateList.length}条记录`);
        }
      } catch (error) {
        console.error('保存属性排序失败:', error);
        this.$message.error('保存属性排序失败');
      }
    },

    // 保存标签排序
    async saveTagSort(cityCode, categoryId, moduleId, sortedTagIds) {
      try {
        // 获取模块关联ID
        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];
        if (!cityCategoryRefId) {
          console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID`);
          return;
        }

        const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];
        if (!moduleRefId) {
          console.warn(`未找到模块${moduleId}的关联ID`);
          return;
        }

        // 获取当前的标签关联关系
        const tags = await getTagsByModuleRefId(moduleRefId);

        // 构建排序更新数据
        const updateList = [];
        sortedTagIds.forEach((tagId, index) => {
          const existingRef = tags.find(tag => tag.tagId === tagId);
          if (existingRef) {
            updateList.push({
              id: existingRef.refId,
              sort: index + 1 // 排序从1开始
            });
          }
        });

        if (updateList.length > 0) {
          await batchUpdateRankSx(updateList);
          console.log(`标签排序保存成功，更新了${updateList.length}条记录`);
        }
      } catch (error) {
        console.error('保存标签排序失败:', error);
        this.$message.error('保存标签排序失败');
      }
    },
  }
};
</script>

<style scoped>
.config-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.config-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.config-desc {
  color: #909399;
  font-size: 14px;
}

.config-operation {
  margin-bottom: 30px;
}

.config-content {
  min-height: 400px;
}

.step-content {
  padding: 20px 0;
}

.step-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

.step-actions {
  margin-top: 30px;
  text-align: right;
}

.city-selection, .category-selection, .module-selection, .attribute-tag-selection {
  margin-top: 20px;
}

.city-list {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
}

.city-item {
  margin-right: 15px;
  margin-bottom: 10px;
}

.selected-cities-info, .selected-cities-categories-info, .selected-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.info-row {
  margin-bottom: 10px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: bold;
  margin-right: 10px;
}

.city-tag, .category-tag, .module-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.category-transfer, .module-transfer, .attribute-transfer, .tag-transfer {
  margin-top: 15px;
}

.transfer-item {
  display: block;
  padding: 5px 0;
}

.attribute-tag-tabs {
  margin-top: 15px;
}

.existing-relations {
  margin-bottom: 20px;
}

.existing-relations h4 {
  margin-bottom: 10px;
}


.city-groups {
  margin-bottom: 20px;
}

.city-groups .el-collapse-item {
  margin-bottom: 10px;
}

.city-groups .el-collapse-item__header {
  padding: 10px;
  background-color: #f8f8f8;
  border-bottom: none;
}

.city-groups .el-collapse-item__header .group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.city-groups .el-collapse-item__header .group-title {
  font-weight: bold;
}

.city-groups .el-collapse-item__header .count-tag {
  margin-left: 10px;
}

.city-category-groups {
  margin-bottom: 20px;
}

.city-category-groups .el-collapse-item {
  margin-bottom: 10px;
}

.city-category-groups .el-collapse-item__header {
  padding: 10px;
  background-color: #f8f8f8;
  border-bottom: none;
}

.city-category-groups .el-collapse-item__header .group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.city-category-groups .el-collapse-item__header .group-title {
  font-weight: bold;
}

.city-category-groups .el-collapse-item__header .count-tag {
  margin-left: 10px;
}

.city-category-module-groups {
  margin-bottom: 20px;
}

.city-category-module-groups .el-collapse-item {
  margin-bottom: 10px;
}

.city-category-module-groups .el-collapse-item__header {
  padding: 10px;
  background-color: #f8f8f8;
  border-bottom: none;
}

.city-category-module-groups .el-collapse-item__header .group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.city-category-module-groups .el-collapse-item__header .group-title {
  font-weight: bold;
}

.city-category-module-groups .el-collapse-item__header .count-tag {
  margin-left: 10px;
}

::v-deep .el-transfer-panel{
  width:35%;
}
::v-deep .el-transfer__buttons{
  width: 28%;
  padding: 0;
  text-align: center;
}

</style> 