{"ast": null, "code": "import \"core-js/modules/es.json.stringify.js\";\nimport { getOrderStatusMap, getPaymentStatusMap } from '@/api/hnzsxH5/order';\nexport default {\n  name: 'OrderSearch',\n\n  data() {\n    return {\n      // 查询表单\n      form: {\n        orderNo: '',\n        userName: '',\n        userCard: '',\n        userMoble: '',\n        custOrderId: '',\n        goodsName: '',\n        cityCode: '',\n        state: null,\n        paymentStatus: null,\n        failReason: '',\n        stateChangeReason: ''\n      },\n      // 日期范围选择\n      dateRange: [],\n      // 订单状态映射\n      statusMap: getOrderStatusMap(),\n      // 支付状态映射\n      paymentStatusMap: {\n        1: {\n          text: '支付成功'\n        },\n        2: {\n          text: '支付失败'\n        },\n        3: {\n          text: '费用0，无需支付'\n        }\n      },\n      // 地市选项\n      cityOptions: [{\n        value: '730',\n        label: '岳阳'\n      }, {\n        value: '731',\n        label: '长沙'\n      }, {\n        value: '732',\n        label: '湘潭'\n      }, {\n        value: '733',\n        label: '株洲'\n      }, {\n        value: '734',\n        label: '衡阳'\n      }, {\n        value: '735',\n        label: '郴州'\n      }, {\n        value: '736',\n        label: '常德'\n      }, {\n        value: '737',\n        label: '益阳'\n      }, {\n        value: '738',\n        label: '娄底'\n      }, {\n        value: '739',\n        label: '邵阳'\n      }, {\n        value: '743',\n        label: '湘西'\n      }, {\n        value: '744',\n        label: '张家界'\n      }, {\n        value: '745',\n        label: '怀化'\n      }, {\n        value: '746',\n        label: '永州'\n      }]\n    };\n  },\n\n  methods: {\n    // 搜索按钮点击事件\n    search() {\n      // 处理日期范围\n      const searchForm = { ...this.form\n      }; // 确保状态值为数字类型\n\n      if (searchForm.state !== null && searchForm.state !== undefined && searchForm.state !== '') {\n        searchForm.state = Number(searchForm.state);\n      } // 确保支付状态为数字类型\n\n\n      if (searchForm.paymentStatus !== null && searchForm.paymentStatus !== undefined && searchForm.paymentStatus !== '') {\n        searchForm.paymentStatus = Number(searchForm.paymentStatus);\n      }\n\n      if (this.dateRange && this.dateRange.length === 2) {\n        searchForm.createdDateStart = this.dateRange[0] + ' 00:00:00';\n        searchForm.createdDateEnd = this.dateRange[1] + ' 23:59:59';\n      } // 移除可能的空字符串，避免后端处理问题\n\n\n      Object.keys(searchForm).forEach(key => {\n        if (searchForm[key] === '') {\n          searchForm[key] = null;\n        }\n      });\n      console.log('发送搜索条件:', JSON.stringify(searchForm));\n      this.$emit('search', searchForm);\n    },\n\n    // 重置按钮点击事件\n    reset() {\n      // 先清空表单数据\n      this.form = {\n        orderNo: '',\n        userName: '',\n        userCard: '',\n        userMoble: '',\n        custOrderId: '',\n        goodsName: '',\n        cityCode: '',\n        state: null,\n        paymentStatus: null,\n        failReason: '',\n        stateChangeReason: ''\n      };\n      this.dateRange = []; // 然后重置表单，确保UI也刷新\n\n      this.$nextTick(() => {\n        this.$refs.form.resetFields(); // 发送空查询条件，触发刷新\n\n        console.log('重置查询条件');\n        this.$emit('search', {});\n      });\n    },\n\n    // 获取当前查询参数，供外部组件调用\n    getParams() {\n      const searchForm = { ...this.form\n      }; // 确保状态值为数字类型\n\n      if (searchForm.state !== null && searchForm.state !== undefined && searchForm.state !== '') {\n        searchForm.state = Number(searchForm.state);\n      } // 确保支付状态为数字类型\n\n\n      if (searchForm.paymentStatus !== null && searchForm.paymentStatus !== undefined && searchForm.paymentStatus !== '') {\n        searchForm.paymentStatus = Number(searchForm.paymentStatus);\n      }\n\n      if (this.dateRange && this.dateRange.length === 2) {\n        searchForm.createdDateStart = this.dateRange[0] + ' 00:00:00';\n        searchForm.createdDateEnd = this.dateRange[1] + ' 23:59:59';\n      } // 移除可能的空字符串，避免后端处理问题\n\n\n      Object.keys(searchForm).forEach(key => {\n        if (searchForm[key] === '' || searchForm[key] === null || searchForm[key] === undefined) {\n          delete searchForm[key];\n        }\n      });\n      return searchForm;\n    }\n\n  }\n};", "map": {"version": 3, "mappings": ";AAyJA;AAEA;EACAA,mBADA;;EAEAC;IACA;MACA;MACAC;QACAC,WADA;QAEAC,YAFA;QAGAC,YAHA;QAIAC,aAJA;QAKAC,eALA;QAMAC,aANA;QAOAC,YAPA;QAQAC,WARA;QASAC,mBATA;QAUAC,cAVA;QAWAC;MAXA,CAFA;MAeA;MACAC,aAhBA;MAiBA;MACAC,8BAlBA;MAmBA;MACAC;QACA;UAAAC;QAAA,CADA;QAEA;UAAAA;QAAA,CAFA;QAGA;UAAAA;QAAA;MAHA,CApBA;MA0BA;MACAC,cACA;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAD;QAAAC;MAAA,CAFA,EAGA;QAAAD;QAAAC;MAAA,CAHA,EAIA;QAAAD;QAAAC;MAAA,CAJA,EAKA;QAAAD;QAAAC;MAAA,CALA,EAMA;QAAAD;QAAAC;MAAA,CANA,EAOA;QAAAD;QAAAC;MAAA,CAPA,EAQA;QAAAD;QAAAC;MAAA,CARA,EASA;QAAAD;QAAAC;MAAA,CATA,EAUA;QAAAD;QAAAC;MAAA,CAVA,EAWA;QAAAD;QAAAC;MAAA,CAXA,EAYA;QAAAD;QAAAC;MAAA,CAZA,EAaA;QAAAD;QAAAC;MAAA,CAbA,EAcA;QAAAD;QAAAC;MAAA,CAdA;IA3BA;EA4CA,CA/CA;;EAgDAC;IACA;IACAC;MACA;MACA;MAAA,EAFA,CAIA;;MACA;QACAC;MACA,CAPA,CASA;;;MACA;QACAA;MACA;;MAEA;QACAA;QACAA;MACA,CAjBA,CAmBA;;;MACAC;QACA;UACAD;QACA;MACA,CAJA;MAMAE;MACA;IACA,CA9BA;;IA+BA;IACAC;MACA;MACA;QACAvB,WADA;QAEAC,YAFA;QAGAC,YAHA;QAIAC,aAJA;QAKAC,eALA;QAMAC,aANA;QAOAC,YAPA;QAQAC,WARA;QASAC,mBATA;QAUAC,cAVA;QAWAC;MAXA;MAaA,oBAfA,CAiBA;;MACA;QACA,8BADA,CAGA;;QACAY;QACA;MACA,CANA;IAOA,CAzDA;;IA0DA;IACAE;MACA;MAAA,EADA,CAGA;;MACA;QACAJ;MACA,CANA,CAQA;;;MACA;QACAA;MACA;;MAEA;QACAA;QACAA;MACA,CAhBA,CAkBA;;;MACAC;QACA;UACA;QACA;MACA,CAJA;MAMA;IACA;;EArFA;AAhDA", "names": ["name", "data", "form", "orderNo", "userName", "userCard", "userMoble", "custOrderId", "goodsName", "cityCode", "state", "paymentStatus", "failReason", "stateChangeReason", "date<PERSON><PERSON><PERSON>", "statusMap", "paymentStatusMap", "text", "cityOptions", "value", "label", "methods", "search", "searchForm", "Object", "console", "reset", "getParams"], "sourceRoot": "src/views/hnzsxH5/order/components", "sources": ["search.vue"], "sourcesContent": ["<!-- 订单管理搜索 -->\r\n<template>\r\n  <div class=\"search-container\">\r\n    <el-form ref=\"form\" :model=\"form\" class=\"ele-form-search\" size=\"small\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"订单号:\">\r\n            <el-input\r\n              v-model=\"form.orderNo\"\r\n              placeholder=\"请输入订单号\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"87单号:\">\r\n            <el-input\r\n              v-model=\"form.custOrderId\"\r\n              placeholder=\"请输入87单号\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"用户姓名:\">\r\n            <el-input\r\n              v-model=\"form.userName\"\r\n              placeholder=\"请输入用户姓名\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"手机号码:\">\r\n            <el-input\r\n              v-model=\"form.userMoble\"\r\n              placeholder=\"请输入手机号码\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"身份证号:\">\r\n            <el-input\r\n              v-model=\"form.userCard\"\r\n              placeholder=\"请输入身份证号\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"商品名称:\">\r\n            <el-input\r\n              v-model=\"form.goodsName\"\r\n              placeholder=\"请输入商品名称\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"地市:\">\r\n            <el-select v-model=\"form.cityCode\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n              <el-option\r\n                v-for=\"item in cityOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"订单状态:\">\r\n            <el-select v-model=\"form.state\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n              <el-option\r\n                v-for=\"(val, key) in statusMap\"\r\n                :key=\"key\"\r\n                :label=\"val.text\"\r\n                :value=\"Number(key)\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n      <el-row :gutter=\"20\">\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"支付状态:\">\r\n            <el-select v-model=\"form.paymentStatus\" clearable placeholder=\"请选择\" style=\"width: 100%\">\r\n              <el-option\r\n                v-for=\"(val, key) in paymentStatusMap\"\r\n                :key=\"key\"\r\n                :label=\"val.text\"\r\n                :value=\"Number(key)\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"失败原因:\">\r\n            <el-input\r\n              v-model=\"form.failReason\"\r\n              placeholder=\"请输入失败原因关键词\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"状态变更原因:\">\r\n            <el-input\r\n              v-model=\"form.stateChangeReason\"\r\n              placeholder=\"请输入状态变更原因关键词\"\r\n              clearable\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"6\" :md=\"8\" :sm=\"12\" :xs=\"24\">\r\n          <el-form-item label=\"创建时间:\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              style=\"width: 100%\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :lg=\"12\" :md=\"8\" :sm=\"24\" :xs=\"24\">\r\n          <div class=\"search-buttons\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              @click=\"search\"\r\n            >\r\n              查询\r\n            </el-button>\r\n            <el-button\r\n              icon=\"el-icon-refresh-left\"\r\n              @click=\"reset\"\r\n            >\r\n              重置\r\n            </el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getOrderStatusMap, getPaymentStatusMap } from '@/api/hnzsxH5/order';\r\n\r\nexport default {\r\n  name: 'OrderSearch',\r\n  data() {\r\n    return {\r\n      // 查询表单\r\n      form: {\r\n        orderNo: '',\r\n        userName: '',\r\n        userCard: '',\r\n        userMoble: '',\r\n        custOrderId: '',\r\n        goodsName: '',\r\n        cityCode: '',\r\n        state: null,\r\n        paymentStatus: null,\r\n        failReason: '',\r\n        stateChangeReason: '',\r\n      },\r\n      // 日期范围选择\r\n      dateRange: [],\r\n      // 订单状态映射\r\n      statusMap: getOrderStatusMap(),\r\n      // 支付状态映射\r\n      paymentStatusMap:{\r\n        1: { text: '支付成功' },\r\n        2: { text: '支付失败' },\r\n        3: { text: '费用0，无需支付' }\r\n      },\r\n\r\n     // 地市选项\r\n     cityOptions: [\r\n        { value: '730', label: '岳阳' },\r\n        { value: '731', label: '长沙' },\r\n        { value: '732', label: '湘潭' },\r\n        { value: '733', label: '株洲' },\r\n        { value: '734', label: '衡阳' },\r\n        { value: '735', label: '郴州' },\r\n        { value: '736', label: '常德' },\r\n        { value: '737', label: '益阳' },\r\n        { value: '738', label: '娄底' },\r\n        { value: '739', label: '邵阳' },\r\n        { value: '743', label: '湘西' },\r\n        { value: '744', label: '张家界' },\r\n        { value: '745', label: '怀化' },\r\n        { value: '746', label: '永州' }\r\n      ]\r\n    };\r\n  },\r\n  methods: {\r\n    // 搜索按钮点击事件\r\n    search() {\r\n      // 处理日期范围\r\n      const searchForm = { ...this.form };\r\n      \r\n      // 确保状态值为数字类型\r\n      if (searchForm.state !== null && searchForm.state !== undefined && searchForm.state !== '') {\r\n        searchForm.state = Number(searchForm.state);\r\n      }\r\n      \r\n      // 确保支付状态为数字类型\r\n      if (searchForm.paymentStatus !== null && searchForm.paymentStatus !== undefined && searchForm.paymentStatus !== '') {\r\n        searchForm.paymentStatus = Number(searchForm.paymentStatus);\r\n      }\r\n      \r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        searchForm.createdDateStart = this.dateRange[0] + ' 00:00:00';\r\n        searchForm.createdDateEnd = this.dateRange[1] + ' 23:59:59';\r\n      }\r\n      \r\n      // 移除可能的空字符串，避免后端处理问题\r\n      Object.keys(searchForm).forEach(key => {\r\n        if (searchForm[key] === '') {\r\n          searchForm[key] = null;\r\n        }\r\n      });\r\n      \r\n      console.log('发送搜索条件:', JSON.stringify(searchForm));\r\n      this.$emit('search', searchForm);\r\n    },\r\n    // 重置按钮点击事件\r\n    reset() {\r\n      // 先清空表单数据\r\n      this.form = {\r\n        orderNo: '',\r\n        userName: '',\r\n        userCard: '',\r\n        userMoble: '',\r\n        custOrderId: '',\r\n        goodsName: '',\r\n        cityCode: '',\r\n        state: null,\r\n        paymentStatus: null,\r\n        failReason: '',\r\n        stateChangeReason: '',\r\n      };\r\n      this.dateRange = [];\r\n      \r\n      // 然后重置表单，确保UI也刷新\r\n      this.$nextTick(() => {\r\n        this.$refs.form.resetFields();\r\n        \r\n        // 发送空查询条件，触发刷新\r\n        console.log('重置查询条件');\r\n        this.$emit('search', {});\r\n      });\r\n    },\r\n    // 获取当前查询参数，供外部组件调用\r\n    getParams() {\r\n      const searchForm = { ...this.form };\r\n      \r\n      // 确保状态值为数字类型\r\n      if (searchForm.state !== null && searchForm.state !== undefined && searchForm.state !== '') {\r\n        searchForm.state = Number(searchForm.state);\r\n      }\r\n      \r\n      // 确保支付状态为数字类型\r\n      if (searchForm.paymentStatus !== null && searchForm.paymentStatus !== undefined && searchForm.paymentStatus !== '') {\r\n        searchForm.paymentStatus = Number(searchForm.paymentStatus);\r\n      }\r\n      \r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        searchForm.createdDateStart = this.dateRange[0] + ' 00:00:00';\r\n        searchForm.createdDateEnd = this.dateRange[1] + ' 23:59:59';\r\n      }\r\n      \r\n      // 移除可能的空字符串，避免后端处理问题\r\n      Object.keys(searchForm).forEach(key => {\r\n        if (searchForm[key] === '' || searchForm[key] === null || searchForm[key] === undefined) {\r\n          delete searchForm[key];\r\n        }\r\n      });\r\n      \r\n      return searchForm;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.search-container {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n  border: 1px solid #f0f0f0;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);\r\n\r\n  .ele-form-search {\r\n    .el-row {\r\n      margin-bottom: 20px;\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n      \r\n      :deep(.el-form-item__label) {\r\n        padding-right: 8px;\r\n        color: #606266;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n    \r\n    .search-buttons {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n      margin-top: 8px;\r\n      height: 32px;\r\n      \r\n      .el-button {\r\n        margin-left: 15px;\r\n        padding-left: 15px;\r\n        padding-right: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 1400px) {\r\n  .search-container {\r\n    .ele-form-search {\r\n      .el-col {\r\n        margin-bottom: 16px;\r\n        \r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> "]}, "metadata": {}, "sourceType": "module"}