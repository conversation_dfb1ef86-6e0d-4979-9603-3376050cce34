package com.hlkj.yxsAdminApi.hnzsxH5.param;

import com.hlkj.yxsAdminApi.common.core.annotation.QueryField;
import com.hlkj.yxsAdminApi.common.core.annotation.QueryType;
import com.hlkj.yxsAdminApi.common.core.web.BaseParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * H5即时受理-订单基础信息查询参数
 *
 * <AUTHOR>
 * @since 2025-04-15 17:54:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "Hnzsxh5OrderInfoParam对象", description = "H5即时受理-订单基础信息查询参数")
public class Hnzsxh5OrderInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识")
    @QueryField(type = QueryType.EQ)
    private Integer id;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户联系号码")
    private String userMoble;

    @ApiModelProperty(value = "用户身份证")
    @QueryField(type = QueryType.LIKE)
    private String userCard;

    @ApiModelProperty(value = "用户地址")
    private String userAddress;

    @ApiModelProperty(value = "地市编码")
    private String cityCode;

    @ApiModelProperty(value = "地市名称")
    private String cityName;

    @ApiModelProperty(value = "商品表中id（hnzsxh5_goods_info）")
    @QueryField(type = QueryType.EQ)
    private Integer goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品图片地址")
    private String goodsImgUrl;

    @ApiModelProperty(value = "商品价格")
    private String goodsPrice;

    @ApiModelProperty(value = "模板信息表ID集合逗号隔开（hnzsxh5_template_info）")
    private String templateInfoIdList;

    @ApiModelProperty(value = "订单类型编码")
    private String serviceOfferId;

    @ApiModelProperty(value = "订单状态(0:完结，1：暂存单，2：订单确认，3：未支付，4：已支付， -1：失败，-2：已删除 -3：生成暂存失败 -4：订单校验失败 -5 订单生单失败 -6：订单费用计算失败 -7：订单免填单生成失败 -8：订单收费确认失败)")
    @QueryField(type = QueryType.EQ)
    private Integer state;

    @ApiModelProperty(value = "创建时间")
    private String createdDate;

    @ApiModelProperty(value = "修改时间")
    private String updatedDate;

    @ApiModelProperty(value = "crm模板编码集合逗号隔开")
    private String templateIdList;

    @ApiModelProperty(value = "暂存单订单号集合逗号隔开")
    private String sceneInstIdList;

    @ApiModelProperty(value = "87单号计合逗号隔开")
    private String custOrderIdList;

    @ApiModelProperty(value = "订单总价格")
    private String totalAmount;

    @ApiModelProperty(value = "当前登陆人id（hnzsx_h5_user）")
    @QueryField(type = QueryType.EQ)
    private Integer loginUserId;

    @ApiModelProperty(value = "当前登陆人揽机工号（hnzsx_h5_user）")
    private String loginStaffCode;

    @ApiModelProperty(value = "当前登陆人受理工号（hnzsx_h5_user）")
    private String loginUserAcceptId;

    @ApiModelProperty(value = "当前登陆人销售员编码（hnzsx_h5_user）")
    private String loginUserNumber;

    @ApiModelProperty(value = "当前登陆人姓名（hnzsx_h5_user）")
    private String loginUserName;

    @ApiModelProperty(value = "当前登陆人手机号码（hnzsx_h5_user）")
    private String loginUserPhone;

    @ApiModelProperty(value = "当前登陆人店中商编码（hnzsx_h5_user）")
    private String loginSaleBoxCode;

    @ApiModelProperty(value = "当前登陆人销售点编码（hnzsx_h5_user）")
    private String loginSalesOrgCode;

    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;

    @ApiModelProperty(value = "支付状态")
    @QueryField(type = QueryType.EQ)
    private Integer paymentStatus;

    @ApiModelProperty(value = "支付流水号")
    private String paymentTransactionId;

    @ApiModelProperty(value = "支付时间")
    private String paymentTime;

    @ApiModelProperty(value = "签名状态（1.成功 2.失败）")
    @QueryField(type = QueryType.EQ)
    private Integer signatureStatus;

    @ApiModelProperty(value = "活体状态（1.成功 2.失败）")
    @QueryField(type = QueryType.EQ)
    private Integer liveStatus;

    @ApiModelProperty(value = "客户信息id")
    private String custId;

    @ApiModelProperty(value = "开票邮箱")
    private String receptInvoiceEmail;

    @ApiModelProperty(value = "开票方式")
    private String pushInvoiceWay;

    @ApiModelProperty(value = "支付状态文本")
    private String paymentStatusName;

    @ApiModelProperty(value = "创建时间开始")
    @QueryField(value = "created_date", type = QueryType.GE)
    private String createdDateStart;

    @ApiModelProperty(value = "创建时间结束")
    @QueryField(value = "created_date", type = QueryType.LE)
    private String createdDateEnd;

    @ApiModelProperty(value = "失败原因")
    @QueryField(type = QueryType.LIKE)
    private String failReason;

    @ApiModelProperty(value = "状态变更原因")
    @QueryField(type = QueryType.LIKE)
    private String stateChangeReason;

    @ApiModelProperty(value = "87单号")
    @QueryField(type = QueryType.LIKE)
    private String custOrderId;

}
