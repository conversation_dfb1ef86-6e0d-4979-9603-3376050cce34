package com.hlkj.hnzsxh5.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hlkj.hnzsxh5.exception.R;
import com.hlkj.hnzsxh5.modules.hnzsxh5_url_config.entity.HnzsxSysUrlConfigEntity;
import com.hlkj.hnzsxh5.modules.hnzsxh5_url_config.util.UrlConfigUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.hlkj.hnzsxh5.util.FileUtil.getImgBase64;
import static com.hlkj.hnzsxh5.util.StringUtil.getExchangeId;

@Component
public class InterfaceUtil {

	private static final Logger logger = LoggerFactory.getLogger(InterfaceUtil.class);

	/**
	 * 能力平台地址 - 通过UrlConfigUtil获取，保留常量是(兼容性)
	 */
	public static final String EOP_URL = "http://api.wg.qynlkfwg.bss.it.hnx.ctc.com:31008/api/rest";// 内网生产
	public static  final  String NEW_3W_SMS_URL = "http://api.wg.qynlkfwg.bss.it.hnx.ctc.com:31008/api/openapi/hndtdxtzmsg/dxtz3wnew";
	public static final String ACCESS_TOKEN = "MzMyN2YxNmExMjdjMjE0NTU2MWI0MDk5MDczZTE0ZjM=";// token令牌

	/**
	 * Dcoos秘钥 - 通过UrlConfigUtil获取，保留常量是(兼容性)
	 */
	public static final String NEW_DCOOS_URL = "http://api.wg.qynlkfwg.bss.it.hnx.ctc.com:31008/api/openapi";//
	public static final String xAppId = "d4c0ea55942b6d1c957e3a3842aadfff";
	public static final String xAppKey = "4a4d717fa94e683ce7beaa7ef9c8b5fa";

	/**
	 * 企业微信机器人
	 */
	public static final String SEND_MESSAGE_CONTENT_JSSL = "即时受理线程池监控";
	public static final String ROBOT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=49f0f96c-27c2-4615-b154-d9547a2a9f41";// 告警机器人地址

	/**
	 * BSS30-通过客户证件号码查询客户信息
	 *
	 * @param cert_num
	 * @param lan_id
	 * @return
	 */
	public static JSONObject queryCustInfoByCertNum(String cert_num, String lan_id) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.cust.QueryCustInfoByCertNum");
		body.put("version", "1.0");

		content.put("cert_num", cert_num);
		content.put("lan_id", lan_id);
		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "BSS30-通过客户证件号码查询客户信息");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getJSONObject("result").get("Code").equals("0000")) {
				return ReturnStrUtil.jsonObjStrNoBase("1", "接口调用失败", "");
			} else {
				return ReturnStrUtil.jsonObjStrNoBase("0", "接口调用成功", restJson);
			}
		} catch (Exception e) {
			logger.error("掌上销工具通过客户证件号码查询客户信息接口异常：", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "接口调用失败", null);
		}
	}

	/**
	 * 校验实名认证是否通过
	 *
	 * @return
	 */
	public static String chkrealNameCertify(String name, String identityId, String lan_id) {
		JSONObject map = new JSONObject();
		String rspCode = null;
		try {
			map.put("SysCode", "18");
			map.put("identityId", identityId);
			map.put("name", name);
			map.put("lan_id", lan_id);
			StringBuffer result = HttpUtil.doServerHttpOpenapiNew(JSON.toJSONString(map),
					NEW_DCOOS_URL + "/realNameCheck/realNameCertify.do", "校验实名认证是否通过");
			JSONObject jsonObject = JSONObject.parseObject(result.toString());
			JSONObject response = jsonObject.getJSONObject("Body").getJSONObject("realNameCertifyResponse")
					.getJSONObject("realNameCertifyReturn").getJSONObject("root").getJSONObject("body");
			return response.getString("rspCode");
		} catch (Exception e) {
			logger.error("校验实名认证是否通过异常：" + e);
			return "1";
		}
	}

	/**
	 * 一证五卡预校验(集团接口)
	 *
	 * @param certNum  身份证
	 * @param custName 姓名
	 * @return
	 */
	public static String chkCertPhoneRelNum(String certNum, String custName) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "chk.order.ChkCertPhoneRelNum");
		body.put("version", "1.1");

		content.put("certNum", certNum);// 号码
		content.put("custName", custName);// 销售品id
		body.put("content", content);

		String rest = null;

		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toString(), "一证五卡预校验(集团接口)");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return "-1";
			} else {
				int usedNum = restJson.getJSONObject("result").getJSONObject("result").getJSONObject("ContractRoot")
						.getJSONObject("SvcCont").getJSONObject("result").getInteger("usedNum");
				return String.valueOf(usedNum);
			}
		} catch (Exception e) {
			logger.error("一证五卡预校验(集团接口)接口异常：", e);
			return "-1";
		}
	}

	/**
	 * BSS30-综合平台客户资料接口
	 *
	 * @param dataMap
	 * @return
	 */
	public static Map<String, String> genPlaCustInfo(Map<String, Object> dataMap) {
		Map<String, String> resultMap = new HashMap<>();
		JSONObject map = new JSONObject();
		map.put("type", StringUtil.getMapByKey("type", dataMap));
		map.put("lan_id", StringUtil.getMapByKey("lan_id", dataMap));
		map.put("cust_id", StringUtil.getMapByKey("cust_id", dataMap));
		map.put("cust_name", StringUtil.getMapByKey("cust_name", dataMap));
		map.put("cert_type", StringUtil.getMapByKey("cert_type", dataMap));
		map.put("cert_num", StringUtil.getMapByKey("cert_num", dataMap));
		map.put("contact_people", StringUtil.getMapByKey("contact_people", dataMap));
		map.put("contact_phone", StringUtil.getMapByKey("contact_phone", dataMap));
		map.put("address", StringUtil.getMapByKey("address", dataMap));
		map.put("img", StringUtil.getMapByKey("img", dataMap));
		map.put("region_id", StringUtil.getMapByKey("region_id", dataMap));
		JSONObject root = new JSONObject();
		root.put("access_token", ACCESS_TOKEN);
		root.put("method", "bss.ord.cust.GenPlaCustInfo");
		root.put("version", "1.0");
		root.put("content", map.toJSONString());
		String result = HttpUtil.sendPost(EOP_URL, root.toJSONString(), "BSS30-综合平台客户资料接口");
		JSONObject jsonObject = JSONObject.parseObject(result);
		String resCode = jsonObject.getString("res_code");
		if ("00000".equals(resCode)) {
			// 接口调用成功获取成功
			JSONObject response = jsonObject.getJSONObject("result");
			String rspCode = response.getString("Code");
			if ("0000".equals(rspCode)) {
				JSONArray jsonArray = response.getJSONObject("Result").getJSONArray("CustInfo");

				JSONObject custInfo = jsonArray.getJSONObject(0);
				String custId = custInfo.getString("cust_id");
				resultMap.put("code", "0");
				resultMap.put("custId", custId);
				logger.info("综合平台客户资料接口调用成功custId：" + custId);
			} else {
				logger.info("综合平台客户资料接口调用失败" + result);
				resultMap.put("code", "1");
			}

		} else {
			logger.info("综合平台客户资料接口调用失败" + result);
			resultMap.put("code", "1");

		}
		return resultMap;
	}

	/**
	 * 查询客户欠费
	 *
	 * @param custId
	 * @return
	 */
	public static String queryArrears(String custId) {
		JSONObject param = new JSONObject();
		param.put("instanceType", "1");
		param.put("instanceId", custId);
		param.put("requestTime", DateUtils.sfm.format(new Date()));// 时间戳
		param.put("requestId", String.valueOf(new Date().getTime()));
		StringBuffer result = null;
		try {
			result = HttpUtil.doServerHttpOpenapiNew(param.toJSONString(), NEW_DCOOS_URL + "/oweQueryCRM/oweQueryCRM",
					"调用能开查询客户欠费接口");
		} catch (Exception e) {
			logger.error("查询客户欠费异常：" + e.getMessage());
		}
		JSONObject parseObject = JSONObject.parseObject(result.toString());
		String resCode = parseObject.getString("code");
		// if (resCode == null || !"0".equals(resCode)) {
		// return "1";
		// }
		String oweFlag = parseObject.getString("oweFlag");
		if (oweFlag == null || "1".equals(oweFlag)) {
			return "1";
		} else {
			return "0";
		}
	}

	/**
	 * 是否已实名
	 *
	 * @param custId
	 * @param cityCode
	 * @return
	 */
	public static String qryCustIsRealName(String custId, String cityCode) {
		Map<String, String> map = new HashMap<>();
		map.put("access_token", ACCESS_TOKEN);
		map.put("method", "bss.qry.cust.QryCustIsRealName");
		map.put("version", "1.0");

		Map<String, String> param = new HashMap<>();
		param.put("cust_id", custId);
		param.put("lan_id", cityCode);
		map.put("content", JSONObject.toJSONString(param));

		String jsonString = JSONObject.toJSONString(map);
		String result = HttpUtil.sendPost(EOP_URL, jsonString, "用户是否已实名");
		JSONObject parseObject = JSONObject.parseObject(result);
		String resCode = parseObject.getString("res_code");
		if (resCode == null || !"00000".equals(resCode)) {
			return "0";
		}
		JSONObject jsonObject = JSONObject.parseObject(result).getJSONObject("result").getJSONObject("QueryResults");
		JSONArray jsonArray = jsonObject.getJSONArray("IsRealName");
		JSONObject customerJson = jsonArray.getJSONObject(0);
		return customerJson.getString("is_realname");
	}

	/**
	 * BSS30-根据身份证查询产品列表
	 *
	 * @param cert_num 身份证
	 * @param lan_id
	 * @return
	 */
	public static JSONObject qryProdListByIDCard(String cert_num, String lan_id) {
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("access_token", ACCESS_TOKEN);
		paramMap.put("method", "bss.qry.prod.QryProdListByIDCard");
		paramMap.put("version", "1.0");

		JSONObject param = new JSONObject();
		param.put("cert_num", cert_num);
		param.put("lan_id", lan_id);

		paramMap.put("content", param.toJSONString());
		String jsonString = JSONObject.toJSONString(paramMap);
		try {
			String result = HttpUtil.sendPost(EOP_URL, jsonString, "BSS30-根据身份证查询产品列表");
			JSONObject parse = JSONObject.parseObject(result);
			String res_code = parse.getString("res_code");
			if ("00000".equals(res_code) && parse.getJSONObject("result").getString("Code").equals("0000")) {
				JSONArray prodInfo = parse.getJSONObject("result").getJSONObject("QueryResults")
						.getJSONArray("ProdInfo");

				return ReturnStrUtil.jsonObjStrNoBase("0", "接口调用成功", prodInfo);
			}
			return ReturnStrUtil.jsonObjStrNoBase("1", parse.getJSONObject("result").get("Message").toString(), "");
		} catch (Exception e) {
			logger.error("掌上销工具根据身份证查询产品列表接口异常", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "接口调用失败", "");
		}
	}

	/**
	 * BSS30-CRM状态查询接口(acc_nbr)
	 *
	 * @param acc_num
	 * @param lan_id
	 * @return
	 */
	public static JSONObject qryByStatusHicky(String acc_num, String lan_id) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.prodinst.QryByStatusHicky");
		body.put("version", "1.0");

		content.put("acc_num", acc_num);
		content.put("lan_id", lan_id);
		content.put("prod_id", "80000045");

		body.put("content", content);
		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "BSS30-CRM状态查询接口(acc_nbr)");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))) {
				return ReturnStrUtil.jsonObjStrNoBase("1", "查询失败!", "");
			}
			return ReturnStrUtil.jsonObjStrNoBase("0", "查询成功",
					restJson.getJSONObject("result").getJSONObject("QueryResults").getJSONArray("StateInfo"));
		} catch (Exception e) {
			logger.error("BSS30-CRM状态查询接口(acc_nbr)异常: ", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "查询异常", "");
		}
	}

	/**
	 * BSS根据身份证查询灰名单和黑名单状态包年
	 * <p>
	 * Title: searchGreyBlackListByCertNum
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 *
	 * @param cert_num
	 * @return
	 * <AUTHOR>
	 * @date 2022年9月3日
	 * @version 1.0
	 */
	public static JSONObject searchGreyBlackListByCertNum(String cert_num) {
		try {
			JSONObject body = new JSONObject();
			body.put("cert_num", cert_num);
			StringBuffer rest = HttpUtil.doServerHttpOpenapiNew(JSON.toJSONString(body),
					NEW_DCOOS_URL + "/bss.searchGreyBlackListByCertNum/bss.searchGreyBlackListByCertNum",
					"BSS根据身份证查询灰名单和黑名单状态");
			return ReturnStrUtil.jsonObjStrNoBase("0", "成功", rest.toString());
		} catch (Exception e) {
			logger.error("BSS根据身份证查询灰名单和黑名单状态异常", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "BSS根据身份证查询灰名单和黑名单状态异常", e.getMessage());
		}
	}

	/**
	 * 集团一证十卡验证
	 *
	 * @return
	 */
	public static JSONObject oneCertMulCard(String certNum, String certType) {
		try {
			JSONObject orderJsonObject = new JSONObject();
			orderJsonObject.put("certNum", certNum);
			orderJsonObject.put("certType", certType);
			logger.info("集团一证十卡验证请求报文：" + orderJsonObject.toJSONString());
			StringBuffer result = HttpUtil.doServerHttpOpenapiNew(orderJsonObject.toJSONString(),
					NEW_DCOOS_URL + "/hnyxsApiServe/queryOrderInfo/oneCertMulCard", "集团一证十卡验证");
			logger.info("集团一证十卡验证返回报文：" + result);
			JSONObject jsonObject = JSONObject.parseObject(result.toString());
			if ("0".equals(jsonObject.getString("statusCode"))) {
				return ReturnStrUtil.jsonObjStrNoBase("0", jsonObject.getString("message"),
						jsonObject.getJSONObject("data"));
			} else {
				return ReturnStrUtil.jsonObjStrNoBase("1", jsonObject.getString("message"),
						jsonObject.getJSONObject("data"));
			}
		} catch (Exception e) {
			logger.error("十卡查询异常：", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "十卡查询异常:" + e.getMessage(), "");
		}
	}

	/**
	 * BSS30-查询政企客户信息
	 *
	 * @param certType 证件类型：1 身份证 , 3 护照 , 4 港澳居民来往内地通行证 , 42 台湾居民来往内地通行证 , 49
	 *                 统一社会信用代码证书 , 51 军人身份证
	 * @param certNum  证件号码
	 * @param lanId    本地网编码
	 */
	public static Map<String, Object> qryGoverCustInfo(String certType, String certNum, String lanId) {

		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.custinfo.QryGoverCustInfo");
		body.put("version", "1.0");

		content.put("cust_name", "");
		content.put("lan_id", lanId);
		content.put("cert_num", certNum);
		content.put("cert_type", certType);

		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toString(), "查询政企客户信息接口");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return ReturnStrUtil.jsonObjStrNoBase("1", "查询政企客户信息接口失败", null);
			} else {
				JSONArray jsonArray = restJson.getJSONObject("result").getJSONObject("QueryResults")
						.getJSONArray("CustInfo");
				if (jsonArray.size() > 0) {
					return ReturnStrUtil.jsonObjStrNoBase("0", "查询政企客户信息接口成功", jsonArray);
				}
				return ReturnStrUtil.jsonObjStrNoBase("1", "查询政企客户信息为空", null);
			}
		} catch (Exception e) {
			logger.error("查询政企客户信息接口异常：", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "查询政企客户信息接口异常", null);
		}
	}

	/**
	 * 查询可售模板列表 模板就是商品
	 *
	 * @param { "templateIdList": [24018,53008], "qryContent": "", "lanId":
	 *          "731","templateGroupId": "", "serviceOfferId": "1" }
	 * @return R
	 * @MethodName: qryTemplateList
	 * @<NAME_EMAIL>
	 * @date 2025-03-27 05:33:11
	 */
	public static R qryTemplateList(JSONObject body) {
		StringBuffer rest = null;
		try {
			rest = HttpUtil.doServerHttpOpenapiNew(JSON.toJSONString(body), NEW_DCOOS_URL + "/hncrm/qryTemplateList",
					"查询可售模板列表接口");
			if (rest == null) {
				return R.error(1, "可售模板列表为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest.toString());
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONArray("resultObject"));
			}
		} catch (Exception e) {
			logger.error("查询可售模板列表接口异常：", e);
			return R.error(1, "查询可售模板列表异常：" + e.getMessage());
		}
	}

	/**
	 * 查询指定模板内容详情
	 *
	 * @param { "custId": "273100753572", "lanId": "731", "templateId": "19012",
	 *          "serviceOfferId": "1" }
	 * @return R
	 * @MethodName: qryTemplateInfo
	 * @<NAME_EMAIL>
	 * @date 2025-03-27 08:11:55
	 */
	public static R qryTemplateInfo(JSONObject body) {
		String rest = null;
		try {
//			rest = HttpUtil.httpClientdoPost(JSON.toJSONString(body), ,"查询指定模板内容详情接口");
			rest = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/qryTemplateInfo", null, "UTF-8", "查询指定模板内容详情接口",
					JSON.toJSONString(body));
			if (rest == null) {
				return R.error(1, "查询指定模板内容详情为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONObject("resultObject"));
			}
		} catch (Exception e) {
			logger.error("查询指定模板内容详情接口异常：", e);
			return R.error(1, "查询指定模板内容详情接口异常：" + e.getMessage());
		}
	}

	final static Map<String, String> serviceActionStatic = new HashMap<>();

	static {
		serviceActionStatic.put("H5SceneCreate", "创建暂存单");
		serviceActionStatic.put("H5SetAttrs", "设置产品属性");
		serviceActionStatic.put("H5ModifyRes", "添加终端");
		serviceActionStatic.put("H5CommitSceneCheck", "订单提交校验");
		serviceActionStatic.put("H5CommitSceneDeal", "订单提交生单");
		serviceActionStatic.put("H5CalculateFee", "订单提交费用计算");
		serviceActionStatic.put("loadScene", "查询暂存单详情信息");
		serviceActionStatic.put("H5SetCommitInfo", "收费确认设置支付信息");
		serviceActionStatic.put("H5CommitFee", "收费确认");
		serviceActionStatic.put("ApproveCommit", "保存审批信息");
	}

	/**
	 * 模板受理的接口H5
	 *
	 * @param body
	 * @param serviceAction
	 * @return R
	 * @<NAME_EMAIL>
	 * @date 2025-03-27 08:29:33
	 */
	public static R exchangeTemplateOrder(String body, String serviceAction) {
		String rest = null;
		String name = serviceActionStatic.get(serviceAction);
		try {
			// 记录请求参数
//			logger.info("{}，模板受理的接口H5请求参数:{}", name, body);
			// 获取配置的URL
//			String exchangeTemplateOrderUrl = UrlConfigUtil.getUrl("EXCHANGE_TEMPLATE_ORDER");

			HnzsxSysUrlConfigEntity sysUrlConfigPojo = UrlConfigUtil.getSysUrlConfigEntity("EXCHANGE_TEMPLATE_ORDER");
			if (1 == sysUrlConfigPojo.getFuseBreakerSwitch()) {
				return R.error(1, sysUrlConfigPojo.getFuseBreakerMsg());
			}

			// 发送POST请求到模板受理接口
			rest = HttpUtil.sendPostRequest(sysUrlConfigPojo.getFullUrl(), body, name);
//			String backupUrl = UrlConfigUtil.getUrl("EXCHANGE_TEMPLATE_ORDER_BACKUP");
			// 记录响应参数
//			logger.info("{}，模板受理的接口H5响应参数:{}", name, rest);

			// 检查响应是否为空
			if (rest == null) {
				return R.error(1, name + "为空");
			}

			// 解析响应字符串为通用Object类型
			Object parsedRest = JSON.parse(rest);
			JSONObject restJson;

			// 判断解析后的对象类型
			if (parsedRest instanceof JSONObject) {
				// 如果是JSONObject，直接赋值
				restJson = (JSONObject) parsedRest;
			} else if (parsedRest instanceof JSONArray) {
				// 如果是JSONArray
				JSONArray restArray = (JSONArray) parsedRest;
				// 假设有效数据在数组的第一个元素（且为JSONObject）
				if (!restArray.isEmpty() && restArray.get(0) instanceof JSONObject) {
					restJson = restArray.getJSONObject(0);
					// 特殊处理：如果serviceAction不是以下几种，且预期结果是数组本身
					// 则将原始数组存回resultObject，以兼容后续逻辑
					if (!("H5CommitSceneCheck".equals(serviceAction) || "H5CalculateFee".equals(serviceAction)
							|| "H5CommitSceneDeal".equals(serviceAction) || "loadScene".equals(serviceAction)
							|| "H5CommitFee".equals(serviceAction) || "H5SetCommitInfo".equals(serviceAction))) {
						restJson.put("resultObject", restArray);
					}
				} else {
					// JSONArray格式不符合预期，记录错误并返回
					logger.error(name + "接口返回JSONArray格式不符合预期: {}", rest);
					return R.error(1, name + "接口返回格式错误");
				}
			} else {
				// 响应不是有效的JSON格式，记录错误并返回
				logger.error(name + "接口返回非JSON格式: {}", rest);
				return R.error(1, name + "接口返回格式错误");
			}

			// 检查接口返回的业务结果码
			if (!"0".equals(restJson.getString("resultCode"))) {
				// 如果结果码非0，表示失败，返回错误信息
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				// 检查resultObject是否存在，若不存在但resultCode为0，则直接返回成功
				if (!restJson.containsKey("resultObject")) {
					return R.ok(restJson.getString("resultMsg"));
				}
				// 根据不同的serviceAction处理resultObject
				if ("H5CommitSceneCheck".equals(serviceAction) || "H5CalculateFee".equals(serviceAction)) {
					// 返回布尔类型结果
					return R.ok(restJson.getString("resultMsg")).put("data", restJson.getBoolean("resultObject"));
				} else if ("H5CommitSceneDeal".equals(serviceAction) || "loadScene".equals(serviceAction)) {
					// 返回JSONObject类型结果
					return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONObject("resultObject"));
				} else if ("H5CommitFee".equals(serviceAction) || "H5SetCommitInfo".equals(serviceAction)) {
					// 返回String类型结果
					return R.ok(restJson.getString("resultMsg")).put("data", restJson.getString("resultObject"));
				} else if ("ApproveCommit".equals(serviceAction)) {
					// 处理审批信息提交，处理resultObject可能为JSONArray或JSONObject的情况
					Object resultObject = restJson.get("resultObject");
					if (resultObject instanceof JSONArray) {
						// 如果是JSONArray，直接返回
						return R.ok(restJson.getString("resultMsg")).put("data", (JSONArray)resultObject);
					} else if (resultObject instanceof JSONObject) {
						// 如果是JSONObject，直接返回
						return R.ok(restJson.getString("resultMsg")).put("data", (JSONObject)resultObject);
					} else {
						// 其他情况，直接返回原始结果
						return R.ok(restJson.getString("resultMsg")).put("data", resultObject);
					}
				} else {
					// 处理其他情况，特别是当resultObject在调整后仍可能是JSONArray时
					Object resultObject = restJson.get("resultObject");
					if (resultObject instanceof JSONArray) {
						// 返回JSONArray类型结果
						return R.ok(restJson.getString("resultMsg")).put("data", (JSONArray) resultObject);
					} else {
						// 处理未预期的resultObject类型，记录警告
						logger.warn(name + "接口返回 resultObject 类型未按预期处理 for serviceAction: {}, type: {}", serviceAction,
								resultObject != null ? resultObject.getClass().getName() : "null");
						// 默认返回原始的resultObject
						return R.ok(restJson.getString("resultMsg")).put("data", resultObject);
					}
				}
			}
		} catch (Exception e) {
			// 捕获并记录异常
			logger.error(name + "接口异常：", e);
			return R.error(1, name + "接口异常：" + e.getMessage());
		}
	}

	public static String dddd() {
		// 创建 Root 层
		Map<String, Object> root = new HashMap<>();
		root.put("notCancel", "true");

		// 创建 Header 层
		Map<String, String> header = new HashMap<>();
		header.put("ClientId", "DQSGSF");
		header.put("ExchangeId", "DQSGSF202211111723565110936205");
		header.put("Password", "******");
		root.put("Header", header);

		// 创建 custInfo 层
		Map<String, String> custInfo = new HashMap<>();
		custInfo.put("ownerCustId", "904135272109");
		custInfo.put("useCustId", "904135272109");

		// 创建 otherInfo 层
		Map<String, String> otherInfo = new HashMap<>();
		otherInfo.put("lanId", "731");
		otherInfo.put("orgId", "110000426229");
		otherInfo.put("regionId", "73101");
		otherInfo.put("staffCode", "DZQDSDZXAI99");
		otherInfo.put("staffId", "200012955923");
		otherInfo.put("staffName", "张春松");

		// 创建 prodAttrs 层
		Map<String, String> prodAttrs = new HashMap<>();
		prodAttrs.put("attrId", "990000382");
		prodAttrs.put("attrName", "手机卡号");
		prodAttrs.put("attrValue", "8986112415598598899");

		// 创建 accessProdInst 层
		Map<String, Object> accessProdInst = new HashMap<>();
		accessProdInst.put("actionType", "A");
		accessProdInst.put("prodAttrs", prodAttrs);
		accessProdInst.put("prodInstId", "************");

		// 创建 acceptInfo 层
		Map<String, Object> acceptInfo = new HashMap<>();
		acceptInfo.put("accessProdInst", new Object[] { accessProdInst });
		acceptInfo.put("sceneInstId", "1071508868675919872");
		acceptInfo.put("templateId", "19012");
		acceptInfo.put("mktProdInst", new Object[] {});
		acceptInfo.put("selectOffers", new Object[] {});

		// 创建 OrderRequest 层
		Map<String, Object> orderRequest = new HashMap<>();
		orderRequest.put("custInfo", custInfo);
		orderRequest.put("Account", new HashMap<>());
		orderRequest.put("otherInfo", otherInfo);
		orderRequest.put("serviceAction", "H5SetAttrs");
		orderRequest.put("acceptInfo", new Object[] { acceptInfo });
		orderRequest.put("extCustOrderAttrs", new Object[] {});
		orderRequest.put("funcProdInst", new Object[] {});
		orderRequest.put("token", "HNZSXH5JSSL20250414000412273489");

		root.put("OrderRequest", orderRequest);

		// 将 Map 转换为 JSONObject
		JSONObject jsonObject = new JSONObject(root);

		// 格式化输出 JSON 字符串
		String jsonString = "";
		try {
			jsonString = jsonObject.toString();
			// 如果你需要转义，可使用以下代码
			// String escapedJsonString = StringEscapeUtils.escapeJson(jsonString);
			// System.out.println(escapedJsonString);

			// 直接输出格式化后的 JSON 字符串
			logger.info("直接输出格式化后的 JSON 字符串:{}", jsonString);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return jsonString;
	}

	/**
	 * 查询H5购物车暂存单
	 *
	 * @param body
	 * @return R
	 * @MethodName: shoppingCartGroups
	 * @<NAME_EMAIL>
	 * @date 2025-03-30 05:09:56
	 */
	public static R shoppingCartGroups(String body) {
		String rest = null;
		try {
			rest = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/shoppingCartGroups", null, null, "查询H5购物车暂存单",
					body);
			if (rest == null) {
				return R.error(1, "查询H5购物车暂存单参数为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONArray("resultObject"));
			}
		} catch (Exception e) {
			logger.error("查询H5购物车暂存单接口异常：", e);
			return R.error(1, "查询H5购物车暂存单接口异常：" + e.getMessage());
		}
	}

	/**
	 * 校验是否需要活体
	 *
	 * @param body
	 * @return R
	 * @MethodName: getPersonCheckImgInfo
	 * @<NAME_EMAIL>
	 * @date 2025-03-29 10:22:26
	 */
	public static R getPersonCheckImgInfo(JSONObject body) {
		StringBuffer rest = null;
		try {
			rest = HttpUtil.doServerHttpOpenapiNew(JSON.toJSONString(body),
					NEW_DCOOS_URL + "/hncrm/getPersonCheckImgInfo", "校验是否需要活体接口");
			if (rest == null) {
				return R.error(1, "校验是否需要活体为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest.toString());
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONObject("resultObject"));
			}
		} catch (Exception e) {
			logger.error("校验是否需要活体接口异常：", e);
			return R.error(1, "校验是否需要活体接口异常：" + e.getMessage());
		}
	}

	/**
	 * 是否需要订单预览视图接口
	 *
	 * @param body
	 * @return R
	 * @MethodName: PREORDERCHECK
	 * @<NAME_EMAIL>
	 * @date 2025-03-29 10:40:44
	 */
	public static R PREORDERCHECK(JSONObject body) {
		StringBuffer rest = null;
		try {
			rest = HttpUtil.doServerHttpOpenapiNew(body.toJSONString(), NEW_DCOOS_URL + "/hncrm/PREORDERCHECK",
					"是否需要订单预览视图接口");
			if (rest == null) {
				return R.error(1, "是否需要订单预览视图为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest.toString());
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getBoolean("resultObject"));
			}
		} catch (Exception e) {
			logger.error("是否需要订单预览视图接口异常：", e);
			return R.error(1, "是否需要订单预览视图接口异常：" + e.getMessage());
		}
	}

	/**
	 * H5查询当前客户是否存在审批单
	 *
	 * @param body
	 * @return R
	 * @MethodName: H5CANAPPROVEAPP
	 * @<NAME_EMAIL>
	 * @date 2025-03-29 10:59:01
	 */
	public static R H5CANAPPROVEAPP(JSONObject body) {
		StringBuffer rest = null;
		try {
			rest = HttpUtil.doServerHttpOpenapiNew(JSON.toJSONString(body), NEW_DCOOS_URL + "/hncrm/H5CANAPPROVEAPP",
					"H5查询当前客户是否存在审批单接口");
			if (rest == null) {
				return R.error(1, "H5查询当前客户是否存在审批单为空");
			}
			JSONObject restJson = JSONObject.parseObject(rest.toString());
			if (!"0".equals(restJson.getString("resultCode"))) {
				return R.error(1, restJson.getString("resultMsg"));
			} else {
				return R.ok(restJson.getString("resultMsg")).put("data", restJson.getJSONObject("resultObject"));
			}
		} catch (Exception e) {
			logger.error("H5查询当前客户是否存在审批单接口异常：", e);
			return R.error(1, "H5查询当前客户是否存在审批单接口异常：" + e.getMessage());
		}
	}

	/**
	 * 查询集团选号池信息按渠道编码（集团） ok
	 *
	 * @param staff_code
	 * @param lanId
	 * @return
	 */
	public static R qryJTNumberSelectPoolInfo(String channel_nbr, String lanId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "qry.resinfo.QryJTNumberSelectPoolInfo");
		body.put("version", "1.0");

		content.put("transaction_id", StringUtil.getTransactionID());
		content.put("req_time", DateUtils.format(DateUtils.DATE_Y_M_D_H_M_S));

		content.put("page_index2", "1");
		content.put("page_size2", "100");
		content.put("region_id", CityUtil.lan_codes.get(lanId));
		content.put("channel_nbr", channel_nbr);
		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "查询集团选号池信息按渠道编码（集团）");
			JSONObject restJson = JSONObject.parseObject(rest);
			JSONObject pub_res = restJson.getJSONObject("result").getJSONObject("SvcCont").getJSONObject("SOO")
					.getJSONObject("PUB_RES");
			if (!"00000".equals(restJson.getString("res_code")) || !restJson.getString("res_message").equals("Success")
					|| !pub_res.get("RspType").equals("0") || !pub_res.get("RspCode").equals("0000")) {
				return R.error(1, "接口调用失败");
			} else {
				return R.ok("接口调用成功").put("data", restJson.getJSONObject("result").getJSONObject("SvcCont")
						.getJSONObject("SOO").getJSONArray("CHANNEL_PNPOOL_RELA"));
			}
		} catch (Exception e) {
			logger.error("查询集团选号池信息按渠道编码（集团）接口异常：", e);
			return R.error(1, "查询集团选号池信息按渠道编码（集团）接口异常" + e);
		}
	}

	/**
	 * 集团lte根据渠道查询号码池-应急
	 *
	 * @param staff_code
	 * @param lanId
	 * @return
	 */
	public static R qryJTNumberSelectPoolInfoNew(String channelNbr, String lanId) {
		JSONObject body = new JSONObject();
		body.put("lan_id", CityUtil.lan_codes.get(lanId));
		body.put("channel_nbr", channelNbr);

		logger.info("集团lte根据渠道查询号码池-应急请求报文：" + body.toString());
		StringBuffer rest = null;
		try {
			rest = HttpUtil.doServerHttpOpenapiNew(body.toJSONString(),
					NEW_DCOOS_URL + "/hncrm/lte/supQueryPhoneNbrPools", "查询指定模板内容详情接口");
			logger.info("集团lte根据渠道查询号码池-应急返回报文：" + rest.toString());
			JSONObject restJson = JSONObject.parseObject(rest.toString());
			if (!"0000".equals(restJson.getString("code"))) {
				return R.error(1, "接口调用失败");
			} else {
				return R.ok("接口调用成功").put("data", restJson.getJSONArray("datas"));
			}
		} catch (Exception e) {
			logger.info("集团lte根据渠道查询号码池-应急接口异常：", e);
			return R.error(1, "集团lte根据渠道查询号码池-应急接口调用失败");
		}
	}

	/**
	 * 查询手机号码接口
	 *
	 * @return
	 */
	public static List<Map<String, Object>> queryPhoneNbrs(Map<String, Object> dataMap) {
		List<Map<String, Object>> phoneList = new ArrayList<>();
		JSONObject map = new JSONObject();
		map.put("transactionid", StringUtil.getTransactionID());
		map.put("req_time", DateUtils.format(DateUtils.DATE_Y_M_D_H_M_S));
		// acc_nbr_pool_id_arr
		String accPoolArr = StringUtil.getMapByKey("acc_nbr_pool_id_arr", dataMap);
		if (StringUtils.isNotBlank(accPoolArr)) {
			JSONArray parseArray = JSONObject.parseArray(accPoolArr);
			String str = "";
			for (int i = 0; i < parseArray.size(); i++) {
				if (i == 0) {
					str += parseArray.get(i);
				} else {
					str += "," + parseArray.get(i);
				}
			}
			map.put("acc_nbr_pool_id", str);
		} else {
			map.put("acc_nbr_pool_id", StringUtil.getMapByKey("acc_nbr_pool_id", dataMap));
		}
		map.put("accNbrEnd", StringUtil.getMapByKey("accNbrEnd", dataMap));
		map.put("acc_nbr_features", StringUtil.getMapByKey("acc_nbr_features", dataMap));
		map.put("acc_nbr_last_four_num", StringUtil.getMapByKey("acc_nbr_last_four_num", dataMap));
		map.put("pageIndex", StringUtil.getMapByKey("pageIndex", dataMap));
		map.put("pageSize", StringUtil.getMapByKey("pageSize", dataMap));
		map.put("lan_id", CityUtil.lan_codes.get(StringUtil.getMapByKey("lan_id", dataMap)));
		map.put("channel_nbr", StringUtil.getMapByKey("saleBoxCode", dataMap));// 店中商编码

		// 靓号查询参数
		if (StringUtils.isNotBlank(StringUtil.getMapByKey("matchNo", dataMap))) {
			map.put("searchType", StringUtil.getMapByKey("searchType", dataMap));// 查询类型
			map.put("matchNo", StringUtil.getMapByKey("matchNo", dataMap));// 需要查询的号码
		} else {
			map.put("codeLevel", StringUtil.getMapByKey("codeLevel", dataMap));
			map.put("accNbrStart", StringUtil.getMapByKey("accNbrStart", dataMap));
			map.put("gt_prestore", StringUtil.getMapByKey("gt_prestore", dataMap));
			map.put("lt_prestore", StringUtil.getMapByKey("lt_prestore", dataMap));
		}

		JSONObject root = new JSONObject();
		root.put("access_token", ACCESS_TOKEN);
		root.put("method", "lte.queryPhoneNbrs");
		root.put("version", "1.0");
		root.put("content", map.toJSONString());
		String str = HttpUtil.sendPost(EOP_URL, root.toJSONString(), "4G号码查询接口");
		if (StringUtils.isBlank(str)) {
			return phoneList;
		}

		JSONObject jsonObject = JSONObject.parseObject(str);
		String resCode = jsonObject.getString("res_code");
		if ("00000".equals(resCode)) {
			// 接口调用成功获取成功
			JSONObject response = jsonObject.getJSONObject("result").getJSONObject("TcpCont").getJSONObject("Response");
			String rspCode = response.getString("RspCode");
			if ("0000".equals(rspCode)) {
				// 查询号码成功
				JSONObject soo = jsonObject.getJSONObject("result").getJSONObject("SvcCont").getJSONObject("SOO");
				JSONArray phoneArray = new JSONArray();
				try {
					if (!soo.getJSONObject("PUB_RES").getString("RspType").equals("0")
							&& !soo.getJSONObject("PUB_RES").getString("RspCode").equals("0000")) {
						return phoneList;
					}
					// wm 2020-12-18 修改
					if (StringUtil.isJsonObject(soo.getString("PHONE_NBR_RES"))) {
						phoneArray.add(soo.getJSONObject("PHONE_NBR_RES"));
					} else {
						phoneArray = soo.getJSONArray("PHONE_NBR_RES");
					}

					/*
					 * String totalCount = soo.getJSONObject("PAGE_RES").getString("TOTAL_COUNT");
					 * if (totalCount.equals("1")) {
					 * phoneArray.add(soo.getJSONObject("PHONE_NBR_RES")); } else { phoneArray =
					 * soo.getJSONArray("PHONE_NBR_RES"); }
					 */
				} catch (Exception e) {
					logger.error("4G号码查询异常：", e);
				}
				if (phoneArray != null) {
					for (int i = 0; i < phoneArray.size(); i++) {
						Object object = phoneArray.get(i);

						Map<String, String> phoneMap = (Map) object;
						Map<String, Object> phone = new HashMap<>();
						if (phoneMap != null) {
							// 号码
							phone.put("phoneNbr", phoneMap.get("PHONE_NBR"));
							// 保底金额 PHONE_NBR_PRICE
							phone.put("phoneNbrPrice", phoneMap.get("PHONE_NBR_PRICE"));
							// 预存款 PRESTORE
							phone.put("preStore", phoneMap.get("PRESTORE"));
							// 号码等级
							phone.put("phoneNbrLevelId",
									StringUtil.nbrLevel.get(phoneMap.get("PHONE_NBR_LEVEL_ID").toString()));

							phone.put("lanId", phoneMap.get("LAN_ID").toString());// 地市
							phone.put("provinceId", phoneMap.get("PROVINCE_ID").toString());
							phone.put("statusCd", phoneMap.get("STATUS_CD").toString());
							phone.put("numLevelId", "");// 接口无返回
							phone.put("phoneNbrCharacterId", phoneMap.get("PHONE_NBR_CHARACTER_ID").toString());
							phone.put("poolId", phoneMap.get("POOL_ID").toString());// 号池编码
							phone.put("poolName", phoneMap.get("POOL_NAME").toString());// 号池名称

							phone.put("checked", false);
							phone.put("id", i);
							// 是否能开通国际漫游,如果开通说明已被占用
							try {
								String mobileCheck = InterfaceUtil.qryOpenGMPossible(phoneMap.get("PHONE_NBR"),
										(String) dataMap.get("lan_id"));
								if ("0".equals(mobileCheck)) {
									// 该号码没有开通国际漫游，可用
									phoneList.add(phone);
								}
							} catch (Exception e) {
								continue;
							}
						}
						if (phoneList.size() >= 20) {
							return phoneList;
						}
					}
				}
			}
		}
		return phoneList;
	}

	/**
	 * 集团lte选号查询-应急
	 *
	 * @return
	 */
	public static JSONObject queryPhoneNbrsNew(Map<String, Object> dataMap) {
		JSONObject resultMap = new JSONObject();
		JSONArray phoneList = new JSONArray();
		JSONObject body = new JSONObject();
		body.put("pool_id", StringUtil.getMapByKey("acc_nbr_pool_id", dataMap));// 号池编码
		body.put("end_no", StringUtil.getMapByKey("acc_nbr_features", dataMap));// 尾号不包含数字
		body.put("lan_id", CityUtil.lan_codes.get(StringUtil.getMapByKey("lan_id", dataMap)));// 本地网
		body.put("acc_nbr_end", StringUtil.getMapByKey("accNbrEnd", dataMap));// 号码尾
		body.put("channel_nbr", StringUtil.getMapByKey("saleBoxCode", dataMap));// 渠道编码

		// 靓号查询参数
		if (StringUtil.isNotNull(StringUtil.getMapByKey("matchNo", dataMap))) {
			body.put("search_type", StringUtil.getMapByKey("searchType", dataMap));// 查询类型
			body.put("match_nbr", StringUtil.getMapByKey("matchNo", dataMap));// 包含数字
		} else {
			body.put("nbr_head", StringUtil.getMapByKey("accNbrStart", dataMap));// 号码头
			body.put("code_level", StringUtil.getMapByKey("codeLevel", dataMap));// 号码等级
			body.put("gt_prestore", StringUtil.getMapByKey("gt_prestore", dataMap));// 预存
			body.put("lt_prestore", StringUtil.getMapByKey("lt_prestore", dataMap));// 预存
			body.put("match_nbr", StringUtil.getMapByKey("match_nbr", dataMap));// 包含数字
		}

//		logger.info("4G号码查询入参报文：" + body.toString());
		StringBuffer str = null;
		try {
			str = HttpUtil.doServerHttpOpenapiNew(body.toJSONString(), NEW_DCOOS_URL + "/hncrm/lte/supQueryPhoneNbrs",
					"4G号码查询-应急接口");
//			logger.info("4G号码查询返回报文：" + str.toString());
		} catch (Exception e) {
			logger.error(" 查询手机号码接口接口异常：", e);
			return R.error(1, "查询手机号码接口接口异常：" + e.getMessage());
		}
		if (StringUtil.isEmpty(str)) {
			return R.error(1, "接口调用异常");
		}
		JSONObject jsonObject = JSONObject.parseObject(str.toString());
		String resultCode = String.valueOf(jsonObject.get("code"));
		String resultMessage = String.valueOf(jsonObject.get("message"));
		if ("0000".equals(resultCode)) {
			JSONArray datas = jsonObject.getJSONArray("datas");
			if (!CollectionUtils.isEmpty(datas)) {
				// for (Object object : datas) {
				for (int i = 0; i < datas.size(); i++) {
					JSONObject object = datas.getJSONObject(i);
					// Map<String, String> phoneMap = (Map) object;
					JSONObject phone = new JSONObject();
					if (object != null) {
						// 号码
						phone.put("phoneNbr", object.getString("phone_nbr"));
						// 保底金额 PHONE_NBR_PRICE
						phone.put("phoneNbrPrice", object.getString("phone_nbr_price"));
						// 预存款 PRESTORE
						phone.put("preStore", object.getString("pre_store"));
						// 号码等级
						phone.put("phoneNbrLevelId",
								StringUtil.nbrLevel.get(object.getString("phone_nbr_level_id").toString()));

						phone.put("lanId", object.getString("lan_id").toString());// 地市
						phone.put("provinceId", object.getString("province_id").toString());
						phone.put("statusCd", object.getString("status_cd").toString());
						phone.put("numLevelId", object.getString("num_level_id").toString());
						phone.put("phoneNbrCharacterId", object.getString("phone_nbr_character_id").toString());
						phone.put("poolId", object.getString("pool_id").toString());// 号池编码
						phone.put("poolName", object.getString("pool_name").toString());// 号池名称

						phone.put("checked", false);
						phone.put("id", i);
						// //是否能开通国际漫游,如果开通说明已被占用
						try {
							String mobileCheck = InterfaceUtil.qryOpenGMPossible(object.getString("phone_nbr"),
									(String) dataMap.get("lan_id"));
							if ("0".equals(mobileCheck)) {
								// 该号码没有开通国际漫游，可用
								phoneList.add(phone);
							}
						} catch (Exception e) {
							continue;
						}
					}
				}
			} else {
				return R.error(1, "未查询到号码信息");
			}
		} else {
			return R.error(1, resultMessage);
		}
		logger.info("4G号码查询结果" + resultMap);
		return R.ok(resultMessage).put("data", phoneList);
	}

	/**
	 * 查询是否可以开通国际漫游 TODO
	 *
	 * @param jsonMap
	 * @return
	 */
	public static String qryOpenGMPossible(String mobile, String lan_id) {
		String rspCode = "";
		try {
			JSONObject map = new JSONObject();
			map.put("acc_num", mobile);
			map.put("lan_id", lan_id);
			JSONObject root = new JSONObject();
			root.put("access_token", ACCESS_TOKEN);
			root.put("method", "bss.qry.offeruse.QryInternationalRomingIsPossible");
			root.put("version", "1.0");
			root.put("content", map);
			String result = HttpUtil.sendPost(EOP_URL, root.toJSONString(), "查询是否可以开通国际漫游");

			JSONObject jsonObject = JSONObject.parseObject(result);
			String resCode = jsonObject.getString("res_code");
			if ("00000".equals(resCode)) {
				// 接口调用成功获取成功
				JSONObject response = jsonObject.getJSONObject("result").getJSONObject("QueryResults");
				JSONArray jsonArray = response.getJSONArray("QueryInfo");
				if (jsonArray.size() == 0) {
					rspCode = "0";
				}
			}
			return rspCode;
		} catch (Exception e) {
			logger.error("查询是否可以开通国际漫游异常", e);
			return rspCode;
		}
	}

	/**
	 * 4G号码预占-集团
	 *
	 * @param phone
	 * @param userCardId
	 * @return
	 */
	public static JSONObject preSelectNbr(Map<String, Object> dataMap) {
		try {
			JSONObject map = new JSONObject();
			map.put("transactionid", StringUtil.getTransactionID());
			map.put("req_time", DateUtils.format(DateUtils.DATE_Y_M_D_H_M_S));
			map.put("lan_id", CityUtil.lan_codes.get(StringUtil.getMapByKey("lan_id", dataMap)));
			map.put("staff_code", StringUtil.getMapByKey("staffCode", dataMap));
			map.put("cert_number", StringUtil.getMapByKey("cert_number", dataMap));
			map.put("ExchangeId", "");
			map.put("channel_nbr", StringUtil.getMapByKey("saleBoxCode", dataMap));
			map.put("acc_nbr", StringUtil.getMapByKey("acc_nbr", dataMap));
			map.put("password", StringUtil.getMapByKey("phoneApprovePassword", dataMap));
			JSONObject root = new JSONObject();
			root.put("access_token", ACCESS_TOKEN);
			root.put("method", "lte.preSelectNbr");
			root.put("version", "1.0");
			root.put("content", map.toJSONString());
			String result = HttpUtil.sendPost(EOP_URL, root.toJSONString(), "4G号码预占");
			if (StringUtils.isBlank(result)) {
				return ReturnStrUtil.jsonObjStrNoBase("1", "4G号码占用,调用接口失败", null);
			}
			JSONObject jsonObject = JSONObject.parseObject(result);
			String resCode = jsonObject.getString("res_code");
			if ("00000".equals(resCode)) {
				// 接口调用成功获取成功
				JSONObject response = jsonObject.getJSONObject("result").getJSONObject("TcpCont")
						.getJSONObject("Response");
				String rspCode = response.getString("RspCode");
				String RspDesc = response.getString("RspDesc");
				if ("0000".equals(rspCode)) {
					return ReturnStrUtil.jsonObjStrNoBase("0", RspDesc, null);
				} else {
					return ReturnStrUtil.jsonObjStrNoBase("1", RspDesc, null);
				}
			} else {
				return ReturnStrUtil.jsonObjStrNoBase("1", "4G号码占用接口调用失败", null);
			}
		} catch (Exception e) {
			logger.error("4G号码占用接口异常", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "4G号码占用接口异常" + e, null);
		}
	}

	/**
	 * 4G号码预占-省内下沉（湖南CRM省湖南BSS省内应急选号预选省EOP包年）
	 *
	 * @param phone
	 * @param userCardId
	 * @return
	 */
	public static R supPreselectPhoneNbr(Map<String, Object> dataMap) {

		JSONObject body = new JSONObject();
		String accNbr = StringUtil.getMapByKey("acc_nbr", dataMap);
		body.put("transactionid", StringUtil.getMapByKey("cert_number", dataMap)); // 身份证号码代流水号预占释放时也是
		body.put("lan_id", CityUtil.lan_codes.get(StringUtil.getMapByKey("lan_id", dataMap))); // 本地网
		body.put("req_time", DateUtils.format(DateUtils.DATE_Y_M_D_H_M_S));
		body.put("staff_code", StringUtil.getMapByKey("staffCode", dataMap)); // 工号
		body.put("cert_number", StringUtil.getMapByKey("cert_number", dataMap)); // 身份号码
		body.put("channel_nbr", StringUtil.getMapByKey("saleBoxCode", dataMap)); // 渠道编码
		body.put("acc_nbr", accNbr); // 业务号码
		body.put("req_status_cd", "1102"); // 预约1103，预占1102
		body.put("password", StringUtil.getMapByKey("phoneApprovePassword", dataMap));// 靓号密码
//		body.put("pre_trsid", StringUtil.getMapByKey("cert_number", dataMap));//预占流水号

		String result = null;
		try {
			result = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/lte/supPreselectPhoneNbr", null, "UTF-8",
					"4G号码预占-省内下沉接口", body.toJSONString());

			JSONObject jsonObject = JSONObject.parseObject(result);
			String resCode = jsonObject.getString("code");
			String message = jsonObject.getString("message");
			if ("0000".equals(resCode)) {
				// 接口调用成功获取成功
				return R.ok(message);
			} else {
				return R.error(1, message);
			}
		} catch (Exception e) {
			logger.error("4G号码预占-省内下沉接口异常", e);
			return R.error(1, "4G号码预占-省内下沉接口异常" + e.getMessage());
		}
	}

	/**
	 * 4G号码释放-集团
	 *
	 * @param phone
	 * @param userCardId
	 * @return
	 */
	public static JSONObject accepcardreleaseFor4G(Map<String, Object> dataMap) {
		try {
			JSONObject map = new JSONObject();
			map.put("TransactionID", StringUtil.getTransactionID());
			map.put("ReqTime", DateUtils.format(DateUtils.DATE_Y_M_D_H_M_S));
			map.put("staffCode", StringUtil.getMapByKey("staffCode", dataMap));
			map.put("certNumber", StringUtil.getMapByKey("certNumber", dataMap));// 身份证号
			map.put("phoneNbr", StringUtil.getMapByKey("phoneNbr", dataMap));
			map.put("channelNbr", StringUtil.getMapByKey("saleBoxCode", dataMap));
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
			map.put("statusDate", simpleDateFormat.format(new Date()));
			map.put("PackageGroup", "kzj_1234567890");
			map.put("lanId", CityUtil.lan_codes.get(StringUtil.getMapByKey("lan_id", dataMap)));
			map.put("attr", StringUtil.getMapByKey("attr", dataMap));
			JSONObject root = new JSONObject();
			root.put("access_token", ACCESS_TOKEN);
			root.put("method", "order.res.accepcardreleaseFor4G");
			root.put("version", "1.0");
			root.put("content", map.toJSONString());
			logger.info("请求报文：" + root.toString());
			String result = "";
			result = HttpUtil.sendPost(EOP_URL, root.toJSONString(), "4G号码释放入参");
			if (StringUtils.isBlank(result)) {
				return ReturnStrUtil.jsonObjStrNoBase("1", "4G号码释放接口调用失败", null);
			}
			JSONObject jsonObject = JSONObject.parseObject(result);

			String resCode = jsonObject.getString("res_code");
			if ("00000".equals(resCode)) {
				// 接口调用成功获取成功
				JSONObject response = jsonObject.getJSONObject("result").getJSONObject("Body")
						.getJSONObject("exchangeResponse").getJSONObject("out").getJSONObject("ContractRoot")
						.getJSONObject("TcpCont").getJSONObject("Response");
				String rspCode = response.getString("RspCode");
				if ("0000".equals(rspCode)) {
					return ReturnStrUtil.jsonObjStrNoBase("0", "4G号码释放成功", null);
				} else {
					return ReturnStrUtil.jsonObjStrNoBase("1", "4G号码释放失败", null);
				}
			} else {
				return ReturnStrUtil.jsonObjStrNoBase("1", "4G号码释放失败", null);
			}
		} catch (Exception e) {
			logger.error("4G号码释放接口异常", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "4G号码释放接口异常" + e, null);
		}
	}

	/**
	 * 4G号码释放-省内下沉 湖南CRM省湖南BSS省内应急选号预选释放
	 * 
	 * @param phone
	 * @param userCardId
	 * @param cityCode
	 * @return String
	 * @date 2025-04-27 01:06:02
	 */
	public static R supRelasePhoneNbr(JSONObject dataMap) {

		JSONObject param = new JSONObject();
		param.put("lan_id", CityUtil.lan_codes.get(StringUtil.getMapByKey("lan_id", dataMap))); // 本地网
		param.put("status_cd", "1000"); // 新状态
		param.put("old_status_cd", "1102"); // 老状态
		param.put("deal_staff_code", StringUtil.getMapByKey("staffCode", dataMap));// 工号
		param.put("acc_nbr", StringUtil.getMapByKey("phoneNbr", dataMap)); // 业务号码
		param.put("pre_trsid", StringUtil.getMapByKey("certNumber", dataMap)); // 预占流水号，传身份证号码
		param.put("transactionid", StringUtil.getTransactionID()); // 释放流水号
		param.put("req_status_cd", "1000");
		param.put("channel_nbr", StringUtil.getMapByKey("saleBoxCode", dataMap)); // 渠道编码
		param.put("req_time", DateUtils.format(DateUtils.DATE_Y_M_D_H_M_S));

		String result = null;
		try {
			result = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/lte/supRelasePhoneNbr", null, "UTF-8",
					"4G号码释放-省内下沉接口", param.toJSONString());
			JSONObject jsonObject = JSONObject.parseObject(result.toString());
			String resCode = jsonObject.getString("code");
			String message = jsonObject.getString("message");
			if ("0000".equals(resCode)) {
				// 接口调用成功获取成功
				return R.ok(message);
			} else {
				return R.error(1, message);
			}
		} catch (Exception e) {
			logger.error("4G号码释放-省内下沉接口异常：", e);
			return R.error(1, "4G号码释放-省内下沉接口异常：" + e);
		}
	}

	/**
	 * 根据身份证查询预约信息
	 *
	 * @param cert_number
	 * @param cityCode
	 * @return
	 */
	public static R custPreInfo(String cert_number, String cityCode) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();
		JSONArray phoneArray = new JSONArray();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "qry.info.custPreInfo");
		body.put("version", "1.0");

		content.put("transactionid", StringUtil.getTransactionID());
		content.put("req_time", DateUtils.format(DateUtils.DATE_Y_M_D_H_M_S));
		content.put("lan_id", CityUtil.lan_codes.get(cityCode));
		content.put("cert_number", cert_number);
		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toString(), "根据身份证查询预约信息");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return R.error(1, restJson.getString("res_message"));
			} else {
				JSONObject response = restJson.getJSONObject("result").getJSONObject("TcpCont")
						.getJSONObject("Response");
				if ("1".equals(response.getString("RspType"))) {
					return R.ok(response.getString("RspDesc")).put("data", phoneArray);
				}

				JSONObject soo = restJson.getJSONObject("result").getJSONObject("SvcCont").getJSONObject("SOO");
				JSONObject pageRes = soo.getJSONObject("PUB_RES");
				if (pageRes.get("RspCode").equals("0000")) {
					String totalCount = soo.getJSONObject("PAGE_RES").getString("TOTAL_COUNT");
					if (totalCount.equals("1")) {
						soo.getJSONObject("PHONE_NBR_RES").put("phoneNbrLevelId", StringUtil.nbrLevel
								.get(soo.getJSONObject("PHONE_NBR_RES").get("PHONE_NBR_LEVEL_ID").toString()));

						JSONObject phoneNbrResObject = soo.getJSONObject("PHONE_NBR_RES");
						JSONObject preoccupiedPhoneObject = new JSONObject();
						preoccupiedPhoneObject.put("userpool", phoneNbrResObject.get("POOL_ID")); // 号池编码
						preoccupiedPhoneObject.put("userpoolName", phoneNbrResObject.get("POOL_NAME")); // 号池名称
						preoccupiedPhoneObject.put("phoneNbr", phoneNbrResObject.get("PHONE_NBR")); // 号码
						preoccupiedPhoneObject.put("phoneNbrPrice", phoneNbrResObject.get("PHONE_NBR_PRICE")); // 保底金额
						// PHONE_NBR_PRICE
						preoccupiedPhoneObject.put("preStore", phoneNbrResObject.get("PRESTORE"));// 预存款 PRESTORE
						preoccupiedPhoneObject.put("phoneNbrLevelId",
								StringUtil.nbrLevel.get(phoneNbrResObject.get("PHONE_NBR_LEVEL_ID").toString()));// 号码等级
						preoccupiedPhoneObject.put("lanId", phoneNbrResObject.get("LAN_ID"));

						preoccupiedPhoneObject.put("provinceId", phoneNbrResObject.getString("PROVINCE_ID").toString());
						preoccupiedPhoneObject.put("statusCd", phoneNbrResObject.getString("STATUS_CD").toString());
						preoccupiedPhoneObject.put("numLevelId", "");// 接口无返回
						preoccupiedPhoneObject.put("phoneNbrCharacterId",
								phoneNbrResObject.getString("PHONE_NBR_CHARACTER_ID").toString());
						preoccupiedPhoneObject.put("poolId", phoneNbrResObject.get("POOL_ID").toString());// 号池编码
						preoccupiedPhoneObject.put("poolName", phoneNbrResObject.get("POOL_NAME").toString());// 号池名称
						// 前端页面使用
						preoccupiedPhoneObject.put("checked", false);
						preoccupiedPhoneObject.put("id", 0);

						phoneArray.add(preoccupiedPhoneObject);
					} else {
						JSONArray preoccupiedPhoneList = new JSONArray();

						JSONArray phoneNbrResArra = soo.getJSONArray("PHONE_NBR_RES");
						for (int i = 0; i < phoneNbrResArra.size(); i++) {
							JSONObject a = phoneNbrResArra.getJSONObject(i);
							JSONObject preoccupiedPhoneObject = new JSONObject();
							preoccupiedPhoneObject.put("userpool", a.get("POOL_ID")); // 号池编码
							preoccupiedPhoneObject.put("userpoolName", a.get("POOL_NAME")); // 号池名称
							preoccupiedPhoneObject.put("phoneNbr", a.get("PHONE_NBR")); // 号码
							preoccupiedPhoneObject.put("phoneNbrPrice", a.get("PHONE_NBR_PRICE")); // 保底金额
							// PHONE_NBR_PRICE
							preoccupiedPhoneObject.put("preStore", a.get("PRESTORE"));// 预存款 PRESTORE
							preoccupiedPhoneObject.put("phoneNbrLevelId",
									StringUtil.nbrLevel.get(a.get("PHONE_NBR_LEVEL_ID").toString()));// 号码等级
							preoccupiedPhoneObject.put("lanId", a.get("LAN_ID"));

							preoccupiedPhoneObject.put("provinceId", a.getString("PROVINCE_ID").toString());
							preoccupiedPhoneObject.put("statusCd", a.getString("STATUS_CD").toString());
							preoccupiedPhoneObject.put("numLevelId", "");// 接口无返回
							preoccupiedPhoneObject.put("phoneNbrCharacterId",
									a.getString("PHONE_NBR_CHARACTER_ID").toString());
							preoccupiedPhoneObject.put("poolId", a.get("POOL_ID").toString());// 号池编码
							preoccupiedPhoneObject.put("poolName", a.get("POOL_NAME").toString());// 号池名称
							// 前端页面使用
							preoccupiedPhoneObject.put("checked", false);
							preoccupiedPhoneObject.put("id", i);
							preoccupiedPhoneList.add(preoccupiedPhoneObject);
						}

						phoneArray = preoccupiedPhoneList;
					}
					return R.ok("根据身份证查询预约信息成功").put("data", phoneArray);
				} else {
					return R.error(1, "根据身份证查询预约信息失败");
				}
			}
		} catch (Exception e) {
			logger.error("根据身份证查询预约信息异常：", e);
			return R.error(1, "根据身份证查询预约信息异常");
		}
	}

	/**
	 * 根据身份证查询预占号码-省内下沉 (湖南CRM省湖南BSS省内应急身份证预约选号查询)
	 * 
	 * @param cert_number
	 * @param cityCode
	 * @return R
	 * @date 2025-04-27 01:44:12
	 */
	public static R supQueryPrePhoneNbrs(String cert_number, String cityCode) {
		JSONObject param = new JSONObject();
		param.put("lan_id", CityUtil.lan_codes.get(cityCode));
		param.put("cert_number", cert_number);

		String rest = null;
		try {
			rest = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/lte/supQueryPrePhoneNbrs", null, "UTF-8",
					"根据身份证查询预占号码-省内接口", param.toJSONString());
			JSONObject restJson = JSONObject.parseObject(rest);
			if ("0000".equals(restJson.getString("code"))) {

				JSONArray preoccupiedPhoneList = new JSONArray();
				JSONArray phoneNbrResArra = restJson.getJSONArray("datas");

				if (phoneNbrResArra != null && phoneNbrResArra.size() > 0) {
					for (int i = 0; i < phoneNbrResArra.size(); i++) {
						JSONObject a = phoneNbrResArra.getJSONObject(i);
						JSONObject preoccupiedPhoneObject = new JSONObject();
						preoccupiedPhoneObject.put("userpool", a.get("pool_id")); // 号池编码
						preoccupiedPhoneObject.put("userpoolName", a.get("pool_name")); // 号池名称
						preoccupiedPhoneObject.put("phoneNbrCharacterId",
								a.getString("phone_nbr_character_id").toString());
						preoccupiedPhoneObject.put("phoneNbrLevelId",
								StringUtil.nbrLevel.get(a.get("phone_nbr_level_id").toString()));// 号码等级
						preoccupiedPhoneObject.put("phoneNbrPrice", a.get("phone_nbr_price")); // 保底金额
						preoccupiedPhoneObject.put("phoneNbr", a.get("phone_nbr")); // 号码
						preoccupiedPhoneObject.put("lanId", a.get("lan_id"));
						preoccupiedPhoneObject.put("provinceId", a.getString("province_id").toString());
						preoccupiedPhoneObject.put("statusCd", a.getString("status_cd").toString());
						preoccupiedPhoneObject.put("preStore", a.get("pre_store"));// 预存款 PRESTORE
						preoccupiedPhoneObject.put("numLevelId", "");// 接口无返回
						preoccupiedPhoneObject.put("poolId", a.get("pool_id").toString());// 号池编码
						preoccupiedPhoneObject.put("poolName", a.get("pool_name").toString());// 号池名称
						// 前端页面使用
						preoccupiedPhoneObject.put("checked", false);
						preoccupiedPhoneObject.put("id", i);
						preoccupiedPhoneList.add(preoccupiedPhoneObject);
					}

					return R.ok("根据身份证查询预约信息成功").put("data", preoccupiedPhoneList);
				} else {
					return R.error(1, "您名下暂未查询到预占号码");
				}
			} else {
				return R.error(1, restJson.getString("message"));
			}
		} catch (Exception e) {
			logger.error("根据身份证查询预约信息异常：", e);
			return R.error(1, "根据身份证查询预约信息异常");
		}
	}

	/**
	 * 查询资源固话选号池号码信息 DCOOS版
	 *
	 * @return Object
	 */
	public static JSONArray queryGuHDcoos(String lanId, String regionId, String returnCount, String phoneNoKey,
			String codeLevel, String orgId) {
		JSONArray GuhList = new JSONArray();

		String xml = "<Data> \n" + "<IntfCode>ResOcuNumQry</IntfCode> \n" + "<Params> \n" + "<LAN_ID>" + lanId
				+ "</LAN_ID>      \n" + "<REGION_ID>" + regionId + "</REGION_ID> \n"
				+ "<NUM_USAGE>6030500</NUM_USAGE> \n" + "<BUSI_AREA>" + orgId + "</BUSI_AREA>  \n"
				+ "<AGREE_NO>reer</AGREE_NO>  \n" + "<CONFIG_NUM>" + returnCount + "</CONFIG_NUM>  \n" + "<TELE_NO>"
				+ phoneNoKey + "</TELE_NO>  \n" + "<CODE_GRADE>" + codeLevel + "</CODE_GRADE> \n"
				+ "<PRODUCT_TYPE>80000000</PRODUCT_TYPE> \n" + "<Password></Password> \n" + "</Params> \n"
				+ "</Data> \n";
		StringBuffer rest = null;

		try {
			rest = HttpUtil.doServerHttpOpenapiXML(xml, NEW_DCOOS_URL + "/hnzynlzx.ResOcuNumQry/ResOcuNumQry",
					"查询资源固话选号池号码信息 DCOOSeop版");
			String result = rest.toString();
			// 转化JSON
			JSONObject GuH = XMLUtil.documentToJSONObject(result);

			// Object obj =
			// JSON.parse(GuH.getJSONObject("Params").get("PHONENOLIST").toString());
			JSONArray paramsArray = GuH.getJSONArray("Params");
			if ("-1".equals(paramsArray.getJSONObject(0).getString("RESULT"))) {
				return GuhList;
			}
			for (int j = 0; j < paramsArray.size(); j++) {
				JSONObject ParamsObj = paramsArray.getJSONObject(j);
				JSONArray jsonArray = ParamsObj.getJSONArray("PHONENOLIST");
				for (int i = 0; i < jsonArray.size(); i++) {
					JSONObject jsonObject = jsonArray.getJSONObject(i);
					JSONObject GuhMap = new JSONObject();
					GuhMap.put("telePhoneNbrPrice", (String) jsonObject.get("LIMITFEE"));
					GuhMap.put("memo", (String) jsonObject.get("MEMO"));
					GuhMap.put("telePhone", (String) jsonObject.get("PHONENO"));
					GuhMap.put("telePhonePrePrice", (String) jsonObject.get("PHONENOFEE"));
					/* GuhMap.put("SALE", (String) jsonObject.get("SALE_ID")); */
					GuhList.add(GuhMap);
				}
			}
		} catch (Exception e) {
			logger.error("查询资源固话选号池号码信息 DCOOS版异常：", e);
		}
		return GuhList;
	}

	/**
	 * 受理预占固话号码 DCOOS测试版
	 */
	public static JSONObject preGuHDcoos(String lanId, String regionId, String phoneNo, String idNo, String orgId,
			String staffId) {
		JSONObject restData = new JSONObject();
		String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" + "<Data>\n"
				+ "    <IntfCode>ResOcuNumOcc</IntfCode>\n" + "    <Params>\n" + "        <LAN_ID>" + lanId
				+ "</LAN_ID>\n" + "        <REGION_ID>" + regionId + "</REGION_ID>\n" + "        <BUSI_AREA>" + orgId
				+ "</BUSI_AREA>\n" + "        <OPERATOR>" + staffId + "</OPERATOR>\n"
				+ "        <PRODUCT_TYPE>80000000</PRODUCT_TYPE>\n" + "        <TELE_NO>" + phoneNo + "</TELE_NO>\n"
				+ "        <TYPE>3</TYPE>\n" + "        <Password>" + idNo + "</Password>\n"
				+ "        <SIMCARD></SIMCARD>\n" + "        <PSNM></PSNM>\n"
				+ "        <ISRESERVATION>0</ISRESERVATION>\n" + "    </Params>\n" + "</Data>";
		StringBuffer rest = null;

		try {
			rest = HttpUtil.doServerHttpOpenapiXML(xml, NEW_DCOOS_URL + "/hnzynlzx.resOcuNumOcc/resOcuNumOcc",
					"受理预占固话号码 DCOOSEOP版");
			String result = rest.toString();
			// 转化JSON
			JSONObject parse = XMLUtil.documentToJSONObject(result);
			String code = parse.getJSONArray("Params").getJSONObject(0).getString("RESULT");
			if ("0".equals(code)) {
				return ReturnStrUtil.jsonObjStrNoBase("0", "预占成功", null);
			} else {
				String message = (String) parse.getJSONArray("Params").getJSONObject(0).getString("RESULT_DESC");
				return ReturnStrUtil.jsonObjStrNoBase("1", message, null);
			}
		} catch (Exception e) {
			logger.error("受理预占固话号码 DCOOS测试版异常：", e);
			return ReturnStrUtil.jsonObjStrNoBase("1", "接口调用失败:" + e.getMessage(), null);
		}
	}

	/**
	 * 查询标准地址按地址名称（模糊查询） DCOOS测试版
	 *
	 * @param addressName  地址名称
	 * @param addrCount    查询条数
	 * @param regionCode   区域id，具体到市县
	 * @param addressLevel 地址等级
	 * @return
	 */
	public static R qryResInfoAddrSearchDcoos(String addressName, String addrCount, String regionCode,
			String addressLevel) {
		String xml = "<?xml version='1.0' encoding='utf-8'?>\n" + "<Data><IntfCode>qryResInfoAddrSearch</IntfCode>\n"
				+ "<Params>\n" + "<addressName>" + addressName + "</addressName> \n" + "<regionCode>" + regionCode
				+ "</regionCode> \n" + "<addrCount>" + addrCount + "</addrCount> \n" + "<addressLevel>" + addressLevel
				+ "</addressLevel> \n" + "<segmType>100100</segmType>\n" + "</Params></Data> ";
		StringBuffer rest = null;

		try {
			rest = HttpUtil.doServerHttpOpenapiXML(xml,
					NEW_DCOOS_URL + "/hnzynlzx.qryResInfoAddrSearch/addr/qryResInfoAddrSearch",
					"查询标准地址按地址名称（模糊查询） DCOOSeop版");
			String resultXML = rest.toString();
			// 转化JSON
			JSONObject restJson = XMLUtil.documentToJSONObject(resultXML);
			return R.ok("查询成功").put("data", restJson);

			/*
			 * if (!"00000".equals(restJson.getString("res_code")) ||
			 * !restJson.getString("res_message").equals("Success")) { return
			 * ReturnStrUtil.jsonObjStrNoBase("1", "未查询到当前地址，请重新输入地址", ""); } else {
			 * JSONObject result = restJson.getJSONObject("result"); JSONObject data =
			 * result.getJSONObject("Body").getJSONObject("crmWSInterfaceResponse")
			 * .getJSONObject("out").getJSONObject("Data"); if
			 * (!data.get("cou").equals("0")) { return ReturnStrUtil.jsonObjStrNoBase("0",
			 * "查询成功", data); } else { return ReturnStrUtil.jsonObjStrNoBase("1",
			 * "未查询到当前地址，请重新输入地址", ""); } }
			 */
		} catch (Exception e) {
			logger.error("查询标准地址按地址名称（模糊查询） DCOOS测试版异常：", e);
			return R.error(1, "查询标准地址按地址名称（模糊查询） DCOOS测试版异常");
		}
	}

	/**
	 * 判断地址是否具备宽带安装资格 DCOOS测试版
	 */
	public static R qryResForNetHallDcoos(String addr_id, String lanId) {
		Map<String, Object> resultMap = new HashMap<>();
		Map<String, Object> resultData = new HashMap<>();
		Map<String, String> resourceMap = new HashMap<>();
		JSONObject resource = null;
		String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" + "                <Data>\n"
				+ "                <IntfCode>qryResForNetHall</IntfCode>\n" + "                <Params>\n"
				+ "                <ADDR_ID>" + addr_id + "</ADDR_ID>\n" + "                </Params>\n"
				+ "                <lanId>" + lanId + "</lanId>\n" + "                </Data>";
		StringBuffer rest = null;

		JSONArray resourceArray = new JSONArray();
		try {
			rest = HttpUtil.doServerHttpOpenapiXML(xml, NEW_DCOOS_URL + "/preaddr/api/getPrejudgeResultForNetHall",
					"判断地址是否具备宽带安装资格 DCOOSeop版");
			String result = rest.toString();
			// 转化JSON
			JSONObject parse = XMLUtil.documentToJSONObject(result);
			JSONObject resInfo = parse.getJSONArray("Return").getJSONObject(0).getJSONArray("ResInfo").getJSONObject(0);
			resourceArray = resInfo.getJSONArray("INFO");
		} catch (Exception e) {
			logger.error("判断地址是否具备宽带安装资格DCOOS测试版接口异常：", e);
			return R.error(1, "接口调用失败");

		}
		return R.ok("接口调用成功").put("data", resourceArray);

	}

	/**
	 * 资源返回是否支持500M DCOOS版
	 *
	 * @param addrId
	 * @return
	 */
	public static R isSupport500MDcoos(String addrId, String isCity, String lanId) {

		String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" + "<Data>\n"
				+ "    <IntfCode>isSupport500M</IntfCode>\n" + "    <Params>\n" + "        <ADDR_ID>" + addrId
				+ "</ADDR_ID>\n" + "        <IsCity>" + isCity + "</IsCity>\n" + "        <TeleNo></TeleNo>\n"
				+ "    </Params>\n" + "    <lanId>" + lanId + "</lanId>\n" + "</Data>";
		StringBuffer rest = null;

		try {
			rest = HttpUtil.doServerHttpOpenapiXML(xml, NEW_DCOOS_URL + "/hnzynlzx.isSupport500M/other/isSupport500M",
					"资源返回是否支持500M DCOOSEOP版");
			String result = rest.toString();
			// 转化JSON
			JSONObject restJson = XMLUtil.documentToJSONObject(result);
			JSONObject Return = restJson.getJSONArray("Return").getJSONObject(0);
			if ("0".equals(Return.getString("Result"))) {
				String isSupport = Return.getString("IsSupport");
				return R.ok("查询成功").put("data", isSupport);
			} else {
				return R.error(1, "失败:" + Return.getString("ErrorDesc"));
			}
		} catch (Exception e) {
			logger.error("资源返回是否支持500M DCOOS版响应异常：", e);
			return R.error(1, "查询异常");
		}
	}

	/**
	 * BSS30-根据缆机名称模糊查询缆机信息供预受理使用ok
	 *
	 * @param staff_code
	 * @param lanId
	 * @return
	 */
	public static R qrytPartyInfoByPartyName(String party_name, String lanId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.cust.QrytPartyInfoByPartyName");
		body.put("version", "1.0");

		content.put("party_name", party_name);
		content.put("lan_id", lanId);
		content.put("page_size", "");
		content.put("page_index", "");
		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "根据缆机名称模糊查询缆机信息供预受理使用");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code")) || !restJson.getString("res_message").equals("Success")
					|| !restJson.getJSONObject("result").getString("Code").equals("0000")) {
				return R.error(1, "接口调用失败");
			} else {
				return R.ok("接口调用成功").put("data",
						restJson.getJSONObject("result").getJSONObject("QueryResults").getJSONArray("Result"));
			}
		} catch (Exception e) {
			logger.error("掌上销工具根据缆机名称模糊查询缆机信息供预受理使用接口异常：", e);
			return R.error(1, "接口调用失败");
		}
	}

	/**
	 * BSS30-根据缆机工号模糊查询缆机信息供预受理使用 ok
	 *
	 * @param staff_code
	 * @param lanId
	 * @return
	 */
	public static R qrytPartyInfoByPartyCode(String staff_code, String lanId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.cust.QrytPartyInfoByPartyCode");
		body.put("version", "1.0");

		content.put("party_code", staff_code);
		content.put("lan_id", lanId);
		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "根据缆机工号模糊查询缆机信息供预受理使用");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code")) || !restJson.getString("res_message").equals("Success")
					|| !restJson.getJSONObject("result").getString("Code").equals("0000")) {
				return R.error(1, "接口调用失败");
			} else {
				return R.ok("接口调用成功").put("data",
						restJson.getJSONObject("result").getJSONObject("QueryResults").getJSONArray("Result"));
			}
		} catch (Exception e) {
			logger.error("掌上销工具根据缆机工号模糊查询缆机信息供预受理使用接口异常：", e);
			return R.error(1, "接口调用失败");
		}
	}

	/**
	 * @param paramMap
	 * @return
	 * @return Map<String, Object>
	 * @throws Exception
	 * @Description 预下单- 使用方系统调用本接口在支付源服务后台生成预支付交易单， 用于WAP-H5支付、公众号支付、用户扫码支付的业务场景
	 * <AUTHOR>
	 * @Date 2020年4月20日下午3:53:00
	 */
	public static R zsxPayUrl(Map<String, String> paramMap) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		try {
			body.put("access_token", ACCESS_TOKEN);
			body.put("method", "pay.rechargepaye.ZsxPayUrl");
			body.put("version", "1.0");

			// 获取签名
			String sign = "";
			String key = "7675496122883984";
			String crmOrderId = paramMap.get("crmOrderId");
			String nowTime = DateUtils.getNowTime();// 获取当前时间，格式为yyyyMMddHHmmss
			double d = new Double(paramMap.get("totalFee")) * 100;// 将订单金额转成分
			int totalFee = new Double(d).intValue();

			// 验签参数
			SortedMap<String, String> signParam = new TreeMap<String, String>();
			signParam.put("accessChannel", "1003");// 使用方渠道(如厅网、营业厅、自助终端，来自 PAY_USER.USER_ID)
			signParam.put("tradeType", "1");// 交易类型(公众号（获取预支付交易会话标识），APP（获取预支付交易会话标识），用户扫码（二维码链接）;固定填 -1
			// PAY_SERIAL.TRADE_TYPE_ID)
			signParam.put("paySourceChannel", "111");// 支付源渠道(如微信、支付宝、翼支付等；对应 PAY_CHANNEL.PAY_CHANNEL_ID)
			signParam.put("openid", "1");// 用户标识(用户在支付源的标识。交易类型为公众号，APP时用对应 pay_serial.CUSTOMER_ACCOUNT)
			signParam.put("totalFee", totalFee + "");// 订单金额,单位分
			signParam.put("timeStart", nowTime);// 交易时间,格式为yyyyMMddHHmmss，可传空
			signParam.put("notifyUrl", paramMap.get("notifyUrl"));// 接收支付中心异步通知回调地址，通知url必须为直接可访问的url，不能携带参数
			signParam.put("goodsDesc",
					StringUtils.isEmpty(paramMap.get("goodsDesc")) ? "即时受理订单" : paramMap.get("goodsDesc"));// 商品描述,店名-销售商品类目
			signParam.put("commodityName", StringUtils.isEmpty(paramMap.get("commodityName")) ? "湖南电信掌上销-即时受理订单"
					: paramMap.get("commodityName"));// 商品名称
			signParam.put("payUserSerial", paramMap.get("payUserSerial"));// 使用方流水ID
			signParam.put("payNodeId", "*********");// 支付节点ID,收款的账号ID。 对应 PAY_NODE.PAY_NODE_ID
			signParam.put("tenantName", "支付中心");// 用于收款的商户名称
			signParam.put("nodePayMethodId", "*********");// 支付节点方式
			signParam.put("payChannelType", "12");// 支付渠道类型(1:银联支付2:翼支付3:支付宝4:微信支付5:快钱6:拉卡拉7:现金-1:未知12：支付网关)
			signParam.put("payMethodType", "4");// 支付方式类型(1:APP支付2:网页支付3:WAP支付4:二维码支付5:条码支付-1:未知)
			signParam.put("signType", "MD5");// 签名类型，比如HMAC-SHA256，MD5等，默认为MD5
			sign = StringUtil.getSign(signParam, key);

			// body
			JSONObject operAttrStruct = new JSONObject();// 操作人属性
			operAttrStruct.put("staffId", paramMap.get("staffId"));// 操作工号标识 ，测试工号103899525
			operAttrStruct.put("operOrgId", paramMap.get("orgId"));// 操作组织标识，测试************
			operAttrStruct.put("operTime", nowTime);// 操作时间
			operAttrStruct.put("operPost", "************");// 操作岗位
			operAttrStruct.put("operServiceId", "***********");// 业务流水标识
			operAttrStruct.put("lanId", paramMap.get("lanId"));// 操作人对应的区域，测试 731
			operAttrStruct.put("busiChannel", Constant.busiChannel);// 业务渠道编码 正式环境--->
			// busiChannel":"80001","accountId":"**********"测试环境---->
			// busiChannel:80002 accountId:**********
			operAttrStruct.put("accountId", Constant.accountId);// 操作岗位

			JSONObject svcObjectStruct = new JSONObject();// 服务对象条件
			svcObjectStruct.put("objType", "4");// 对象类型
			svcObjectStruct.put("objValue", "2");// 对象值
			svcObjectStruct.put("objAttr", "99");// 用户号码属性
			svcObjectStruct.put("dataArea", "3");// 数据范围

			content.put("operAttrStruct", operAttrStruct);// 操作人属性
			content.put("svcObjectStruct", svcObjectStruct);// 服务对象信息
			content.put("accessChannel", "1003");// 使用方渠道(如厅网、营业厅、自助终端，来自 PAY_USER.USER_ID)
			content.put("tradeType", "1");// 交易类型(公众号（获取预支付交易会话标识），APP（获取预支付交易会话标识），用户扫码（二维码链接）;固定填 -1
			// PAY_SERIAL.TRADE_TYPE_ID)
			content.put("paySourceChannel", "111");// 支付源渠道(如微信、支付宝、翼支付等；对应 PAY_CHANNEL.PAY_CHANNEL_ID)
			content.put("openid", "1");// 用户标识(用户在支付源的标识。交易类型为公众号，APP时用对应 pay_serial.CUSTOMER_ACCOUNT)
			content.put("totalFee", totalFee + "");// 订单金额,单位分
			content.put("timeStart", nowTime);// 交易时间,格式为yyyyMMddHHmmss，可传空
			content.put("notifyUrl", paramMap.get("notifyUrl"));// 接收支付中心异步通知回调地址，通知url必须为直接可访问的url，不能携带参数
			content.put("goodsDesc",
					StringUtils.isEmpty(paramMap.get("goodsDesc")) ? "即时受理订单" : paramMap.get("goodsDesc"));// 商品描述,店名-销售商品类目
			content.put("commodityName", StringUtils.isEmpty(paramMap.get("commodityName")) ? "湖南电信掌上销-即时受理订单"
					: paramMap.get("commodityName"));// 商品名称
			content.put("payUserSerial", paramMap.get("payUserSerial"));// 使用方流水ID
			content.put("payNodeId", "*********");// 支付节点ID,收款的账号ID。 对应 PAY_NODE.PAY_NODE_ID
			content.put("tenantName", "支付中心");// 用于收款的商户名称
			content.put("nodePayMethodId", "*********");// 支付节点方式
			content.put("payChannelType", "12");// 支付渠道类型(1:银联支付2:翼支付3:支付宝4:微信支付5:快钱6:拉卡拉7:现金-1:未知12：支付网关)
			content.put("payMethodType", "4");// 支付方式类型(1:APP支付2:网页支付3:WAP支付4:二维码支付5:条码支付-1:未知)
			content.put("sign", sign);// 签名
			content.put("signType", "MD5");// 签名类型，比如HMAC-SHA256，MD5等，默认为MD5

			// 掌上销接口定义
			content.put("contact_tel", "");// 联系人电话
			content.put("lan_id", paramMap.get("lanId"));// 地市编码
			content.put("staff_id", paramMap.get("staffId"));// 工号id
			content.put("openid", "1");// 用户标识
			content.put("accept_type", "001");// 掌上销订单类型
			content.put("org_subtype", paramMap.get("orgSubtype"));
			content.put("order_id", crmOrderId);// CRM订单号
			logger.info("客户id: " + paramMap.get("custId"));
			content.put("cust_id", paramMap.get("custId"));// 客户id
			if (StringUtils.isNotEmpty(paramMap.get("coupon_flag"))) {
				content.put("coupon_flag", paramMap.get("coupon_flag"));// 优惠券抵扣标识
			}
			body.put("content", content);

			String rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "支付中心预下单");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getJSONObject("result").get("error_code").equals("0000")) {
				return R.error(1, "受理生成掌上销二维码支付链接地址接口失败");
			} else {
				JSONObject bodyJson = JSONObject.parseObject(restJson.getJSONObject("result").getString("body"));
				if (bodyJson.get("resultCode").equals("0")) {
					return R.ok("受理生成掌上销二维码支付链接地址接口成功").put("data", bodyJson.get("codeUrl"));
				} else {
					return R.error(1, bodyJson.getString("resultMsg"));
				}
			}
		} catch (Exception e) {
			logger.error("支付中心预下单接口异常", e);
			return R.error(1, "受理生成掌上销二维码支付链接地址接口异常");
		}
	}

	/**
	 * 台席支付 调用ord.order.preorder 扣除风险金
	 *
	 * @param condition
	 * @return
	 */
	public static R txPre_order(Map<String, Object> condition) {
		Map<String, Object> paramMap = new HashMap<>();
		JSONObject content = new JSONObject();

		JSONObject root = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject request = new JSONObject();
		JSONObject params = new JSONObject();

		paramMap.put("access_token", ACCESS_TOKEN);
		paramMap.put("method", "bss.ord.order.preorder");
		paramMap.put("version", "1.0");
		paramMap.put("staff_code", StringUtil.getMapByKey("staff_code", condition));
		paramMap.put("deviceId", "");
		paramMap.put("staff_id", StringUtil.getMapByKey("staff_id", condition));
		paramMap.put("lan_id", StringUtil.getMapByKey("lan_id", condition));

		content.put("lan_id", StringUtil.getMapByKey("lan_id", condition));
		params.put("lan_id", StringUtil.getMapByKey("lan_id", condition));
		params.put("isUpdatePay", StringUtil.getMapByKey("isUpdatePay", condition));
		params.put("order_id", StringUtil.getMapByKey("order_id", condition));
		params.put("log_id", StringUtil.getMapByKey("log_id", condition));
		params.put("staff_id", StringUtil.getMapByKey("staff_id", condition));
		params.put("pay_method", "100000");

		header.put("ExchangeId", getExchangeId("HNZSX0"));
		header.put("BizCode", "PreOrder");
		header.put("ClientId", "HNZSX0");
		header.put("Password", "123456");
		root.put("Header", header);

		request.put("params", params);
		root.put("Request", request);

		content.put("Root", root);
		paramMap.put("content", content);

		String jsonString = JSONObject.toJSONString(paramMap);
		try {
			String result = HttpUtil.doPost(EOP_URL, jsonString, "台席支付 修改风险金");
			JSONObject parse = JSONObject.parseObject(result);
			String res_code = parse.getString("res_code");
			if (StringUtils.isEmpty(res_code) || !res_code.equals("00000")) { // res_code
				return R.error(1, "预存金支付接口调用失败");
			}
			JSONObject resultObj = parse.getJSONObject("result");
			if (resultObj != null && "0000".equals(resultObj.getString("code"))) {
				return R.ok("预存金支付成功").put("data", resultObj);
			} else {
				return R.error(1, resultObj.getString("message"));
			}
		} catch (Exception e) {
			logger.error("台席支付 修改风险金 接口异常", e);
			return R.error(1, "预存金支付接口异常：" + e.getMessage());
		}
	}

	/**
	 * 人证比对
	 *
	 * @param condition
	 * @return
	 * @throws IOException
	 */
	public static R chkfaceVerify(AmazonS3 amazonS3, JSONObject condition) throws IOException {
		logger.info("人证比对接口入参>：{}", condition.toJSONString());
		// 免冠照数据转化
		String image_best = condition.getString("identityCardImage3");
		image_best = getImgBase64(amazonS3, image_best);

		// 身份证免冠二寸照
		String image_idcard = condition.getString("image_idcard");
		image_idcard = getImgBase64(amazonS3, image_idcard);

		String cleartext = "{'staff_code':'" + CityUtil.cityMap.get(condition.get("cityCode")) + "','channel_nbr':'"
				+ CityUtil.cityMapChannel.get(condition.get("cityCode"))
				+ "','channel_type':'110301','busi_type':'34','opt_name':'" + condition.get("customerName")
				+ "','opt_certnum':'" + condition.get("customerCard")
				+ "','area_id':'8430100','province_code':'8430000','cust_id':'','party_name':'"
				+ condition.get("customerName")
				+ "','gender':'','nation':'','born_day':'','cert_address':'','cert_number':'"
				+ condition.get("customerCard")
				+ "','cert_org':'','eff_date':'','exp_date':'','finger_print':'','image_idcard':'" + image_idcard
				+ "'}";
		cleartext = cleartext.replaceAll("\'", "\"");

		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();
		body.put("method", "chk.auth.ChkfaceVerify");
		body.put("access_token", ACCESS_TOKEN);
		body.put("version", "1.0");
		content.put("params", cleartext);
		content.put("app_id", "crm");
		content.put("image_best", image_best);
		content.put("AppKey", "6001020001");
		body.put("content", content);

		try {
			String result = HttpUtil.doPost(EOP_URL, body.toString(), "掌上销工具人证比对接口");
			JSONObject parse = JSONObject.parseObject(result);
			String res_code = parse.getString("res_code");
			if (!"00000".equals(res_code)) {
				return R.error(1, "接口调用异常");
			}
			String result_code = parse.getJSONObject("result").getJSONObject("ContractRoot").getJSONObject("SvcCont")
					.getString("result_code");
			if ("0".equals(result_code)) {
				String confidence = parse.getJSONObject("result").getJSONObject("ContractRoot").getJSONObject("SvcCont")
						.getString("confidence");
				return R.ok("成功").put("data", confidence);
			} else {
				String error_msg = parse.getJSONObject("result").getJSONObject("ContractRoot").getJSONObject("SvcCont")
						.getString("error_msg");
				return R.error(1, error_msg);
			}

		} catch (Exception e) {
			logger.error("掌上销工具人证比对接口异常", e);
			return R.error(1, "接口调用异常");
		}
	}

	/**
	 * 同步人证比对照片给CRM
	 *
	 * @param similarity
	 * @param lan_id
	 * @param image_idcard
	 * @param image_best
	 * @return
	 */
	public static R synCustCertInfoToCrm(AmazonS3 amazonS3, JSONObject condition) {
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("access_token", ACCESS_TOKEN);
		paramMap.put("method", "bss.syn.photo.SynCustCertInfoToCrm");
		paramMap.put("version", "1.0");
		paramMap.put("deviceId", "");
		paramMap.put("lan_id", StringUtil.getMapByKey("lan_id", condition));
		paramMap.put("staff_code", StringUtil.getMapByKey("manual_staffcode", condition));
		paramMap.put("staff_id", StringUtil.getMapByKey("staffId", condition));

		JSONObject param = new JSONObject();
		// 免冠照数据转化
		String image_usersidcard = "";
		String image_agentidcard = "";
		String image_best = "";
		String image_idcard = "";
		if (StringUtil.isNotNull(StringUtil.getMapByKey("image_best", condition))) {
			image_best = getImgBase64(amazonS3, condition.getString("image_best"));// 人证比对免冠照
		}
		if (StringUtil.isNotNull(StringUtil.getMapByKey("image_idcard", condition))) {
			image_idcard = getImgBase64(amazonS3, condition.getString("image_idcard"));// 客户身份证免冠二寸照
		}
		if (StringUtil.isNotNull(StringUtil.getMapByKey("image_usersidcard", condition))) {
			image_usersidcard = getImgBase64(amazonS3, condition.getString("image_usersidcard"));// 使用人份证免冠二寸照
		}
		if (StringUtil.isNotNull(StringUtil.getMapByKey("image_agentidcard", condition))) {
			image_agentidcard = getImgBase64(amazonS3, condition.getString("image_agentidcard"));// 经办人身份证免冠二寸照
		}

		StringBuffer image_data = new StringBuffer();
		if ("2".equals(StringUtil.getMapByKey("custType", condition))) {// 政企客户
			image_data.append(image_usersidcard).append("IMGSTR").append(image_best).append("IMGSTR")
					.append(image_agentidcard);
		} else {// 公众客户(个人客户)
			image_data.append(image_idcard).append("IMGSTR").append(image_best);
		}
		if (image_data.toString().contains("uploads")) {
			return R.error(1, "图片base64串获取不到");
		}
		param.put("image_data", image_data);
		param.put("similarity", StringUtil.getMapByKey("similarity", condition));
		param.put("lan_id", StringUtil.getMapByKey("lan_id", condition));
		param.put("handle_certi_number", StringUtil.getMapByKey("customerCard", condition));

		param.put("ExchangeId", "");
		param.put("manual_staffcode", StringUtil.getMapByKey("manual_staffcode", condition));
		String transactionid = StringUtil.getMapByKey("transactionid", condition);

		// 一号一拍新增
		if (1 == condition.getInteger("isOneBeat")) {
			param.put("acc_num", StringUtil.getMapByKey("acc_num", condition));
			param.put("cust_order_id", StringUtil.getMapByKey("cust_order_id", condition));
			param.put("is_h5", "1");
		} else {
			if (!StringUtil.isEmpty(transactionid)) {
				param.put("transactionid", "");
				param.put("cust_order_id", StringUtil.getMapByKey("cust_order_id", condition));
			} else {
				param.put("pre_info_id", StringUtil.getMapByKey("cust_order_id", condition));
			}
		}

		paramMap.put("content", param.toJSONString());
		String jsonString = JSONObject.toJSONString(paramMap);
		String result = "";
		// logger.info("掌上销工具同步人证比对照片给CRM接口请求参数" + jsonString);
		try {
			result = HttpUtil.doPost(EOP_URL, jsonString, "掌上销工具同步人证比对照片给CRM");
			logger.info("掌上销工具同步人证比对照片给CRM接口返回参数" + result);
			JSONObject parse = JSONObject.parseObject(result);
			String res_code = parse.getString("res_code");
			if (!"00000".equals(res_code) && !parse.getJSONObject("result").getString("Code").equals("0000")) {
				return R.error(1, "接口调用失败");
			}
			JSONObject prodInfo = (JSONObject) parse.getJSONObject("result").getJSONObject("result")
					.getJSONObject("Result");

			return R.ok("接口调用成功").put("data", prodInfo);
		} catch (Exception e) {
			logger.error("掌上销工具同步人证比对照片给CRM接口异常", e);
			return R.error(1, "接口调用失败");
		}
	}

	/**
	 * syn.custinfo.SynIdardInfo 同步识别仪读取的证件信息给CRM（掌上销用）
	 */
	public static JSONObject syncIdardInfo(Map<String, Object> condition, AmazonS3 amazonS3) {
		Map<String, Object> paramMap = new HashMap<>();
		JSONObject content = new JSONObject();
		paramMap.put("access_token", ACCESS_TOKEN);
		paramMap.put("method", "bss.syn.custinfo.SynIdardInfo");
		paramMap.put("version", "1.0");
		content.put("lan_id", StringUtil.getMapByKey("lan_id", condition)); // 本地网
		content.put("certiNumber", StringUtil.getMapByKey("certiNumber", condition)); // 身份证号码
		content.put("activityLTo", StringUtil.getMapByKey("activityLTo", condition)); // 有效截至日期
		content.put("staff_id", StringUtil.getMapByKey("staffId", condition)); // 工号
		content.put("custName", StringUtil.getMapByKey("custName", condition)); // 姓名
		content.put("birthDay", StringUtil.getMapByKey("birthDay", condition)); // 生日
		content.put("address", StringUtil.getMapByKey("address", condition)); // 地址
		String customerCardImg = "";
		String[] parts1 = StringUtil.getMapByKey("customerCardImg", condition).split("/");
		if (parts1.length == 3) {
			customerCardImg = FileUtil.getImageBytess(amazonS3, parts1[0], parts1[1] + "/" + parts1[2]);
		} else {
			customerCardImg = FileUtil.getImageBytess(amazonS3, parts1[0], parts1[1]);
		}

		if (condition.get("type").toString().equals("2")) {
			String[] parts2 = StringUtil.getMapByKey("identityCardImage5", condition).split("/");
			String identityCardImage5 = "";
			if (parts2.length == 3) {
				identityCardImage5 = FileUtil.getImageBytess(amazonS3, parts2[0], parts2[1] + "/" + parts2[2]);
			} else {
				identityCardImage5 = FileUtil.getImageBytess(amazonS3, parts2[0], parts2[1]);
			}

			customerCardImg = identityCardImage5 + "IMGSTR" + customerCardImg;
		}

		content.put("image", customerCardImg);
		content.put("deviceId", "");
		content.put("ExchangeId", "");
		paramMap.put("content", content);
		String jsonString = JSONObject.toJSONString(paramMap);
		try {
			String result = HttpUtil.sendPost(EOP_URL, jsonString, "同步识别仪读取的证件信息给CRM（掌上销用）接口");
			JSONObject parse = JSONObject.parseObject(result);
			String res_code = parse.getString("res_code");
			if (StringUtils.isBlank(res_code) || !res_code.equals("00000")) { // res_code
				// 返回为空或者
				// 返回值不为00000
				// 则接口调用失败
				return ReturnStrUtil.jsonObjStrNoBase("1", "接口调用失败", null);
			}
			return ReturnStrUtil.jsonObjStrNoBase("0", "接口调用成功", null);
		} catch (Exception e) {
			logger.error("同步识别仪读取的证件信息给CRM（掌上销用）接口异常");
			return ReturnStrUtil.jsonObjStrNoBase("1", "接口调用失败", "");
		}
	}

	/**
	 * 暂存单删除
	 * 
	 * @param orderNo         订单号
	 * @param custId          客户ID
	 * @param lanId           地市编码
	 * @param orgId           组织ID
	 * @param staffId         工号
	 * @param sceneInstIdList 暂存单ID列表
	 * @return 返回结果对象，包含状态和详细信息
	 */
	public static JSONObject deleteOrderCancelOrderForH5(String orderNo, String custId, String lanId, String orgId,
			String staffId, JSONArray sceneInstIdList) {
		JSONObject resultObj = new JSONObject();
		resultObj.put("success", false);
		resultObj.put("message", "");

		try {
			// 构建请求参数
			JSONObject para = new JSONObject();
			para.put("custId", custId);
			para.put("lanId", lanId);
			para.put("sceneInstIdList", sceneInstIdList);
			para.put("orgId", orgId);
			para.put("staffId", staffId);
			para.put("token", orderNo);

			// 记录请求参数
			resultObj.put("requestParams", para.toJSONString());

			// 调用接口
			String result = HttpUtil
					.doServerHttpOpenapiNew(para.toString(), NEW_DCOOS_URL + "/hncrmdis/cancelOrderForH5", "订单中暂存单取消")
					.toString();

			// 记录响应结果
			resultObj.put("responseResult", result);

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("resultCode"))) {
				boolean success = restObject.getBoolean("resultObject");
				resultObj.put("success", success);
				resultObj.put("message", success ? "暂存单删除成功" : "暂存单删除失败");
			} else {
				resultObj.put("success", false);
				resultObj.put("message", restObject.getString("resultMsg"));
			}
		} catch (Exception e) {
			resultObj.put("success", false);
			resultObj.put("message", "暂存单删除异常: " + e.getMessage());
			logger.error("订单{}，暂存单删除异常: {}", orderNo, e.getMessage(), e);
		}

		return resultObj;
	}

	/**
	 * 湖南CRM收费确认订单免填单生成与查询接口
	 *
	 * @param params 包含以下参数: busi_type - 业务类型 sys_source - 系统来源 cust_order_id -
	 *               客户订单号 add_mbk - 是否添加营销品 add_mbk_name - 营销品名称
	 * @return 返回接口响应结果
	 */
	public static R getPdf(JSONObject params) {
		try {
			String url = NEW_DCOOS_URL + "/hnCrm/getPdf/getPdf";
			String jsonString = params.toJSONString();
			String result = HttpUtil.httpClientdoPost(url, null, "UTF-8", "湖南CRM收费确认订单免填单生成与查询接口", jsonString);
//			logger.info("湖南CRM收费确认订单免填单生成与查询接口返回：{}", result);
			JSONObject restObject = JSONObject.parseObject(result);
			if ("0000".equals(restObject.getString("result_code"))) {
				return R.ok().put("data", restObject.getJSONObject("contact"));
			} else {
				return R.error(1, restObject.getString("result_msg"));
			}
		} catch (Exception e) {
			logger.error("湖南CRM收费确认订单免填单生成与查询接口异常：", e);
			return null;
		}
	}

	/**
	 * H5查询订单信息接口
	 *
	 * @param params 包含以下参数: qry_type - 查询类型 1:客户订单号 2:证件号码 3:业务号码 qry_content -
	 *               查询内容 lan_id - 地市编码 staff_id - 工号ID
	 * @return 返回接口响应结果
	 */
	public static JSONObject qryOrderList(JSONObject params) {
		try {
			String url = NEW_DCOOS_URL + "/H5qryOrderInfo/H5qryOrderInfo";
			String jsonString = params.toJSONString();
			String result = HttpUtil.httpClientdoPost(url, null, "UTF-8", "H5查询订单信息接口", jsonString);
//			logger.info("H5查询订单信息接口返回：{}", result);

			if (StringUtils.isNotBlank(result)) {
				JSONObject resultJson = JSONObject.parseObject(result);
				// 判断接口是否成功
				if (resultJson != null && "0".equals(resultJson.getString("resultCode"))) {
					// 接口调用成功，返回resultObject
					return resultJson;
				} else {
					// 接口调用失败，返回错误信息
					return resultJson;
				}
			}
			return null;
		} catch (Exception e) {
			logger.error("H5查询订单信息接口异常：", e);
			return null;
		}
	}

	/**
	 * 线上线下订单详情查询接口
	 *
	 * @param params 包含以下参数: cust_order_id - 客户订单号
	 * @return 返回接口响应结果
	 */
	public static JSONObject qryOrderDetailInfo(JSONObject params) {
		try {
			String url = NEW_DCOOS_URL + "/H5qryOrderDetailInfo/H5qryOrderDetailInfo";
			// String result = HttpUtil.sendPost(url, params.toJSONString(),
			// "线上线下订单详情查询接口");
			String result = HttpUtil.httpClientdoPost(url, null, "UTF-8", "线上线下订单详情查询接口", params.toJSONString());
//			logger.info("线上线下订单详情查询接口返回：{}", result);

			if (StringUtils.isNotBlank(result)) {
				JSONObject resultJson = JSONObject.parseObject(result);
				// 判断接口是否成功
				if (resultJson != null && "0".equals(resultJson.getString("resultCode"))) {
					// 接口调用成功，返回resultObject
					return resultJson;
				} else {
					// 接口调用失败，返回错误信息
					return resultJson;
				}
			}
			return null;
		} catch (Exception e) {
			logger.error("H5线上线下订单详情查询接口异常：", e);
			return null;
		}
	}

	/**
	 * 查询终端信息接口
	 *
	 * @param objId 网络摄像头（终端标识）
	 * @return 返回接口响应结果
	 */
	public static R qryMktResInfo(String objId) {
		try {
			// 构建GET请求的URL
			String baseUrl = NEW_DCOOS_URL + "/hncrm/qryMktResInfo";
			String url = baseUrl + "?obj_id=" + objId;
			String result = HttpUtil.httpClientdoGet(url, null, "UTF-8", "查询终端信息接口");
			logger.info("查询终端信息接口返回：{}", result);

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0000".equals(restObject.getString("code"))) {
				return R.ok().put("data", restObject.getJSONArray("mkt_res_info"));
			} else {
				return R.error(1, restObject.getString("reason"));
			}

		} catch (Exception e) {
			logger.error("查询终端信息接口异常：", e);
			return R.error(1, "查询终端信息接口异常" + e.getMessage());
		}
	}

	/**
	 * 查询暂存单费用
	 *
	 * @return 返回接口响应结果
	 */
	public static R qryFeeInfo(JSONObject params) {
		try {
			// 构建GET请求的URL
			String baseUrl = NEW_DCOOS_URL + "/hncrm/getAcctItemFee";
			String url = baseUrl + "?sceneInstId=" + params.getString("sceneInstId") + "&custId="
					+ params.getString("custId") + "&lanId=" + params.getString("lanId") + "&staffId="
					+ params.getString("staffId");
			logger.info("查询暂存单费用入参：{}", params);
			String result = HttpUtil.httpClientdoGet(url, null, "UTF-8", "查询终端信息接口");
			logger.info("查询暂存单费用返回：{}", result);
			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("resultCode"))) {
				return R.ok().put("data", restObject.getJSONArray("resultObject"));
			} else {
				return R.error(1, restObject.getString("resultMsg"));
			}
		} catch (Exception e) {
			logger.error("查询暂存单费用接口异常：", e);
			return R.error(1, "查询暂存单费用接口异常" + e.getMessage());
		}
	}

	/**
	 * 湖南BSS客户联系人修改接口
	 *
	 * @param params 包含以下参数: lan_id - 地市编码 cust_id - 客户ID contact_id - 联系人ID
	 *               contact_name - 联系人姓名 email - 邮箱 contact_addr - 联系地址
	 *               mobile_phone - 手机号码 contact_action_type - 联系人操作类型(M: 修改; A: 新增)
	 *               enter_contact_type - [可选] 联系人类型，当contact_action_type=A时需要
	 * @return 返回接口响应结果
	 */
	public static R updateCustContactInfo(JSONObject params) {
		try {
			String custId = params.getString("cust_id");
			String url = NEW_DCOOS_URL + "/crm/custContactInfoUpd/custContactInfo/" + custId;

			// 如果是新增联系人且未设置联系人类型，默认添加
			if ("A".equals(params.getString("contact_action_type")) && !params.containsKey("enter_contact_type")) {
				params.put("enter_contact_type", "2020401201"); // 联系人类型
			}
			// 使用HttpUtil发送PATCH请求
			String result = HttpUtil.sendPatch(url, params.toJSONString(), "湖南BSS客户联系人修改接口", null);
			logger.info("湖南BSS客户联系人修改接口返回：{}", result);
			JSONObject restObject = JSONObject.parseObject(result);
			if ("0000".equals(restObject.getString("code"))) {
				return R.ok().put("data", restObject.getJSONObject("contact"));
			} else {
				return R.error(1, restObject.getString("message"));
			}
		} catch (Exception e) {
			logger.error("湖南BSS客户联系人修改接口异常：", e);
			return R.error(1, "湖南BSS客户联系人修改异常：" + e.getMessage());
		}
	}

	// public static void main(String[] args) {
	// JSONObject da = new JSONObject();
	// da.put("cust_id", "804129106200");
	//
	// // 要添加的键值对
	// da.put("lan_id", "731");
	// da.put("contact_id", "98220908");
	// da.put("contact_name", "陈歆");
	// da.put("email", "");
	// da.put("contact_addr", "");
	// da.put("mobile_phone", "17388981012");
	// da.put("contact_action_type", "M");
	//
	// System.out.println(updateCustContactInfo(da));
	// }

	/**
	 * 湖南BSS客户联系人查询接口
	 *
	 * @param custId 客户ID
	 * @return 返回接口响应结果
	 */
	public static R queryCustContactInfo(String custId) {
		try {
			// 构建GET请求的URL
			String url = NEW_DCOOS_URL + "/crm/custContactInfoSel/custContactInfo/" + custId;

			// 使用HttpUtil的httpClientdoGet方法发送GET请求
			String result = HttpUtil.sendGet(url, "UTF-8", "湖南BSS客户联系人查询接口");
			logger.info("湖南BSS客户联系人查询接口返回：{}", result);
			return R.ok().put("data", JSONObject.parseArray(result));

		} catch (Exception e) {
			logger.error("湖南BSS客户联系人查询接口异常：", e);
			return null;
		}
	}

	/**
	 * POST查询橙分期订单信息根据身份证号码1.0 （竣工状态）
	 *
	 * @param cert_no   身份证
	 * @param cust_name 姓名
	 * @param fqType    分期类型编码
	 */
	public static R qrySspPayOrderInfo(String cert_no, String cust_name) {
		logger.info("查询橙分期订单信息根据身份证号码入参--> accNum:" + cert_no + ", lanId:" + cust_name + "");
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "qry.order.QrySspPayOrderInfo");
		body.put("version", "1.0");

		content.put("test_flag", "0");
		content.put("req_trans_id", "");
		content.put("req_date", "");
		content.put("req_date_time", "");
		content.put("cert_no", cert_no);
		content.put("cust_name", cust_name);

		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "POST查询橙分期订单信息根据身份证号码1.0 ");
			JSONObject restJson = JSONObject.parseObject(rest);
			JSONObject result = restJson.getJSONObject("result");
			if (!"00000".equals(restJson.getString("res_code")) || !restJson.getString("res_message").equals("Success")
					|| !"010A00".equals(result.getJSONObject("sspPay").getJSONObject("body").getString("rspCode"))) {
				return R.error(1, "未找到相关信息");
			}
			JSONArray resultArray = result.getJSONObject("sspPay").getJSONObject("body").getJSONArray("orderInfos");
			logger.info("查询橙分期订单信息接口出参-->resultArray：" + resultArray);
			return R.ok("查询橙分期订单信息成功").put("data", resultArray);
		} catch (Exception e) {
			logger.error("查询橙分期订单信息查询接口异常", e);
			return R.error(1, "查询橙分期订单信息异常:" + e.getMessage());
		}
	}

	/**
	 * BSS30_查询终端串码状态
	 *
	 * @param mkt_res_inst_nbr 串码
	 * @param lanId            本地网id
	 * @return
	 */
	public static R qryMktResStatus(String mkt_res_inst_nbr, String lanId) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "bss.qry.resinfo.QryMktResStatus");
		body.put("version", "1.0");
		content.put("mkt_res_inst_nbr", mkt_res_inst_nbr);
		content.put("lan_id", lanId);
		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toString(), "BSS30_查询终端串码状态");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return R.error(1, restJson.getString("res_message"));
			} else {
				JSONArray result = restJson.getJSONObject("result").getJSONObject("QueryResults")
						.getJSONArray("result");
				if (result.size() > 0) {
					String status_cd = result.getJSONObject(0).getString("status_cd");
					if (status_cd.equals("1000")) {
						return R.ok("串码可用").put("data", result.get(0));
					} else {
						return R.error(1, mkt_res_inst_nbr + "串码无效，请确认终端串码正确，且已入库未使用。");
					}
				} else {
					return R.error(1, mkt_res_inst_nbr + "串码无效，请确认终端串码正确，且已入库未使用。");
				}
			}
		} catch (Exception e) {
			logger.error("BSS30_查询终端串码状态接口异常：", e);
			return R.error(1, "BSS30_查询终端串码状态接口异常");
		}
	}

	/**
	 * 同步订单以及费用信息给掌上销
	 *
	 * @return
	 */
	public static Map<String, Object> synAddOrderInfo(Map<String, Object> condition) {
		logger.info("掌上销工具订单同步ZSX接口入参:condition=" + condition.get("acc_num") + "——————" + condition);
		JSONObject body = new JSONObject();
		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "syn.orderinfo.SynAddOrderInfo");
		body.put("version", "1.0");

		JSONObject content = new JSONObject();
		content.put("deviceOS", StringUtil.getMapByKey("deviceOS", condition));// 系统版本
		content.put("deviceType", StringUtil.getMapByKey("deviceType", condition));// 系统信息
		content.put("acc_nbr", StringUtil.getMapByKey("acc_nbr", condition));// 业务号码
		content.put("acceptType", StringUtil.getMapByKey("acceptType", condition));// 订单类型
		content.put("custId", StringUtil.getMapByKey("custId", condition));// 客户id
		content.put("custName", StringUtil.getMapByKey("custName", condition));// 客户名
		content.put("lanId", StringUtil.getMapByKey("lanId", condition));// 本地网id
		content.put("orgId", StringUtil.getMapByKey("orgId", condition));// 组织id
		content.put("regionId", StringUtil.getMapByKey("regionId", condition));// 区域id
		content.put("serv_id", StringUtil.getMapByKey("serv_id", condition));// 产品实例ID
		content.put("staffId", StringUtil.getMapByKey("staffId", condition));// 工号id
		content.put("staffName", StringUtil.getMapByKey("staffName", condition));// 工号名称
		content.put("status", StringUtil.getMapByKey("status", condition));// 订单状态
		content.put("statusDate", StringUtil.getMapByKey("statusDate", condition));// 时间
		content.put("targetOrderId", StringUtil.getMapByKey("targetOrderId", condition));// 订单号
		content.put("org_subtype", StringUtil.getMapByKey("orgSubtype", condition));// 标记代理商
		content.put("ExchangeId", getExchangeId("mmarkt"));
		content.put("method", "OrderService.addOrderInfo");// 方法名
		content.put("version", "1.0");// 版本号
		content.put("sj_order", StringUtil.getMapByKey("sj_order", condition));// 商机订单号
		JSONArray feeList = new JSONArray();
		if (StringUtil.isNotNull(StringUtil.getMapByKey("feeValue", condition))) {
			JSONObject feeData = new JSONObject();
			feeData.put("feeValue", StringUtil.getMapByKey("feeValue", condition));
			feeData.put("feeId", "100016");
			feeData.put("feeTypeId", "");
			feeList.add(feeData);
		}
		content.put("feeList", feeList);
		body.put("content", content);
//		logger.info("掌上销工具订单同步ZSX接口请求参数：" + body);
		try {
			String result = HttpUtil.httpClientdoPost(EOP_URL, null, "UTF-8", "同步订单以及费用信息给掌上销", body.toJSONString());
//			logger.info("掌上销工具订单同步ZSX接口返回参数：" + result);
			JSONObject parse = JSONObject.parseObject(result);
			String res_code = parse.getString("res_code");
			if ("00000".equals(res_code) && parse.getJSONObject("result").getString("error_code").equals("0000")) {
				JSONObject prodInfo = parse.getJSONObject("result");
				return R.ok("接口调用成功");
			}
			return R.error(1, "接口调用失败");
		} catch (Exception e) {
			logger.error("掌上销工具订单同步ZSX接口异常");
			return R.error(1, "接口调用失败");
		}
	}

	/**
	 * 无纸化工单状态查询 补拍补签 DCOOS版
	 * 
	 * @param orderId 预受理订单号
	 * @param token
	 * @return
	 */
	public static R queryOrderStatusDCOOS(Map<String, Object> condition) {
		JSONObject body = new JSONObject();

		String orderId = StringUtil.getMapByKey("orderId", condition);
		String strTimestamp = String.valueOf(new Date().getTime());
		String strHashCode = null;
		try {
			strHashCode = MD5Util.MD5(orderId + strTimestamp + "!@##@!");
		} catch (NoSuchAlgorithmException e) {
			logger.error(orderId + ">>>无纸化工单状态查询 补拍补签 生成密钥串失败: " + e.getMessage());
		}

		// 接口入参
		body.put("orderId", orderId);// 预受理订单号
		body.put("strTimestamp", strTimestamp);// 时间戳
		body.put("strHashCode", strHashCode);// 密钥串
		StringBuffer rest = null;
		JSONObject resultDataObj = new JSONObject();
		try {
			rest = HttpUtil.doServerHttpOpenapiNew(JSON.toJSONString(body),
					NEW_DCOOS_URL + "/queryStatus/queryOrderStatus", "无纸化工单状态查询");
			JSONObject restJson = JSONObject.parseObject(rest.toString());
			if (restJson.getString("result") == null || !"0".equals(restJson.getString("result"))
					|| !"200".equals(restJson.getString("code"))) {
				return R.error(1, restJson.getString("msg"));
			} else {
				JSONObject resultObjs = restJson.getJSONObject("data");
				if (null == resultObjs || "".equals(resultObjs.toJSONString())) {
					resultDataObj.put("isSign", "0");
					resultDataObj.put("isPhoto", "0");
					return R.error(1, "接口调用成功");
				} else {
					resultDataObj.put("isSign", resultObjs.getString("isSign"));
					resultDataObj.put("isPhoto", resultObjs.getString("isPhoto"));
					return R.ok("接口调用成功").put("data", resultDataObj);
				}
			}
		} catch (Exception e) {
			logger.error("无纸化工单状态查询请求参数 接口报错", e);
			return R.error(1, "接口调用失败" + e.getMessage());
		}
	}

	/**
	 * H5保存号码信息
	 * 
	 * @param params
	 * @return JSONObject
	 * @date 2025-04-23 02:50:31
	 */
	public static R savePhoneRes(JSONObject params) {
		try {
			String url = NEW_DCOOS_URL + "/hncrm/savePhoneRes";
			String result = HttpUtil.httpClientdoPost(url, null, "UTF-8", "H5保存号码信息接口", params.toJSONString());

			if (StringUtils.isNotBlank(result)) {
				JSONObject resultJson = JSONObject.parseObject(result);
				if ("0".equals(resultJson.getString("resultCode"))) {
					boolean resultObject = resultJson.getBoolean("resultObject");
					if (resultObject) {
						return R.ok();
					} else {
						return R.error(1, resultJson.getString("resultMsg"));
					}
				} else {
					return R.error(1, "保存号码失败，请重新选择");
				}

			}
			return null;
		} catch (Exception e) {
			logger.error("H5线上线下订单详情查询接口异常：", e);
			return null;
		}
	}

	/**
	 * 查询客户欠费信息
	 *
	 * @param custId
	 * @return
	 */
	public static R queryArrearsList(String custId) {

		JSONObject param = new JSONObject();
		param.put("instanceType", "1");
		param.put("instanceId", custId);
		param.put("requestTime", DateUtils.sfm.format(new Date()));// 时间戳
		param.put("requestId", String.valueOf(new Date().getTime()));

		String result = null;
		try {
			result = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/oweQueryCRM/oweQueryCRM", null, "UTF-8", "查询客户欠费信息",
					param.toJSONString());
		} catch (Exception e) {
			logger.error("查询客户欠费异常：" + e.getMessage());
		}
		JSONObject parseObject = JSONObject.parseObject(result.toString());
		String resCode = parseObject.getString("code");
		if (resCode == null || !"0".equals(resCode)) {
			return R.error(1, "接口调用失败");
		}
		String oweFlag = parseObject.getString("oweFlag");
		if (oweFlag == null) {
			return R.error(1, "接口调用失败");
		} else {
			return R.ok("接口调用成功").put("data", parseObject);
		}
	}

	/**
	 * H5查询当前客户可使用的审批单详细信息
	 * 
	 * @param params
	 * @return R
	 * @date 2025-04-25 12:44:50
	 */
	public static R h5qryApproveApp(JSONObject params) {
		try {
			// 构建GET请求的URL
			String baseUrl = NEW_DCOOS_URL + "/hncrm/H5qryApproveApp";
			String url = baseUrl + "?regionId=" + params.getString("regionId") + "&acctItemTypeId="
					+ params.getString("acctItemTypeId") + "&custId=" + params.getString("custId") + "&lanId="
					+ params.getString("lanId") + "&approveNumber=" + params.getString("approveNumber") + "&staffId="
					+ params.getString("staffId");
			String result = HttpUtil.httpClientdoGet(url, null, "UTF-8", "H5查询当前客户可使用的审批单详细信息接口");

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("resultCode"))) {
				return R.ok().put("data", restObject.getJSONArray("resultObject"));
			} else {
				return R.error(1, restObject.getString("resultMsg"));
			}

		} catch (Exception e) {
			logger.error("H5查询当前客户可使用的审批单详细信息接口异常：", e);
			return R.error(1, "H5查询当前客户可使用的审批单详细信息接口异常" + e.getMessage());
		}
	}

	/**
	 * 查询当前客户下退订的套餐促销包实例列表包年
	 * 
	 * @param params
	 * @return R
	 * @date 2025-04-25 03:31:36
	 */
	public static R LISTCUSTOFFINSTS(JSONObject params) {
		try {
			String result = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/LISTCUSTOFFINSTS", null, "UTF-8",
					"查询当前客户下退订的套餐促销包实例列表包年", params.toJSONString());

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("resultCode"))) {
				return R.ok().put("data", restObject.getJSONArray("resultObject"));
			} else {
				return R.error(1, restObject.getString("resultMsg"));
			}

		} catch (Exception e) {
			logger.error("查询当前客户下退订的套餐促销包实例列表包年接口异常：", e);
			return R.error(1, "查询当前客户下退订的套餐促销包实例列表包年接口异常" + e.getMessage());
		}
	}

	/**
	 * 批量退订销售品实例
	 * 
	 * @param params
	 * @return R
	 * @date 2025-04-25 03:31:36
	 */
	public static R BATCHREMOVEOFFINSTS(JSONObject params) {
		try {
			String result = HttpUtil.httpClientdoPost(NEW_DCOOS_URL + "/hncrm/BATCHREMOVEOFFINSTS", null, "UTF-8",
					"批量退订销售品实例", params.toJSONString());

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("resultCode"))) {
				return R.ok().put("data", restObject.getBoolean("resultObject"));
			} else {
				return R.error(1, restObject.getString("resultMsg"));
			}

		} catch (Exception e) {
			logger.error("H5查询当前客户可使用的审批单详细信息接口异常：", e);
			return R.error(1, "H5查询当前客户可使用的审批单详细信息接口异常" + e.getMessage());
		}
	}

	/**
	 * wm 2024-05-14 微信信用授权获取token接口
	 *
	 * @return
	 */
	public static R getToken() {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "qry.hbwx.getToken");
		body.put("version", "1.0");

		content.put("customerNo", Constant.customerNo);
		body.put("content", content);
		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toJSONString(), "微信信用授权获取token接口");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return R.error(1, "微信信用授权获取token接口失败");
			} else {
				JSONObject result = restJson.getJSONObject("result");
				if (result.get("returnCode").equals("10000")) {
					String decryptStr = Des3Util
							.desDecrypt((String) result.get("token"), Constant.keyHex, Constant.ivHex).trim();
					return R.ok("微信信用授权获取token接口成功").put("data", decryptStr);
				} else {
					return R.error(1, "微信信用授权获取token接口失败");
				}
			}
		} catch (IOException e) {
			logger.info("微信信用授权获取token接口异常：", e);
			return R.error(1, "微信信用授权获取token接口异常：" + e.getMessage());
		} catch (Exception e) {
			logger.info("微信信用授权token解密异常：", e);
			return R.error(1, "微信信用授权token解密异常:" + e.getMessage());
		}
	}

	/**
	 * 查询微信信用授权结果（线上渠道）
	 * 
	 * @<NAME_EMAIL>
	 * @param paramDesStr
	 * @return R
	 * @date 2025-04-29 07:16:15
	 */
	public static R queryResult(String paramDesStr) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();
		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "qry.hbwx.queryResult");
		body.put("version", "1.0");
		content.put("customerNo", Constant.customerNo);
		content.put("paramJson", paramDesStr);
		body.put("content", content);

		String rest = null;
		try {
			rest = HttpUtil.sendPost(EOP_URL, body.toString(), "查询微信信用授权结果（线上渠道）接口");
			JSONObject restJson = JSONObject.parseObject(rest);
			if (restJson.getInteger("res_code") == 00000) {
				return R.ok("查询固网担保订单状态成功").put("data", restJson.getJSONObject("result"));
			} else {
				return R.error(1, "查询固网担保订单状态失败");
			}
		} catch (Exception e) {
			logger.info("查询微信信用授权结果（线上渠道）接口异常：", e);
			return R.error(1, "查询微信信用授权结果（线上渠道）接口异常");
		}

	}

	/**
	 * 湖南账务中心_查询代理商代办点余额省EOP
	 * 
	 * @param sysUserId 工号id
	 * @return R
	 * @date 2025-05-06 09:25:50
	 */
	public static R queryAgentDepositAmount(String sysUserId) {
		try {
			JSONObject params = new JSONObject();
			params.put("requestTime", DateUtils.sfm_.format(new Date()));
			params.put("requestId", String.valueOf(new Date().getTime()));
			params.put("sysUserId", sysUserId);

			String result = HttpUtil.httpClientdoPost(
					NEW_DCOOS_URL + "/queryAgentDepositAmount/queryAgentDepositAmount", null, "UTF-8",
					"湖南账务中心_查询代理商代办点余额省EOP接口", params.toJSONString());

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("code"))) {
				String balanceInFen = restObject.getString("balance");
				if (balanceInFen == null || balanceInFen.isEmpty()) {
					return R.error(1, "您的风险金余额为0，请选择二维码支付！");
				}
				try {
					BigDecimal fen = new BigDecimal(balanceInFen);
					BigDecimal hundred = new BigDecimal("100");
					return R.ok().put("balance", fen.divide(hundred, 2, BigDecimal.ROUND_DOWN));
				} catch (NumberFormatException e) {
					logger.info("预存金额转换异常，请反馈到支撑群处理: " + balanceInFen);
					return R.error(1, "预存金额转换异常，请反馈到支撑群处理！");
				}
			} else {
				return R.error(1, restObject.getString("message"));
			}

		} catch (Exception e) {
			logger.error("湖南账务中心_查询代理商代办点余额省EOP接口异常：", e);
			return R.error(1, "查询预存金接口异常" + e.getMessage());
		}
	}

	public static void main1(String[] args) {
		JSONObject prObject = new JSONObject();
		prObject.put("regionId", "74501");
		prObject.put("acctItemTypeId", "3,7");
		prObject.put("custId", "274544973034");
		prObject.put("lanId", "745");
		prObject.put("approveNumber", "");
		prObject.put("staffId", "200012955923");
		System.out.println(h5qryApproveApp(prObject));
//		PREORDERCHECK(null);
	}

	public static BigDecimal convertFenToYuan(String balanceInFen) {
		if (balanceInFen == null || balanceInFen.isEmpty()) {
			return BigDecimal.ZERO;
		}
		try {
			BigDecimal fen = new BigDecimal(balanceInFen);
			BigDecimal hundred = new BigDecimal("100");
			return fen.divide(hundred, 2, BigDecimal.ROUND_DOWN);
		} catch (NumberFormatException e) {
			System.err.println("输入的字符串无法转换为数字: " + balanceInFen);
			return BigDecimal.ZERO;
		}
	}

	/**
	 * 查询用户微信订单接口 接口功能描述:根据证件号，业务号码，查询用户微信竣工订单信息
	 *
	 * @param phone 冻结业务号码
	 * @param idnum 冻结证件号
	 * @return
	 * @throws Exception
	 * @throws UnsupportedEncodingException
	 */
	public static R queryCancelExamTosafe(JSONObject params) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();
		JSONObject paramJson = new JSONObject();
		try {
			body.put("access_token", ACCESS_TOKEN);
			body.put("method", "syn.hbsafe.cancelExamTosafe");
			body.put("version", "1.0");

			paramJson.put("token", System.currentTimeMillis());
			paramJson.put("phone", params.getString("phone"));
			paramJson.put("idnum", params.getString("customerCard"));
			paramJson.put("type", params.getString("type"));

			String paramDesStr = Des3Util
					.desEncrypt(paramJson.toJSONString().getBytes("GBK"), Constant.newKeyHex, Constant.ivHex).trim();

			content.put("customerNo", "HUNAN_ELEC");
			content.put("paramJson", paramDesStr);

			body.put("content", content);

			logger.info("查询用户微信订单接口入参:" + body.toJSONString());
			String result = HttpUtil.httpClientdoPost(EOP_URL, null, "UTF-8", "查询用户微信订单接口", body.toJSONString());
			logger.info("查询用户微信订单接口响应:" + result);

			JSONObject restJson = JSONObject.parseObject(result);
			if (restJson.getJSONObject("result").get("returnCode").equals("10000")) {
				return R.ok("查询成功").put("data", JSONArray
						.parseArray(JSON.toJSONString(restJson.getJSONObject("result").getJSONArray("items"))));
			} else if (restJson.getJSONObject("result").get("returnCode").equals("20001")) {
				return R.error(1, restJson.getJSONObject("result").getString("message"));
			}
			return R.error(1, restJson.getJSONObject("result").getString("message"));
		} catch (Exception e) {
			logger.error("查询用户微信订单接口: ", e);
			return R.error(1, "查询异常" + e.getMessage());
		}
	}

	/**
	 * 微信信用授权二维码获取接口（线上渠道)
	 *
	 * @return
	 */
	public static R createOrder(String paramDesStr) {
		JSONObject body = new JSONObject();
		JSONObject content = new JSONObject();

		body.put("access_token", ACCESS_TOKEN);
		body.put("method", "syn.hbwx.createOrder");
		body.put("version", "1.0");

		content.put("customerNo", Constant.customerNo);
		content.put("paramJson", paramDesStr);
		body.put("content", content);

		logger.info("微信信用授权二维码获取接口（线上渠道)接口请求报文：" + body.toString());
		try {
			String rest = HttpUtil.httpClientdoPost(EOP_URL, null, "UTF-8", "微信信用授权二维码获取接口（线上渠道)接口",
					body.toJSONString());
			logger.info("微信信用授权二维码获取接口（线上渠道)接口返回报文：" + rest);
			JSONObject restJson = JSONObject.parseObject(rest);
			if (!"00000".equals(restJson.getString("res_code"))
					|| !restJson.getString("res_message").equals("Success")) {
				return R.error("微信信用授权二维码获取接口（线上渠道)接口失败");
			} else {
				JSONObject result = restJson.getJSONObject("result");
				if (result.get("returnCode").equals("10000")) {
					return R.ok("微信信用授权二维码获取接口（线上渠道)接口成功").put("data", result);
				} else {
					return R.error("微信信用授权二维码获取接口（线上渠道)接口失败");
				}
			}
		} catch (Exception e) {
			logger.error("微信信用授权获取token接口异常：", e);
			return R.error("微信信用授权二维码获取接口（线上渠道)接口异常" + e.getMessage());
		}
	}

	/**
	 * HNBSS掌上销查询查验结果(携号转网结果查询)
	 * 
	 * @param accNbr
	 * @param lanId
	 * @return
	 */
	public static R zsxGetAuthRsp(String accNbr, String lanId) {
		try {
			String url = NEW_DCOOS_URL + "/bss.zsxGetAuthRsp/zsxGetAuthRsp?accNbr=" + accNbr + "&lanId=" + lanId;
			String result = HttpUtil.httpClientdoGet(url, null, "UTF-8", "HNBSS掌上销查询查验结果(携号转网结果查询)接口");
			JSONObject restJson = JSONObject.parseObject(result);
			if (result.length() <= 2) {
				return R.error(1, "未查询到数据");
			}
			return R.ok("查询成功").put("data", restJson);
		} catch (Exception e) {
			logger.info("HNBSS掌上销查询查验结果(携号转网结果查询)接口异常：", e);
			return R.error(1, "HNBSS掌上销查询查验结果(携号转网结果查询)接口异常:" + e.getMessage());
		}
	}

	/**
	 * 号码查询用户的基本信息
	 *
	 * @param phone    业务号码
	 * @param cityCode 本地网编码
	 * @param proId    产品类型id
	 * @return Map<String, Object> 用户基本信息
	 */
	public static R queryCustByNumber(String phone, String cityCode, String proId) {
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("access_token", ACCESS_TOKEN);
		paramMap.put("method", "bss.qry.cust.QueryCustByNumber");
		paramMap.put("version", "1.0");

		JSONObject param = new JSONObject();
		param.put("acc_num", phone);
		param.put("lan_id", cityCode);
		param.put("prod_id", proId);

		paramMap.put("content", param.toJSONString());
		String jsonString = JSONObject.toJSONString(paramMap);
		try {
			String result = HttpUtil.httpClientdoPost(EOP_URL, null, "UTF-8", "号码查询用户的基本信息接口", jsonString);
			JSONObject parse = JSONObject.parseObject(result);
			String res_code = parse.getString("res_code");
			if (!"00000".equals(res_code) && !parse.getJSONObject("result").getString("Code").equals("0000")) {
				return R.error(1, "号码查询用户的基本信息失败");
			} else {
				JSONArray customerInfo = (JSONArray) parse.getJSONObject("result").getJSONObject("QueryResults")
						.getJSONArray("CustomerInfo");
				return R.ok("号码查询用户的基本信息成功").put("data", customerInfo);
			}
		} catch (Exception e) {
			logger.error("号码查询用户的基本信息接口异常", e);
			return R.error(1, "号码查询用户的基本信息接口异常:" + e.getMessage());
		}
	}

	/**
	 * 协同告警发送机器人消息
	 *
	 * @param msg
	 */
	public static void sendRobotMsg(String sendContent) {
		try {
			JSONObject para = new JSONObject();
			para.put("msgtype", "text");
			JSONObject text = new JSONObject();
			text.put("content", sendContent);
			para.put("text", text);
			HttpUtil.doPost(ROBOT_URL, para.toString(), "协同告警发送机器人消息");
		} catch (Exception e) {
			logger.error("协同告警发送机器人消息异常", e);
		}
	}

	public static void main(String[] args) {
//		String[] balances = { "1973577", "0", "100", "80", "" };
//		for (String balance : balances) {
//			BigDecimal yuan = convertFenToYuan(balance);
//			System.out.println("分: " + balance + ", 元: " + yuan);
//		}
//		JSONObject ff = new JSONObject();
//		ff.put("idnum", "430721198907220308");
//		ff.put("type", "newQuery");
//		ff.put("phone", "");
//		System.out.println(queryCancelExamTosafe(ff));
	}

	/**
	 * 查询客户群号关系
	 * 
	 * @param custId 客户ID
	 * @param lanId  本地网ID
	 * @return R
	 * @date 2025-06-20
	 * <AUTHOR>
	 */
	public static R qryCustomerGroupRelation(String custId, String lanId) {
		try {
			logger.info("查询客户群号关系入参: custId={}, lanId={}", custId, lanId);

			// 构建请求URL
			String requestUrl = NEW_DCOOS_URL + "/bss.eCProduct/eCProduct";

			// 添加请求参数
			StringBuilder urlBuilder = new StringBuilder(requestUrl);
			urlBuilder.append("?custId=").append(custId);
			urlBuilder.append("&lanId=").append(lanId);

			// 发送GET请求
			String result = HttpUtil.httpClientdoGet(urlBuilder.toString(), null, "UTF-8", "查询客户群号关系接口");
			logger.info("查询客户群号关系接口返回: {}", result);

			if (StringUtils.isBlank(result)) {
				return R.error(1, "查询客户群号关系接口返回为空");
			}

			// 解析返回结果，先判断返回的是对象还是数组
			JSONArray resultArray;
			try {
				// 尝试解析为JSON对象
				JSONObject resultObj = JSONObject.parseObject(result);
				// 如果是对象，看是否包含data字段
				if (resultObj.containsKey("data")) {
					resultArray = resultObj.getJSONArray("data");
				} else {
					// 如果不包含data字段，可能是错误信息
					String message = resultObj.getString("message");
					if (StringUtils.isNotBlank(message)) {
						return R.error(1, message);
					}
					return R.error(1, "查询客户群号关系接口返回格式异常");
				}
			} catch (Exception e) {
				// 如果不是JSON对象，尝试直接解析为JSON数组
				try {
					resultArray = JSONArray.parseArray(result);
				} catch (Exception e2) {
					logger.error("解析接口返回结果异常", e2);
					return R.error(1, "解析接口返回结果异常: " + e2.getMessage());
				}
			}

			if (resultArray == null || resultArray.isEmpty()) {
				return R.ok().put("data", new JSONArray());
			}

			// 筛选符合条件的记录
			JSONArray filteredData = new JSONArray();
			for (int i = 0; i < resultArray.size(); i++) {
				JSONObject item = resultArray.getJSONObject(i);
				// 筛选条件: prodInstStatus为"100000"且productId为"********"的记录
				if ("100000".equals(item.getString("prodInstStatus"))
						&& "********".equals(item.getString("productId"))) {
					// 只保留需要的字段
					JSONObject filteredItem = new JSONObject();
					filteredItem.put("account", item.getString("id"));
					filteredItem.put("serialNumber", item.getString("serialNumber"));

					// 格式化时间
					String startDateStr = item.getString("startDate");
					if (StringUtils.isNotBlank(startDateStr)) {
						try {
							// 解析ISO 8601格式的时间
							SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
							isoFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
							Date date = isoFormat.parse(startDateStr);

							// 格式化
							SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
							outputFormat.setTimeZone(TimeZone.getDefault());
							String formattedDate = outputFormat.format(date);

							filteredItem.put("startDate", formattedDate);
						} catch (Exception e) {
							// 如果解析失败，使用原始格式
							filteredItem.put("startDate", startDateStr);
							logger.warn("格式化时间失败，使用原始格式: {}", startDateStr);
						}
					} else {
						filteredItem.put("startDate", "");
					}

					filteredData.add(filteredItem);
				}
			}

			return R.ok().put("data", filteredData);
		} catch (Exception e) {
			logger.error("查询客户群号关系异常", e);
			return R.error(1, "查询客户群号关系异常: " + e.getMessage());
		}
	}

	/**
	 * 终端回收信息查询接口
	 * 
	 * @param params 请求参数
	 * @return R
	 * @date 2025-01-27
	 */
	public static R qryTerminalRecycleInfo(JSONObject params) {
		try {
			// 调用接口
			String result = HttpUtil.httpClientdoPost(
					NEW_DCOOS_URL+"/crm/h5CommonSvc/qryTerminalRecycleInfo",
				null,
				"UTF-8", 
				"终端回收信息查询接口", 
				params.toJSONString()
			);

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("resultCode"))) {
				return R.ok().put("data", restObject.getJSONArray("resultObject"));
			} else {
				return R.error(1, restObject.getString("resultMsg"));
			}

		} catch (Exception e) {
			logger.error("终端回收信息查询接口异常：", e);
			return R.error(1, "终端回收信息查询接口异常" + e.getMessage());
		}
	}

	/**
	 * 新增终端回收接口
	 * 
	 * @param params 请求参数
	 * @return R
	 * @date 2025-01-27
	 */
	public static R dealTerminalRecycleInfo(JSONObject params) {
		try {
			// 调用接口
			String result = HttpUtil.httpClientdoPost(
				NEW_DCOOS_URL + "/crm/h5CommonSvc/dealTerminalRecycleInfo", 
				null,
				"UTF-8", 
				"新增终端回收接口", 
				params.toJSONString()
			);

			JSONObject restObject = JSONObject.parseObject(result);
			if ("0".equals(restObject.getString("resultCode"))) {
				return R.ok().put("data", restObject.getString("resultObject"));
			} else {
				return R.error(1, restObject.getString("resultMsg"));
			}

		} catch (Exception e) {
			logger.error("新增终端回收接口异常：", e);
			return R.error(1, "新增终端回收接口异常" + e.getMessage());
		}
	}
}