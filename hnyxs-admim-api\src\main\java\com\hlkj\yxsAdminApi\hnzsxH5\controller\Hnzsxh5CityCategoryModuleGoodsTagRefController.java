package com.hlkj.yxsAdminApi.hnzsxH5.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.common.core.constant.SysLogConstant;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5CityCategoryModuleGoodsTagRef;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5GoodsTag;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5CityCategoryModuleGoodsTagRefParam;
import com.hlkj.yxsAdminApi.hnzsxH5.service.Hnzsxh5CityCategoryModuleGoodsTagRefService;
import com.hlkj.yxsAdminApi.hnzsxH5.service.Hnzsxh5GoodsTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * H5即时受理 - （(地市分类关系表 - 对应 - 模块表（关系表）) - 对应 - 商品标签信息表）（关系表）控制器
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Api(tags = "H5即时受理-模块-商品标签关系管理")
@RestController
@RequestMapping("/api/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref")
public class Hnzsxh5CityCategoryModuleGoodsTagRefController extends BaseController {
    @Autowired
    private Hnzsxh5CityCategoryModuleGoodsTagRefService hnzsxh5CityCategoryModuleGoodsTagRefService;
    
    @Autowired
    private Hnzsxh5GoodsTagService hnzsxh5GoodsTagService;

    @OperationLog(value = "分页查询模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("分页查询模块-商品标签关系")
    @PostMapping("/page")
    public ApiResult<PageResult<Hnzsxh5CityCategoryModuleGoodsTagRef>> page(@RequestBody Hnzsxh5CityCategoryModuleGoodsTagRefParam param) {
        PageParam<Hnzsxh5CityCategoryModuleGoodsTagRef, Hnzsxh5CityCategoryModuleGoodsTagRefParam> page = new PageParam<>(param);
        return success(hnzsxh5CityCategoryModuleGoodsTagRefService.page(page, page.getWrapper()));
    }

    @OperationLog(value = "查询全部模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("查询全部模块-商品标签关系")
    @PostMapping("/getListByParam")
    public ApiResult<List<Hnzsxh5CityCategoryModuleGoodsTagRef>> list(@RequestBody Hnzsxh5CityCategoryModuleGoodsTagRefParam param) {
        PageParam<Hnzsxh5CityCategoryModuleGoodsTagRef, Hnzsxh5CityCategoryModuleGoodsTagRefParam> page = new PageParam<>(param);
        return success(hnzsxh5CityCategoryModuleGoodsTagRefService.list(page.getOrderWrapper()));
    }

    @OperationLog(value = "根据ID查询模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("根据id查询模块-商品标签关系")
    @PostMapping("/getById")
    public ApiResult<?> get(@RequestBody Hnzsxh5CityCategoryModuleGoodsTagRefParam param) {
        if(param.getId() == null){
            return fail("请确认传入的id是否为空");
        }
        return success(hnzsxh5CityCategoryModuleGoodsTagRefService.getById(param.getId()));
    }

    @OperationLog(value = "添加模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("添加模块-商品标签关系")
    @PostMapping("/avoid/insert")
    public ApiResult<?> save(@RequestBody Hnzsxh5CityCategoryModuleGoodsTagRef relation) {
        if (hnzsxh5CityCategoryModuleGoodsTagRefService.save(relation)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @OperationLog(value = "修改模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("修改模块-商品标签关系")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody Hnzsxh5CityCategoryModuleGoodsTagRef relation) {
        if (hnzsxh5CityCategoryModuleGoodsTagRefService.updateById(relation)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @OperationLog(value = "批量添加模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("批量添加模块-商品标签关系")
    @PostMapping("/avoid/batchInsert")
    public ApiResult<?> saveBatch(@RequestBody List<Hnzsxh5CityCategoryModuleGoodsTagRef> list) {
        if (hnzsxh5CityCategoryModuleGoodsTagRefService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @OperationLog(value = "批量修改模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("批量修改模块-商品标签关系")
    @PostMapping("/batchUpdate")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<Hnzsxh5CityCategoryModuleGoodsTagRef> batchParam) {
        if (batchParam.update(hnzsxh5CityCategoryModuleGoodsTagRefService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 批量更新标签排序rank
     */
    @PostMapping("/batchUpdateRank")
    @ApiOperation("批量更新标签排序")
    public ApiResult<?> batchUpdateRank(@RequestBody List<Hnzsxh5CityCategoryModuleGoodsTagRef> list) {
        boolean ok = hnzsxh5CityCategoryModuleGoodsTagRefService.batchUpdateRank(list);
        return ok ? success("批量更新成功") : fail("批量更新失败");
    }

    /**
     * 删除模块-商品标签关系
     * @param param 包含id的参数
     * @return 删除结果
     * <AUTHOR>
     * @date 2025-05-15
     */
    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleGoodsTagRef:remove')")
    @OperationLog(value = "删除模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("删除模块-商品标签关系")
    @PostMapping("/delete")
    public ApiResult<?> delete(@RequestBody Map<String, Integer> param) {
        Integer id = param.get("id");
        if (id == null) {
            return fail("关系ID不能为空");
        }
        
        if (hnzsxh5CityCategoryModuleGoodsTagRefService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 根据模块关系ID查询关联的商品标签信息
     * @param param 参数，包含cityCategoryModuleRefId
     * @return 商品标签信息列表
     */
    @OperationLog(value = "查询模块关联的商品标签信息", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("根据模块关系ID查询关联的商品标签信息")
    @PostMapping("/getTagsByModuleRefId")
    public ApiResult<?> getTagsByModuleRefId(@RequestBody Map<String, Integer> param) {
        Integer cityCategoryModuleRefId = param.get("cityCategoryModuleRefId");
        if (cityCategoryModuleRefId == null) {
            return fail("模块关系ID不能为空");
        }
        
        // 查询关联的模块-商品标签关系，按sort排序
        LambdaQueryWrapper<Hnzsxh5CityCategoryModuleGoodsTagRef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Hnzsxh5CityCategoryModuleGoodsTagRef::getCityCategoryModuleRefId, cityCategoryModuleRefId)
               .eq(Hnzsxh5CityCategoryModuleGoodsTagRef::getStatus, Constants.HNZSXH5_CATEGORY_STATE_EFFECT)
               .orderByAsc(Hnzsxh5CityCategoryModuleGoodsTagRef::getSort);
        
        List<Hnzsxh5CityCategoryModuleGoodsTagRef> refList = hnzsxh5CityCategoryModuleGoodsTagRefService.list(wrapper);
        if (refList == null || refList.isEmpty()) {
            return success(new ArrayList<>());
        }
        
        // 获取所有标签ID
        List<Integer> tagIds = refList.stream()
                .map(Hnzsxh5CityCategoryModuleGoodsTagRef::getGoodsTagId)
                .collect(Collectors.toList());
        
        // 查询标签信息，只返回状态为有效的数据
        List<Hnzsxh5GoodsTag> tags = hnzsxh5GoodsTagService.listValidByIds(tagIds);
        
        // 转换为前端需要的格式
        List<Map<String, Object>> result = new ArrayList<>();
        for (Hnzsxh5CityCategoryModuleGoodsTagRef ref : refList) {
            for (Hnzsxh5GoodsTag tag : tags) {
                if (ref.getGoodsTagId().equals(tag.getId())) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("refId", ref.getId());
                    item.put("tagId", tag.getId());
                    item.put("tagName", tag.getTagName());
                    item.put("rank", ref.getSort());
                    result.add(item);
                    break;
                }
            }
        }
        
        return success(result);
    }

    /**
     * 批量删除模块-商品标签关系
     * 支持一次性删除多条记录，减少API调用次数，提高性能
     * @param param 包含ids的参数，ids为关系ID数组
     * @return 删除结果
     * <AUTHOR>
     * @date 2025-05-15
     */
    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleGoodsTagRef:remove')")
    @OperationLog(value = "批量删除模块-商品标签关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("批量删除模块-商品标签关系")
    @PostMapping("/batchDelete")
    public ApiResult<?> batchDelete(@RequestBody Map<String, List<Integer>> param) {
        List<Integer> ids = param.get("ids");
        if (ids == null || ids.isEmpty()) {
            return fail("关系ID不能为空");
        }

        // 使用单次数据库操作批量删除，减少数据库交互次数
        if (hnzsxh5CityCategoryModuleGoodsTagRefService.removeByIds(ids)) {
            return success("批量删除成功，共删除" + ids.size() + "条记录");
        }
        return fail("批量删除失败");
    }

    /**
     * 批量获取多个模块关系的标签信息
     * 优化前端性能，减少API调用次数，支持一次性获取多个模块关系的标签数据
     * @param param 包含moduleRefIds的参数，moduleRefIds为模块关系ID数组
     * @return 模块关系ID为key，标签数组为value的对象
     * <AUTHOR> Assistant
     * @date 2025-08-01
     */
    @OperationLog(value = "批量获取多个模块关系的标签信息", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/模块-商品标签关系管理")
    @ApiOperation("批量获取多个模块关系的标签信息")
    @PostMapping("/batchGetTagsByModuleRefIds")
    public ApiResult<?> batchGetTagsByModuleRefIds(@RequestBody Map<String, List<Integer>> param) {
        List<Integer> moduleRefIds = param.get("moduleRefIds");
        if (moduleRefIds == null || moduleRefIds.isEmpty()) {
            return fail("模块关系ID不能为空");
        }

        try {
            // 构建查询条件：模块关系ID在指定列表中且状态为有效
            LambdaQueryWrapper<Hnzsxh5CityCategoryModuleGoodsTagRef> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Hnzsxh5CityCategoryModuleGoodsTagRef::getCityCategoryModuleRefId, moduleRefIds)
                   .eq(Hnzsxh5CityCategoryModuleGoodsTagRef::getStatus, Constants.HNZSXH5_CATEGORY_STATE_EFFECT);

            List<Hnzsxh5CityCategoryModuleGoodsTagRef> allRefs = hnzsxh5CityCategoryModuleGoodsTagRefService.list(wrapper);

            // 获取所有涉及的标签ID
            List<Integer> tagIds = allRefs.stream()
                    .map(Hnzsxh5CityCategoryModuleGoodsTagRef::getGoodsTagId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询标签信息
            List<Hnzsxh5GoodsTag> tags = new ArrayList<>();
            if (!tagIds.isEmpty()) {
                tags = hnzsxh5GoodsTagService.listValidByIds(tagIds);
            }

            // 按模块关系ID分组组织结果
            Map<Integer, List<Map<String, Object>>> result = new HashMap<>();

            // 初始化所有模块关系ID的结果为空列表
            for (Integer refId : moduleRefIds) {
                result.put(refId, new ArrayList<>());
            }

            // 填充实际数据
            for (Hnzsxh5CityCategoryModuleGoodsTagRef ref : allRefs) {
                Integer moduleRefId = ref.getCityCategoryModuleRefId();

                // 查找对应的标签信息
                for (Hnzsxh5GoodsTag tag : tags) {
                    if (ref.getGoodsTagId().equals(tag.getId())) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("refId", ref.getId());
                        item.put("tagId", tag.getId());
                        item.put("tagName", tag.getTagName());
                        item.put("sort", ref.getSort());

                        result.get(moduleRefId).add(item);
                        break;
                    }
                }
            }

            return success(result);
        } catch (Exception e) {
            return fail("批量获取模块标签关系失败：" + e.getMessage());
        }
    }
}
