<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2cf279b4-0fe7-4cd1-88f6-fb465a19d8b3" name="Changes" comment="统一认证探活配置">
      <change beforePath="$PROJECT_DIR$/catalina.home_IS_UNDEFINED/logs/WM/hnyxsAdmin/error.log" beforeDir="false" afterPath="$PROJECT_DIR$/catalina.home_IS_UNDEFINED/logs/WM/hnyxsAdmin/error.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/catalina.home_IS_UNDEFINED/logs/WM/hnyxsAdmin/info.log" beforeDir="false" afterPath="$PROJECT_DIR$/catalina.home_IS_UNDEFINED/logs/WM/hnyxsAdmin/info.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/catalina.home_IS_UNDEFINED/logs/WM/hnyxsAdmin/warn.log" beforeDir="false" afterPath="$PROJECT_DIR$/catalina.home_IS_UNDEFINED/logs/WM/hnyxsAdmin/warn.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRefController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRefController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryModuleGoodsTagRefController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryModuleGoodsTagRefController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryModuleRefController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryModuleRefController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryRefController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/controller/Hnzsxh5CityCategoryRefController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/entity/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRef.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/entity/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRef.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/entity/Hnzsxh5CityCategoryModuleGoodsTagRef.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/entity/Hnzsxh5CityCategoryModuleGoodsTagRef.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRefService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRefService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/Hnzsxh5CityCategoryModuleGoodsTagRefService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/Hnzsxh5CityCategoryModuleGoodsTagRefService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/impl/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRefServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/impl/Hnzsxh5CityCategoryModuleGoodsAttributeTypeRefServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/impl/Hnzsxh5CityCategoryModuleGoodsTagRefServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/hlkj/yxsAdminApi/hnzsxH5/service/impl/Hnzsxh5CityCategoryModuleGoodsTagRefServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="srd19176679047/lyx" />
                    <option name="lastUsedInstant" value="1744117287" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1744117286" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/cn/hutool/hutool-http/5.7.11/hutool-http-5.7.11.jar!/cn/hutool/http/useragent/UserAgent.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar!/com/alibaba/fastjson/JSONObject.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar!/com/alibaba/fastjson/parser/JSONLexerBase.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/com/amazonaws/aws-java-sdk-s3/1.12.504/aws-java-sdk-s3-1.12.504.jar!/com/amazonaws/services/s3/AbstractAmazonS3.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/com/amazonaws/aws-java-sdk-s3/1.12.504/aws-java-sdk-s3-1.12.504.jar!/com/amazonaws/services/s3/AmazonS3.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/com/baomidou/mybatis-plus-extension/3.4.1/mybatis-plus-extension-3.4.1.jar!/com/baomidou/mybatisplus/extension/service/IService.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/org/apache/poi/poi/4.1.1/poi-4.1.1.jar!/org/apache/poi/hssf/usermodel/HSSFWorkbook.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/org/apache/tomcat/embed/tomcat-embed-core/9.0.52/tomcat-embed-core-9.0.52.jar!/javax/servlet/ServletContext.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/org/apache/tomcat/embed/tomcat-embed-core/9.0.52/tomcat-embed-core-9.0.52.jar!/javax/servlet/http/HttpServletRequest.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../developer/maven/apache-maven-3.9.9/mvn_repo/org/springframework/spring-web/5.3.9/spring-web-5.3.9.jar!/org/springframework/web/bind/annotation/RequestBody.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../java_develop tools/jdk1.8.0_131/src.zip!/java/io/File.java" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../java_develop tools/jdk1.8.0_131/src.zip!/java/util/Arrays.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
        <option name="userSettingsFile" value="D:\developer\maven\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2vRZ3jYZytqztRmM5wC2apikuFR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Notification.DisplayName-DoNotAsk-DatabaseConfigFileWatcher.found": "找到数据库连接形参",
    "Notification.DisplayName-DoNotAsk-Lombok plugin": "Lombok 集成问题",
    "Notification.DoNotAsk-DatabaseConfigFileWatcher.found": "true",
    "Notification.DoNotAsk-Lombok plugin": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.EleAdminApplication.executor": "Debug",
    "WebServerToolWindowFactoryState": "false",
    "checkBoxType": "false",
    "git-widget-placeholder": "srd19176679047/lyx",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/文件",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.37816092",
    "settings.editor.selected.configurable": "project.propCompiler",
    "spring.configuration.checksum": "b618b9973220a4de777fcd4bcf8dbd96",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-api\src\main\java\com\hlkj\yxsAdminApi\common\core" />
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-api\src\main\java\com\hlkj\yxsAdminApi\hnzsxH5\mapper\xml" />
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-api\src\main\java\com\hlkj\yxsAdminApi\hnzsxH5\common" />
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-api\src\main\java\com\hlkj\yxsAdminApi\hnzsxH5\common\enums" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-api\src\main\java\com\hlkj\yxsAdminApi\hnzsxH5\mapper\xml" />
      <recent name="D:\code\dianxinCode\新版省集约项目\hnyxs-admim-api\src\main\java\com\hlkj\yxsAdminApi\hnzhsl\mapper\xml" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.hlkj.yxsAdminApi.hnzhsl.controller" />
      <recent name="com.hlkj.yxsAdminApi.hnzhsl.service.impl" />
      <recent name="com.hlkj.yxsAdminApi.hnzhsl.service" />
      <recent name="com.hlkj.yxsAdminApi.hnzhsl.mapper" />
      <recent name="com.hlkj.yxsAdminApi.hnzhsl.entity" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="EleAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="hnyxs-admim-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hlkj.yxsAdminApi.EleAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2cf279b4-0fe7-4cd1-88f6-fb465a19d8b3" name="Changes" comment="" />
      <created>1744109651122</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744109651122</updated>
      <workItem from="1744109652182" duration="9073000" />
      <workItem from="1744163400562" duration="6379000" />
      <workItem from="1744245487676" duration="687000" />
      <workItem from="1744247086627" duration="5742000" />
      <workItem from="1744338020525" duration="1268000" />
      <workItem from="1744340858097" duration="5377000" />
      <workItem from="1744435611925" duration="18000" />
      <workItem from="1745287475796" duration="11669000" />
      <workItem from="1745373626764" duration="19646000" />
      <workItem from="1745462180506" duration="16880000" />
      <workItem from="1745496372245" duration="341000" />
      <workItem from="1745497063068" duration="7970000" />
      <workItem from="1745562073314" duration="1074000" />
      <workItem from="1745563745683" duration="7216000" />
      <workItem from="1745576451695" duration="9209000" />
      <workItem from="1745731894415" duration="14596000" />
      <workItem from="1745753226012" duration="19000" />
      <workItem from="1745754839479" duration="8924000" />
      <workItem from="1745769986891" duration="3432000" />
      <workItem from="1745776987448" duration="3313000" />
      <workItem from="1745823474607" duration="468000" />
      <workItem from="1745824425679" duration="5316000" />
      <workItem from="1745897672077" duration="3652000" />
      <workItem from="1745910201885" duration="7357000" />
      <workItem from="1745918666313" duration="25252000" />
      <workItem from="1745993502344" duration="932000" />
      <workItem from="1746491885612" duration="12515000" />
      <workItem from="1746578991048" duration="17540000" />
      <workItem from="1746665048063" duration="4178000" />
      <workItem from="1746684459454" duration="11657000" />
      <workItem from="1746701395474" duration="644000" />
      <workItem from="1746751682787" duration="11000" />
      <workItem from="1746753489082" duration="8070000" />
      <workItem from="1746781291062" duration="1465000" />
      <workItem from="1747010564093" duration="16832000" />
      <workItem from="1747057202463" duration="27000" />
      <workItem from="1747096937617" duration="6104000" />
      <workItem from="1747118313478" duration="76000" />
      <workItem from="1747118449765" duration="16554000" />
      <workItem from="1747150099170" duration="16727000" />
      <workItem from="1747226179490" duration="244000" />
      <workItem from="1747271395046" duration="4160000" />
      <workItem from="1747276494818" duration="14277000" />
      <workItem from="1747323519854" duration="1066000" />
      <workItem from="1747325023792" duration="1966000" />
      <workItem from="1747328413676" duration="17332000" />
      <workItem from="1747370129425" duration="2456000" />
      <workItem from="1747535927213" duration="5781000" />
      <workItem from="1747637648416" duration="5623000" />
      <workItem from="1747702283639" duration="281000" />
      <workItem from="1747702599513" duration="283000" />
      <workItem from="1747703198293" duration="13477000" />
      <workItem from="1747765019640" duration="5078000" />
      <workItem from="1747797684904" duration="13340000" />
      <workItem from="1747874137860" duration="12021000" />
      <workItem from="1747960816640" duration="12288000" />
      <workItem from="1748138699446" duration="8599000" />
      <workItem from="1748219918658" duration="1869000" />
      <workItem from="1748221810950" duration="10721000" />
      <workItem from="1748306784514" duration="9956000" />
      <workItem from="1748350795363" duration="5277000" />
      <workItem from="1748479773611" duration="10145000" />
      <workItem from="1748566894767" duration="6968000" />
      <workItem from="1748911151364" duration="1234000" />
      <workItem from="1748912846652" duration="3935000" />
      <workItem from="1749000829285" duration="12183000" />
      <workItem from="1749084507636" duration="2151000" />
      <workItem from="1749089765814" duration="17605000" />
      <workItem from="1749171056803" duration="13066000" />
      <workItem from="1749431088567" duration="11177000" />
      <workItem from="1749518916139" duration="8333000" />
      <workItem from="1749602605771" duration="4477000" />
      <workItem from="1749746918432" duration="3705000" />
      <workItem from="1749764345826" duration="672000" />
      <workItem from="1749768562534" duration="658000" />
      <workItem from="1750040763494" duration="8490000" />
      <workItem from="1750120803646" duration="27000" />
      <workItem from="1750120849274" duration="9205000" />
      <workItem from="1750207311491" duration="3468000" />
      <workItem from="1750232021710" duration="5175000" />
      <workItem from="1750294413853" duration="22701000" />
      <workItem from="1750327189261" duration="101000" />
      <workItem from="1750327321090" duration="41000" />
      <workItem from="1750380039401" duration="4646000" />
      <workItem from="1750639366792" duration="10803000" />
      <workItem from="1750725521704" duration="22743000" />
      <workItem from="1750812177558" duration="16877000" />
      <workItem from="1750872816462" duration="1767000" />
      <workItem from="1750919608394" duration="9061000" />
      <workItem from="1750985346615" duration="13639000" />
      <workItem from="1751018702053" duration="593000" />
      <workItem from="1751100056868" duration="16477000" />
      <workItem from="1751331094868" duration="14395000" />
      <workItem from="1751416951333" duration="23501000" />
      <workItem from="1751503352133" duration="18323000" />
      <workItem from="1751589926734" duration="8360000" />
      <workItem from="1751849121787" duration="17889000" />
      <workItem from="1751935157033" duration="10622000" />
      <workItem from="1751967287711" duration="597000" />
      <workItem from="1752022354268" duration="20871000" />
      <workItem from="1752130042516" duration="447000" />
      <workItem from="1752194963183" duration="15371000" />
      <workItem from="1752453911085" duration="8473000" />
      <workItem from="1752540358832" duration="1659000" />
      <workItem from="1752626824310" duration="19773000" />
      <workItem from="1752713298427" duration="19557000" />
      <workItem from="1752820707703" duration="10611000" />
      <workItem from="1752833256691" duration="6418000" />
      <workItem from="1753068702853" duration="2081000" />
      <workItem from="1753081404507" duration="2530000" />
      <workItem from="1753148804653" duration="1806000" />
      <workItem from="1753317581652" duration="8586000" />
      <workItem from="1753348587846" duration="1967000" />
      <workItem from="1753351843230" duration="12888000" />
      <workItem from="1753583966889" duration="660000" />
      <workItem from="1753663342601" duration="1208000" />
      <workItem from="1753666300697" duration="1942000" />
      <workItem from="1753751337472" duration="13135000" />
      <workItem from="1753836131961" duration="2703000" />
      <workItem from="1753839076315" duration="6967000" />
      <workItem from="1753922400851" duration="6966000" />
      <workItem from="1753949699892" duration="5495000" />
      <workItem from="1754008865634" duration="930000" />
      <workItem from="1754009818280" duration="530000" />
      <workItem from="1754010390780" duration="5594000" />
    </task>
    <task id="LOCAL-00001" summary="掌上销H5crud">
      <option name="closed" value="true" />
      <created>1744338092379</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1744338092379</updated>
    </task>
    <task id="LOCAL-00002" summary="商品以及模板管理对接">
      <option name="closed" value="true" />
      <created>1745406430103</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1745406430103</updated>
    </task>
    <task id="LOCAL-00003" summary="掌上销H5后台管理">
      <option name="closed" value="true" />
      <created>1745565303100</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1745565303100</updated>
    </task>
    <task id="LOCAL-00004" summary="掌上销H5后台管理商品属性信息返回">
      <option name="closed" value="true" />
      <created>1745574980264</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1745574980264</updated>
    </task>
    <task id="LOCAL-00005" summary="掌上销H5后台管理商品属性信息返回">
      <option name="closed" value="true" />
      <created>1745575780453</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1745575780453</updated>
    </task>
    <task id="LOCAL-00006" summary="注释删除操作">
      <option name="closed" value="true" />
      <created>1745766569361</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1745766569361</updated>
    </task>
    <task id="LOCAL-00007" summary="优化">
      <option name="closed" value="true" />
      <created>1745831259412</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1745831259412</updated>
    </task>
    <task id="LOCAL-00008" summary="手动过费，订单信息导出">
      <option name="closed" value="true" />
      <created>1745942950671</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1745942950671</updated>
    </task>
    <task id="LOCAL-00009" summary="关联配置管理实现">
      <option name="closed" value="true" />
      <created>1746523385379</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746523385379</updated>
    </task>
    <task id="LOCAL-00010" summary="商品列表增加模板ID展示">
      <option name="closed" value="true" />
      <created>1746588680512</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746588680512</updated>
    </task>
    <task id="LOCAL-00011" summary="商品新增白名单管理功能及各种校验">
      <option name="closed" value="true" />
      <created>1747102336556</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1747102336556</updated>
    </task>
    <task id="LOCAL-00012" summary="用户工号列表分页优化">
      <option name="closed" value="true" />
      <created>1747127760847</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1747127760847</updated>
    </task>
    <task id="LOCAL-00013" summary="订单日志记录">
      <option name="closed" value="true" />
      <created>1747203889526</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1747203889526</updated>
    </task>
    <task id="LOCAL-00014" summary="新增礼包状态、跳转路由及链接等">
      <option name="closed" value="true" />
      <created>1747343403667</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1747343403667</updated>
    </task>
    <task id="LOCAL-00015" summary="保存关联工号bug修复">
      <option name="closed" value="true" />
      <created>1747351666541</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1747351666541</updated>
    </task>
    <task id="LOCAL-00016" summary="商品规则配置">
      <option name="closed" value="true" />
      <created>1748242530555</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1748242530555</updated>
    </task>
    <task id="LOCAL-00017" summary="商品关联工号bug修复">
      <option name="closed" value="true" />
      <created>1748497909637</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1748497909637</updated>
    </task>
    <task id="LOCAL-00018" summary="订单日志动态表名优化">
      <option name="closed" value="true" />
      <created>1748509182326</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1748509182326</updated>
    </task>
    <task id="LOCAL-00019" summary="1.商品信息新增导出功能&#10;2.订单导出新增“订单金额”字段&#10;3.订单统计新增“各省市订单量走势图”功能&#10;4.订单统计新增“各地市按模块分类订单数量">
      <option name="closed" value="true" />
      <created>1749019944478</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1749019944478</updated>
    </task>
    <task id="LOCAL-00020" summary="新装客户定位功能、登录bug修复">
      <option name="closed" value="true" />
      <created>1750121512155</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1750121512155</updated>
    </task>
    <task id="LOCAL-00021" summary="接入统一认证、接口熔断、登录/操作日志实现、账号权限变更日志记录">
      <option name="closed" value="true" />
      <created>1750750652480</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1750750652480</updated>
    </task>
    <task id="LOCAL-00022" summary="扫楼薪资报表、反诈签名等模块脱敏">
      <option name="closed" value="true" />
      <created>1750753690176</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1750753690176</updated>
    </task>
    <task id="LOCAL-00023" summary="统一认证接入、订单日志状态检查">
      <option name="closed" value="true" />
      <created>1750986153797</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1750986153797</updated>
    </task>
    <task id="LOCAL-00024" summary="统一认证接入、老扫楼平台代码迁移等">
      <option name="closed" value="true" />
      <created>1752563815792</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1752563815792</updated>
    </task>
    <task id="LOCAL-00025" summary="注销登录日志">
      <option name="closed" value="true" />
      <created>1752626984684</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1752626984684</updated>
    </task>
    <task id="LOCAL-00026" summary="老扫楼平台审批下载以及导出功能迁移 部分分页查询bug修复">
      <option name="closed" value="true" />
      <created>1753090537429</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1753090537429</updated>
    </task>
    <task id="LOCAL-00027" summary="统一认证开关配置">
      <option name="closed" value="true" />
      <created>1753325228255</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1753325228255</updated>
    </task>
    <task id="LOCAL-00028" summary="统一认证探活配置">
      <option name="closed" value="true" />
      <created>1753428978124</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753428978124</updated>
    </task>
    <option name="localTasksCounter" value="29" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/master" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="srd19176679047/lyx" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="掌上销H5后台管理" />
    <MESSAGE value="掌上销H5后台管理商品属性信息返回" />
    <MESSAGE value="注释删除操作" />
    <MESSAGE value="优化" />
    <MESSAGE value="手动过费，订单信息导出" />
    <MESSAGE value="关联配置管理实现" />
    <MESSAGE value="商品列表增加模板ID展示" />
    <MESSAGE value="商品新增白名单管理功能及各种校验" />
    <MESSAGE value="用户工号列表分页优化" />
    <MESSAGE value="订单日志记录" />
    <MESSAGE value="新增礼包状态、跳转路由及链接等" />
    <MESSAGE value="保存关联工号bug修复" />
    <MESSAGE value="商品规则配置" />
    <MESSAGE value="商品关联工号bug修复" />
    <MESSAGE value="订单日志动态表名优化" />
    <MESSAGE value="1.商品信息新增导出功能&#10;2.订单导出新增“订单金额”字段&#10;3.订单统计新增“各省市订单量走势图”功能&#10;4.订单统计新增“各地市按模块分类订单数量" />
    <MESSAGE value="新装客户定位功能、登录bug修复" />
    <MESSAGE value="接入统一认证、接口熔断、登录/操作日志实现、账号权限变更日志记录" />
    <MESSAGE value="扫楼薪资报表、反诈签名等模块脱敏" />
    <MESSAGE value="统一认证接入、订单日志状态检查" />
    <MESSAGE value="统一认证接入、老扫楼平台代码迁移等" />
    <MESSAGE value="注销登录日志" />
    <MESSAGE value="老扫楼平台审批下载以及导出功能迁移 部分分页查询bug修复" />
    <MESSAGE value="统一认证开关配置" />
    <MESSAGE value="统一认证探活配置" />
    <option name="LAST_COMMIT_MESSAGE" value="统一认证探活配置" />
  </component>
  <component name="XDebuggerManager">
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="java.util.LinkedHashMap$Entry" memberName="value" />
        <PinnedItemInfo parentTag="com.hlkj.yxsAdminApi.common.core.web.PageParam" memberName="orders" />
        <PinnedItemInfo parentTag="com.hlkj.yxsAdminApi.common.core.web.PageParam" memberName="optimizeCountSql" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="queryCancelExamTosafe" />
        <watch expression="orderInfo.hnzsxh5OrderInfoOrderattrlists" />
      </configuration>
    </watches-manager>
  </component>
</project>