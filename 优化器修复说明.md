# 配置关系优化器修复说明

## 问题描述

前端控制台报错：
```
TypeError: componentInstance.batchSaveModuleAttributeTypeRef is not a function
```

这个错误发生在添加新的属性或标签时，因为优化器试图调用组件实例上不存在的方法。

## 问题原因

在 `configRelationOptimizer.js` 中，代码试图通过组件实例调用批量保存方法：

```javascript
// 错误的调用方式
savePromises.push(componentInstance.batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs));
savePromises.push(componentInstance.batchSaveModuleTagRef(moduleTagRefs));
```

但是这些方法实际上是从 API 模块导入的独立函数，不是组件实例的方法。

## 解决方案

### 1. 修改导入语句
在 `configRelationOptimizer.js` 中添加批量保存方法的导入：

```javascript
import {
  batchGetCategoriesByCityCodes,
  batchGetModulesByCityCategoryRefIds,
  batchGetAttributeTypesByModuleRefIds,
  batchGetTagsByModuleRefIds,
  batchSaveModuleAttributeTypeRef,  // 新增
  batchSaveModuleTagRef,           // 新增
  getCategoriesByCityCode,
  getModulesByCityCategoryRefId,
  getAttributeTypesByModuleRefId,
  getTagsByModuleRefId
} from '@/api/hnzsxH5/configRelation';
```

### 2. 修改方法调用
将组件实例方法调用改为直接函数调用：

```javascript
// 修复后的调用方式
if (moduleAttributeTypeRefs.length > 0) {
  console.log(`批量保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);
  savePromises.push(batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs));
}

if (moduleTagRefs.length > 0) {
  console.log(`批量保存${moduleTagRefs.length}个模块-商品标签关系`);
  savePromises.push(batchSaveModuleTagRef(moduleTagRefs));
}
```

## 验证修复

### 测试步骤
1. 进入配置关系管理页面
2. 选择地市和分类
3. 添加新的属性或标签
4. 点击保存配置
5. 检查控制台是否还有错误

### 预期结果
- 不再出现 `is not a function` 错误
- 批量保存操作正常执行
- 新添加的属性和标签能够成功保存

## 相关文件

- **修改文件**: `hnyxs-admim-app/src/utils/configRelationOptimizer.js`
- **API文件**: `hnyxs-admim-app/src/api/hnzsxH5/configRelation.js`
- **后端接口**: 
  - `POST /api/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/avoid/batchInsert`
  - `POST /api/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/avoid/batchInsert`

## 注意事项

1. 这个修复只影响优化器的保存功能，不影响数据加载优化
2. 批量保存接口在后端已经存在，无需额外开发
3. 修复后的代码与组件中原有的保存逻辑保持一致
