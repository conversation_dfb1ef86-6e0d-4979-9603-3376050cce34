# 移除操作修复说明

## 问题描述

用户反馈：所有的移除操作后的结果都没有被正确保存，保存后再次进来还是移除之前的结果。

## 问题根因

优化器中的删除逻辑没有实现，只有框架代码：

```javascript
// 原来的代码（只有注释，没有实际逻辑）
async optimizedDeleteUnusedRelations(componentInstance) {
  // 使用缓存的数据进行比较和删除逻辑
  // ... 删除逻辑实现（基于已有的allRelationData）
}
```

## 修复内容

### 1. 添加批量删除API导入
```javascript
import {
  // ... 其他导入
  batchDeleteCityCategoryRef,
  batchDeleteCityCategoryModuleRef,
  batchDeleteModuleAttributeTypeRef,
  batchDeleteModuleTagRef
} from '@/api/hnzsxH5/configRelation';
```

### 2. 修复数据结构转换问题

**问题**: 属性和标签删除逻辑中的数据结构不匹配
- 后端返回: `Map<Integer, List<Map<String, Object>>>`
- 优化器期望: `{moduleRefId: {attributes: [], tags: []}}`

**修复**: 在 `_loadModuleAttributesAndTagsData` 方法中添加数据结构转换：

```javascript
// 转换数据结构：从 {attributes: Map, tags: Map} 转换为 {moduleRefId: {attributes: [], tags: []}}
const result = {};

// 初始化所有模块的数据结构
for (const moduleRefId of moduleRefIds) {
  result[moduleRefId] = {
    attributes: attributesResult[moduleRefId] || [],
    tags: tagsResult[moduleRefId] || []
  };
}

return result;
```

### 3. 实现完整的删除逻辑

#### 2.1 主删除方法
- 调用各层级的查找无用关系方法
- 收集需要删除的关系ID
- 并行执行批量删除操作
- 添加详细的调试日志

#### 2.2 地市-分类关系删除
```javascript
findUnusedCityCategoryRelations(componentInstance, toDeleteRefIds) {
  // 比较现有关系与当前选择
  // 标记不在当前选择中的关系为删除
}
```

#### 2.3 地市分类-模块关系删除
```javascript
findUnusedCityCategoryModuleRelations(componentInstance, toDeleteRefIds) {
  // 处理模块关系的删除
  // 包括因地市-分类关系删除而级联删除的情况
}
```

#### 2.4 模块-属性关系删除
```javascript
findUnusedModuleAttributeRelations(componentInstance, toDeleteRefIds) {
  // 处理属性关系的删除
  // 包括因模块关系删除而级联删除的情况
}
```

#### 2.5 模块-标签关系删除
```javascript
findUnusedModuleTagRelations(componentInstance, toDeleteRefIds) {
  // 处理标签关系的删除
  // 包括因模块关系删除而级联删除的情况
}
```

### 3. 删除逻辑的层级处理

删除操作按照数据依赖关系进行：

1. **地市-分类关系**: 如果地市下的某个分类被移除，删除对应的关系记录
2. **模块关系**: 如果地市-分类下的某个模块被移除，或者地市-分类关系本身被删除，删除对应的模块关系
3. **属性关系**: 如果模块下的某个属性被移除，或者模块关系本身被删除，删除对应的属性关系
4. **标签关系**: 如果模块下的某个标签被移除，或者模块关系本身被删除，删除对应的标签关系

### 4. 级联删除处理

当上级关系被删除时，自动删除所有下级关系：
- 删除地市-分类关系 → 自动删除该分类下的所有模块、属性、标签关系
- 删除模块关系 → 自动删除该模块下的所有属性、标签关系

## 后端接口支持

所有必要的批量删除接口都已存在：

- `POST /api/hnzsxH5/hnzsxh5-city-category-ref/batchDelete`
- `POST /api/hnzsxH5/hnzsxh5-city-category-module-ref/batchDelete`
- `POST /api/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchDelete`
- `POST /api/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchDelete`

请求格式：
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

## 调试信息

修复后的代码会输出详细的调试信息：

```
开始处理删除无用关系...
开始查找需要删除的属性关系...
现有属性和标签数据结构: ["123", "456", "789"]
检查模块关系 123 的属性: ["101(refId:1001)", "102(refId:1002)"]
模块组合 110000-1-456 当前选择的属性: [101]
标记删除属性关系: 110000-1-456-102 (refId: 1002)
开始查找需要删除的标签关系...
检查模块关系 123 的标签: ["201(refId:2001)", "202(refId:2002)"]
模块组合 110000-1-456 当前选择的标签: [201]
标记删除标签关系: 110000-1-456-202 (refId: 2002)
待删除的关系统计:
地市-分类关系: 0个
地市分类-模块关系: 0个
模块-属性关系: 1个
模块-标签关系: 1个
批量删除1个模块-属性关系: [1002]
批量删除1个模块-标签关系: [2002]
删除无用关系完成
```

## 测试步骤

### 1. 测试分类移除
1. 选择地市，添加多个分类
2. 保存配置
3. 移除其中一个分类
4. 保存配置
5. 刷新页面，验证被移除的分类不再显示

### 2. 测试模块移除
1. 在地市-分类下添加多个模块
2. 保存配置
3. 移除其中一个模块
4. 保存配置
5. 刷新页面，验证被移除的模块不再显示

### 3. 测试属性/标签移除
1. 在模块下添加多个属性和标签
2. 保存配置
3. 移除其中一些属性和标签
4. 保存配置
5. 刷新页面，验证被移除的项目不再显示

### 4. 测试级联删除
1. 创建完整的配置层级（地市→分类→模块→属性/标签）
2. 保存配置
3. 移除上级关系（如删除分类）
4. 保存配置
5. 验证下级关系也被正确删除

## 预期效果

修复后：
- ✅ 移除操作能正确保存到数据库
- ✅ 刷新页面后移除的项目不会重新出现
- ✅ 级联删除正常工作
- ✅ 控制台显示详细的删除过程日志
- ✅ 与添加操作保持一致的用户体验
