package com.hlkj.yxsAdminApi.hnzsxH5.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.constant.SysLogConstant;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5Module;
import com.hlkj.yxsAdminApi.hnzsxH5.service.Hnzsxh5CityCategoryModuleRefService;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5CityCategoryModuleRef;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5CityCategoryModuleRefParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import com.hlkj.yxsAdminApi.hnzsxH5.service.Hnzsxh5ModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * H5即时受理-地市分类关系表-对应-模块表（关系表）控制器
 *
 * <AUTHOR>
 * @since 2025-04-17 17:18:06
 */
@Api(tags = "H5即时受理-地市分类关系表-对应-模块表（关系表）管理")
@RestController
@RequestMapping("/api/hnzsxH5/hnzsxh5-city-category-module-ref")
public class Hnzsxh5CityCategoryModuleRefController extends BaseController {
    @Autowired
    private Hnzsxh5CityCategoryModuleRefService hnzsxh5CityCategoryModuleRefService;
    
    @Autowired
    private Hnzsxh5ModuleService hnzsxh5ModuleService;

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:list')")
    @OperationLog(value = "分页查询地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("分页查询H5即时受理-地市分类关系表-对应-模块表（关系表）")
    @PostMapping("/page")
    public ApiResult<PageResult<Hnzsxh5CityCategoryModuleRef>> page(@RequestBody  Hnzsxh5CityCategoryModuleRefParam param) {
        PageParam<Hnzsxh5CityCategoryModuleRef, Hnzsxh5CityCategoryModuleRefParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnzsxh5CityCategoryModuleRefService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnzsxh5CityCategoryModuleRefService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:list')")
    @OperationLog(value = "查询全部地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("查询全部H5即时受理-地市分类关系表-对应-模块表（关系表）")
    @PostMapping("/getListByParam")
    public ApiResult<List<Hnzsxh5CityCategoryModuleRef>> list(@RequestBody Hnzsxh5CityCategoryModuleRefParam param) {
        PageParam<Hnzsxh5CityCategoryModuleRef, Hnzsxh5CityCategoryModuleRefParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnzsxh5CityCategoryModuleRefService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnzsxh5CityCategoryModuleRefService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:list')")
    @OperationLog(value = "根据ID查询地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("根据id查询H5即时受理-地市分类关系表-对应-模块表（关系表）")
    @PostMapping("/getById")
    public ApiResult<?> get(@RequestBody Hnzsxh5CityCategoryModuleRefParam param) {
        if(param.getId()==null){
            return fail("请确认传入的id是否为空");
        }
        return success(hnzsxh5CityCategoryModuleRefService.getById(param.getId()));
        // 使用关联查询
        //return success(hnzsxh5CityCategoryModuleRefService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:save')")
    @OperationLog(value = "添加地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("添加H5即时受理-地市分类关系表-对应-模块表（关系表）")
    @PostMapping("/avoid/insert")
    public ApiResult<?> save(@RequestBody Hnzsxh5CityCategoryModuleRef hnzsxh5CityCategoryModuleRef) {
        if (hnzsxh5CityCategoryModuleRefService.save(hnzsxh5CityCategoryModuleRef)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:update')")
    @OperationLog(value = "修改地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("修改H5即时受理-地市分类关系表-对应-模块表（关系表）")
    @PostMapping("/update")
    public ApiResult<?> update(@RequestBody Hnzsxh5CityCategoryModuleRef hnzsxh5CityCategoryModuleRef) {
        if (hnzsxh5CityCategoryModuleRefService.updateById(hnzsxh5CityCategoryModuleRef)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }


    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:save')")
    @OperationLog(value = "批量添加地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("批量添加H5即时受理-地市分类关系表-对应-模块表（关系表）")
    @PostMapping("/avoid/batchInsert")
    public ApiResult<?> saveBatch(@RequestBody List<Hnzsxh5CityCategoryModuleRef> list) {
        if (hnzsxh5CityCategoryModuleRefService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:update')")
    @OperationLog(value = "批量修改地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("批量修改H5即时受理-地市分类关系表-对应-模块表（关系表）")
    @PostMapping("/batchUpdate")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<Hnzsxh5CityCategoryModuleRef> batchParam) {
        if (batchParam.update(hnzsxh5CityCategoryModuleRefService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除地市分类-模块关系
     * @param param 包含id的参数
     * @return 删除结果
     * <AUTHOR>
     * @date 2025-05-15
     */
    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:remove')")
    @OperationLog(value = "删除地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("删除地市分类-模块关系")
    @PostMapping("/delete")
    public ApiResult<?> delete(@RequestBody Map<String, Integer> param) {
        Integer id = param.get("id");
        if (id == null) {
            return fail("关系ID不能为空");
        }
        
        if (hnzsxh5CityCategoryModuleRefService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 根据地市-分类关系ID查询关联的模块信息
     * @param cityCategoryRefId 地市-分类关系ID
     * @return 模块信息列表
     */
    @OperationLog(value = "查询地市分类关联的模块信息", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("根据地市-分类关系ID查询关联的模块信息")
    @PostMapping("/getModulesByCityCategoryRefId")
    public ApiResult<?> getModulesByCityCategoryRefId(@RequestBody Map<String, Integer> param) {
        Integer cityCategoryRefId = param.get("cityCategoryRefId");
        if (cityCategoryRefId == null) {
            return fail("地市-分类关系ID不能为空");
        }
        
        // 查询关联的地市-分类-模块关系
        LambdaQueryWrapper<Hnzsxh5CityCategoryModuleRef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Hnzsxh5CityCategoryModuleRef::getCityCategoryRefId, cityCategoryRefId)
               .eq(Hnzsxh5CityCategoryModuleRef::getStatus, Constants.HNZSXH5_CATEGORY_STATE_EFFECT);
        
        List<Hnzsxh5CityCategoryModuleRef> refList = hnzsxh5CityCategoryModuleRefService.list(wrapper);
        if (refList == null || refList.isEmpty()) {
            return success(new ArrayList<>());
        }
        
        // 获取所有模块ID
        List<Integer> moduleIds = refList.stream()
                .map(Hnzsxh5CityCategoryModuleRef::getModuleId)
                .collect(Collectors.toList());
        
        // 查询模块信息，只返回状态为有效的数据
        List<Hnzsxh5Module> modules = hnzsxh5ModuleService.listValidByIds(moduleIds);
        
        // 转换为前端需要的格式
        List<Map<String, Object>> result = new ArrayList<>();
        for (Hnzsxh5CityCategoryModuleRef ref : refList) {
            for (Hnzsxh5Module module : modules) {
                if (ref.getModuleId().equals(module.getId())) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("refId", ref.getId());
                    item.put("moduleId", module.getId());
                    item.put("moduleName", module.getModuleName());
                    item.put("moduleCode", module.getModuleCode());
                    result.add(item);
                    break;
                }
            }
        }
        
        return success(result);
    }

    /**
     * 批量删除地市分类-模块关系
     * 支持一次性删除多条记录，减少API调用次数，提高性能
     * @param param 包含ids的参数，ids为关系ID数组
     * @return 删除结果
     * <AUTHOR>
     * @date 2025-05-15
     */
    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryModuleRef:remove')")
    @OperationLog(value = "批量删除地市分类-模块关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("批量删除地市分类-模块关系")
    @PostMapping("/batchDelete")
    public ApiResult<?> batchDelete(@RequestBody Map<String, List<Integer>> param) {
        List<Integer> ids = param.get("ids");
        if (ids == null || ids.isEmpty()) {
            return fail("关系ID不能为空");
        }

        // 使用单次数据库操作批量删除，减少数据库交互次数
        if (hnzsxh5CityCategoryModuleRefService.removeByIds(ids)) {
            return success("批量删除成功，共删除" + ids.size() + "条记录");
        }
        return fail("批量删除失败");
    }

    /**
     * 批量获取多个地市分类关系的模块信息
     * 优化前端性能，减少API调用次数，支持一次性获取多个地市分类关系的模块数据
     * @param param 包含cityCategoryRefIds的参数，cityCategoryRefIds为地市分类关系ID数组
     * @return 关系ID为key，模块关系数组为value的对象
     * <AUTHOR> Assistant
     * @date 2025-08-01
     */
    @OperationLog(value = "批量获取多个地市分类关系的模块信息", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市分类-模块关系管理")
    @ApiOperation("批量获取多个地市分类关系的模块信息")
    @PostMapping("/batchGetModulesByCityCategoryRefIds")
    public ApiResult<?> batchGetModulesByCityCategoryRefIds(@RequestBody Map<String, List<Integer>> param) {
        List<Integer> cityCategoryRefIds = param.get("cityCategoryRefIds");
        if (cityCategoryRefIds == null || cityCategoryRefIds.isEmpty()) {
            return fail("地市分类关系ID不能为空");
        }

        try {
            // 构建查询条件：地市分类关系ID在指定列表中且状态为有效
            LambdaQueryWrapper<Hnzsxh5CityCategoryModuleRef> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Hnzsxh5CityCategoryModuleRef::getCityCategoryRefId, cityCategoryRefIds)
                   .eq(Hnzsxh5CityCategoryModuleRef::getStatus, Constants.HNZSXH5_CATEGORY_STATE_EFFECT);

            List<Hnzsxh5CityCategoryModuleRef> allRefs = hnzsxh5CityCategoryModuleRefService.list(wrapper);

            // 获取所有涉及的模块ID
            List<Integer> moduleIds = allRefs.stream()
                    .map(Hnzsxh5CityCategoryModuleRef::getModuleId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询模块信息
            List<Hnzsxh5Module> modules = new ArrayList<>();
            if (!moduleIds.isEmpty()) {
                modules = hnzsxh5ModuleService.listValidByIds(moduleIds);
            }

            // 按地市分类关系ID分组组织结果
            Map<Integer, List<Map<String, Object>>> result = new HashMap<>();

            // 初始化所有关系ID的结果为空列表
            for (Integer refId : cityCategoryRefIds) {
                result.put(refId, new ArrayList<>());
            }

            // 填充实际数据
            for (Hnzsxh5CityCategoryModuleRef ref : allRefs) {
                Integer cityCategoryRefId = ref.getCityCategoryRefId();

                // 查找对应的模块信息
                for (Hnzsxh5Module module : modules) {
                    if (ref.getModuleId().equals(module.getId())) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("refId", ref.getId());
                        item.put("moduleId", module.getId());
                        item.put("moduleName", module.getModuleName());
                        item.put("moduleCode", module.getModuleCode());

                        result.get(cityCategoryRefId).add(item);
                        break;
                    }
                }
            }

            return success(result);
        } catch (Exception e) {
            return fail("批量获取地市分类模块关系失败：" + e.getMessage());
        }
    }

}
