# 分类和模块配置问题调试指南

## 问题描述

用户反馈：前两层（分类和模块）的添加和移除配置没有生效，但是属性和标签的配置正常工作。

## 已修复的问题

### 1. API方法调用错误
**问题**: 优化器试图调用组件实例上不存在的方法
**修复**: 添加了缺失的API导入并修正了调用方式

```javascript
// 修复前（错误）
await componentInstance.batchSaveCityCategoryRef(cityCategoryRefs);
await componentInstance.batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);

// 修复后（正确）
await batchSaveCityCategoryRef(cityCategoryRefs);
await batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);
```

### 2. 添加调试日志
为了更好地诊断问题，在优化器中添加了详细的调试日志：

- 地市-分类关系保存过程的日志
- 地市分类-模块关系保存过程的日志
- 数据状态和映射关系的日志

## 调试步骤

### 1. 检查控制台日志
打开浏览器开发者工具，在保存配置时观察控制台输出：

```
开始处理地市-分类关系保存...
选中的地市: ["110000", "120000"]
地市分类数据: {110000: [1, 2], 120000: [3]}
现有关系数据: {110000: [{refId: 1, categoryId: 1}]}
地市 110000 的分类: [1, 2]
检查关系 110000_1: 已存在=true
检查关系 110000_2: 已存在=false
添加新的地市-分类关系: 110000_2
```

### 2. 验证数据流
检查以下关键数据点：

1. **组件数据状态**:
   - `componentInstance.selectedCities` - 选中的地市
   - `componentInstance.cityCategories` - 每个地市的分类
   - `componentInstance.cityModules` - 每个地市-分类的模块

2. **映射关系**:
   - `componentInstance.cityCategoryRefMap` - 地市分类关系ID映射
   - `componentInstance.cityCategoryModuleRefMap` - 模块关系ID映射

3. **现有关系数据**:
   - `this.allRelationData.cityCategories` - 现有地市分类关系
   - `this.allRelationData.categoryModules` - 现有模块关系

### 3. 检查API调用
在Network面板中验证：

1. **批量保存接口调用**:
   - `POST /api/hnzsxH5/hnzsxh5-city-category-ref/avoid/batchInsert`
   - `POST /api/hnzsxH5/hnzsxh5-city-category-module-ref/avoid/batchInsert`

2. **请求数据格式**:
```json
// 地市-分类关系
[
  {
    "cityCode": "110000",
    "categoryId": 2,
    "status": 1,
    "sort": 1
  }
]

// 地市分类-模块关系
[
  {
    "cityCategoryRefId": 123,
    "moduleId": 456,
    "status": 1,
    "isOneBeat": 1
  }
]
```

### 4. 检查后端响应
确保后端接口正常响应：

```json
{
  "code": 0,
  "message": "添加成功",
  "data": null
}
```

## 可能的问题原因

### 1. 数据同步问题
- 组件的数据状态可能没有正确更新
- 映射关系可能没有及时刷新

### 2. 时序问题
- 保存操作的执行顺序可能有问题
- 映射关系更新可能在保存之前执行

### 3. 缓存问题
- 现有关系数据的缓存可能过期
- 组件状态可能没有正确清理

## 解决方案验证

### 1. 手动测试步骤
1. 清空浏览器缓存
2. 重新加载页面
3. 选择地市
4. 添加新的分类
5. 添加新的模块
6. 保存配置
7. 检查控制台日志和Network请求

### 2. 数据验证
保存后检查数据库中的记录：

```sql
-- 检查地市-分类关系
SELECT * FROM hnzsxh5_city_category_ref 
WHERE city_code IN ('110000', '120000') 
ORDER BY created_date DESC;

-- 检查地市分类-模块关系
SELECT * FROM hnzsxh5_city_category_module_ref 
WHERE city_category_ref_id IN (SELECT id FROM hnzsxh5_city_category_ref WHERE city_code IN ('110000', '120000'))
ORDER BY created_date DESC;
```

## 下一步行动

1. **运行测试**: 按照调试步骤进行测试
2. **收集日志**: 记录控制台输出和Network请求
3. **分析数据**: 检查数据流和API调用是否正确
4. **验证结果**: 确认数据库中是否有新记录

如果问题仍然存在，请提供：
- 控制台的完整日志输出
- Network面板中的API请求详情
- 组件数据状态的快照
