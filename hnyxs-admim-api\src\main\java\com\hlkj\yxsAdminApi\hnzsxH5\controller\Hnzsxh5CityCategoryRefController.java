package com.hlkj.yxsAdminApi.hnzsxH5.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.yxsAdminApi.common.core.constant.Constants;
import com.hlkj.yxsAdminApi.common.core.constant.SysLogConstant;
import com.hlkj.yxsAdminApi.common.core.web.BaseController;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5Category;
import com.hlkj.yxsAdminApi.hnzsxH5.service.Hnzsxh5CategoryService;
import com.hlkj.yxsAdminApi.hnzsxH5.service.Hnzsxh5CityCategoryRefService;
import com.hlkj.yxsAdminApi.hnzsxH5.entity.Hnzsxh5CityCategoryRef;
import com.hlkj.yxsAdminApi.hnzsxH5.param.Hnzsxh5CityCategoryRefParam;
import com.hlkj.yxsAdminApi.common.core.web.ApiResult;
import com.hlkj.yxsAdminApi.common.core.web.PageResult;
import com.hlkj.yxsAdminApi.common.core.web.PageParam;
import com.hlkj.yxsAdminApi.common.core.web.BatchParam;
import com.hlkj.yxsAdminApi.common.core.annotation.OperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * H5即时受理-地市-对应-模块分类表（关系表）控制器
 *
 * <AUTHOR>
 * @since 2025-04-17 16:00:29
 */
@Api(tags = "H5即时受理-地市-对应-模块分类表（关系表）管理")
@RestController
@RequestMapping("/api/hnzsxH5/hnzsxh5-city-category-ref")
public class Hnzsxh5CityCategoryRefController extends BaseController {
    @Autowired
    private Hnzsxh5CityCategoryRefService hnzsxh5CityCategoryRefService;
    
    @Autowired
    private Hnzsxh5CategoryService hnzsxh5CategoryService;

    @PreAuthorize("hasAuthority(' hnzsxH5:hnzsxh5CityCategoryRef:list')")
    @OperationLog(value = "分页查询地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("分页查询H5即时受理-地市-对应-模块分类表（关系表）")
    @PostMapping("/page")
    public ApiResult<PageResult<Hnzsxh5CityCategoryRef>> page(@RequestBody Hnzsxh5CityCategoryRefParam param) {
        PageParam<Hnzsxh5CityCategoryRef, Hnzsxh5CityCategoryRefParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnzsxh5CityCategoryRefService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(hnzsxh5CityCategoryRefService.pageRel(param));
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryRef:list')")
    @OperationLog(value = "查询全部地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("查询全部H5即时受理-地市-对应-模块分类表（关系表）")
    @PostMapping("/getListByParam")
    public ApiResult<List<Hnzsxh5CityCategoryRef>> list(@RequestBody Hnzsxh5CityCategoryRefParam param) {
        PageParam<Hnzsxh5CityCategoryRef, Hnzsxh5CityCategoryRefParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(hnzsxh5CityCategoryRefService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(hnzsxh5CityCategoryRefService.listRel(param));
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryRef:list')")
    @OperationLog(value = "根据ID查询地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("根据id查询H5即时受理-地市-对应-模块分类表（关系表）")
    @PostMapping("/getById")
    public ApiResult<?> get(@RequestBody Hnzsxh5CityCategoryRefParam param) {
        if(param.getId()==null){
            return fail("请传入id");
        }
        return success(hnzsxh5CityCategoryRefService.getById(param.getId()));
        // 使用关联查询
        //return success(hnzsxh5CityCategoryRefService.getByIdRel(id));
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryRef:save')")
    @OperationLog(value = "添加地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("添加H5即时受理-地市-对应-模块分类表（关系表）")
    @PostMapping("/avoid/insert")
    public ApiResult<?> save(@RequestBody Hnzsxh5CityCategoryRef hnzsxh5CityCategoryRef) {
        if (hnzsxh5CityCategoryRefService.save(hnzsxh5CityCategoryRef)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryRef:update')")
    @OperationLog(value = "修改地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("修改H5即时受理-地市-对应-模块分类表（关系表）")
    @PostMapping("update")
    public ApiResult<?> update(@RequestBody Hnzsxh5CityCategoryRef hnzsxh5CityCategoryRef) {
        if (hnzsxh5CityCategoryRefService.updateById(hnzsxh5CityCategoryRef)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    @OperationLog(value = "批量添加地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("批量添加H5即时受理-地市-对应-模块分类表（关系表）")
    @PostMapping("/avoid/batchInsert")
    public ApiResult<?> saveBatch(@RequestBody List<Hnzsxh5CityCategoryRef> list) {
        if (hnzsxh5CityCategoryRefService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryRef:update')")
    @OperationLog(value = "批量修改地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("批量修改H5即时受理-地市-对应-模块分类表（关系表）")
    @PostMapping("/batchUpdate")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<Hnzsxh5CityCategoryRef> batchParam) {
        if (batchParam.update(hnzsxh5CityCategoryRefService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除地市-模块分类关系
     * @param param 包含id的参数
     * @return 删除结果
     * <AUTHOR>
     * @date 2025-05-15
     */
    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryRef:remove')")
    @OperationLog(value = "删除地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_CLICK, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("删除地市-模块分类关系")
    @PostMapping("/delete")
    public ApiResult<?> delete(@RequestBody Map<String, Integer> param) {
        Integer id = param.get("id");
        if (id == null) {
            return fail("关系ID不能为空");
        }
        
        if (hnzsxh5CityCategoryRefService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 根据地市编码查询关联的分类信息
     * @param cityCode 地市编码
     * @return 分类信息列表
     */
    @OperationLog(value = "查询地市关联的分类信息", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("根据地市编码查询关联的分类信息")
    @PostMapping("/getCategoriesByCityCode")
    public ApiResult<?> getCategoriesByCityCode(@RequestBody Map<String, String> param) {
        String cityCode = param.get("cityCode");
        if (cityCode == null || cityCode.isEmpty()) {
            return fail("地市编码不能为空");
        }
        
        // 查询关联的地市-分类关系
        LambdaQueryWrapper<Hnzsxh5CityCategoryRef> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Hnzsxh5CityCategoryRef::getCityCode, cityCode)
               .eq(Hnzsxh5CityCategoryRef::getStatus, Constants.HNZSXH5_CATEGORY_STATE_EFFECT);
        
        List<Hnzsxh5CityCategoryRef> refList = hnzsxh5CityCategoryRefService.list(wrapper);
        if (refList == null || refList.isEmpty()) {
            return success(new ArrayList<>());
        }
        
        // 获取所有分类ID
        List<Integer> categoryIds = refList.stream()
                .map(Hnzsxh5CityCategoryRef::getCategoryId)
                .collect(Collectors.toList());
        
        // 查询分类信息，只返回状态为有效的数据
        List<Hnzsxh5Category> categories = hnzsxh5CategoryService.listValidByIds(categoryIds);
        
        // 转换为前端需要的格式
        List<Map<String, Object>> result = new ArrayList<>();
        for (Hnzsxh5CityCategoryRef ref : refList) {
            for (Hnzsxh5Category category : categories) {
                if (ref.getCategoryId().equals(category.getId())) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("refId", ref.getId());
                    item.put("categoryId", category.getId());
                    item.put("categoryName", category.getModuleTypeName());
                    item.put("categoryCode", category.getModuleTypeCode());
                    result.add(item);
                    break;
                }
            }
        }
        
        return success(result);
    }

    /**
     * 批量删除地市-模块分类关系
     * 支持一次性删除多条记录，减少API调用次数，提高性能
     * @param param 包含ids的参数，ids为关系ID数组
     * @return 删除结果
     * <AUTHOR>
     * @date 2025-05-15
     */
    @PreAuthorize("hasAuthority('hnzsxH5:hnzsxh5CityCategoryRef:remove')")
    @OperationLog(value = "批量删除地市-模块分类关系", logType = SysLogConstant.LOG_TYPE_BATCH, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("批量删除地市-模块分类关系")
    @PostMapping("/batchDelete")
    public ApiResult<?> batchDelete(@RequestBody Map<String, List<Integer>> param) {
        List<Integer> ids = param.get("ids");
        if (ids == null || ids.isEmpty()) {
            return fail("关系ID不能为空");
        }

        // 使用单次数据库操作批量删除，减少数据库交互次数
        if (hnzsxh5CityCategoryRefService.removeByIds(ids)) {
            return success("批量删除成功，共删除" + ids.size() + "条记录");
        }
        return fail("批量删除失败");
    }

    /**
     * 批量获取多个地市的分类关系
     * 优化前端性能，减少API调用次数，支持一次性获取多个地市的分类关系数据
     * @param param 包含cityCodes的参数，cityCodes为地市编码数组
     * @return 地市编码为key，分类关系数组为value的对象
     * <AUTHOR> Assistant
     * @date 2025-08-01
     */
    @OperationLog(value = "批量获取多个地市的分类关系", logType = SysLogConstant.LOG_TYPE_QUERY, operateMenu = "掌上销H5即时受理/地市-模块分类关系管理")
    @ApiOperation("批量获取多个地市的分类关系")
    @PostMapping("/batchGetCategoriesByCityCodes")
    public ApiResult<?> batchGetCategoriesByCityCodes(@RequestBody Map<String, List<String>> param) {
        List<String> cityCodes = param.get("cityCodes");
        if (cityCodes == null || cityCodes.isEmpty()) {
            return fail("地市编码不能为空");
        }

        try {
            // 构建查询条件：地市编码在指定列表中且状态为有效
            LambdaQueryWrapper<Hnzsxh5CityCategoryRef> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Hnzsxh5CityCategoryRef::getCityCode, cityCodes)
                   .eq(Hnzsxh5CityCategoryRef::getStatus, Constants.HNZSXH5_CATEGORY_STATE_EFFECT);

            List<Hnzsxh5CityCategoryRef> allRefs = hnzsxh5CityCategoryRefService.list(wrapper);

            // 获取所有涉及的分类ID
            List<Integer> categoryIds = allRefs.stream()
                    .map(Hnzsxh5CityCategoryRef::getCategoryId)
                    .distinct()
                    .collect(Collectors.toList());

            // 批量查询分类信息
            List<Hnzsxh5Category> categories = new ArrayList<>();
            if (!categoryIds.isEmpty()) {
                categories = hnzsxh5CategoryService.listValidByIds(categoryIds);
            }

            // 按地市编码分组组织结果
            Map<String, List<Map<String, Object>>> result = new HashMap<>();

            // 初始化所有地市的结果为空列表
            for (String cityCode : cityCodes) {
                result.put(cityCode, new ArrayList<>());
            }

            // 填充实际数据
            for (Hnzsxh5CityCategoryRef ref : allRefs) {
                String cityCode = ref.getCityCode();

                // 查找对应的分类信息
                for (Hnzsxh5Category category : categories) {
                    if (ref.getCategoryId().equals(category.getId())) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("refId", ref.getId());
                        item.put("categoryId", category.getId());
                        item.put("categoryName", category.getModuleTypeName());
                        item.put("categoryCode", category.getModuleTypeCode());

                        result.get(cityCode).add(item);
                        break;
                    }
                }
            }

            return success(result);
        } catch (Exception e) {
            return fail("批量获取地市分类关系失败：" + e.getMessage());
        }
    }

}
