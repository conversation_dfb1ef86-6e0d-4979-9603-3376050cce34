{"ast": null, "code": "import \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport { getAllCities, getCategories, getModules, getAttributeTypes, getTags, getCategoriesByCityCode, getModulesByCityCategoryRefId, saveCityCategoryRef, batchSaveCityCategoryRef, saveCityCategoryModuleRef, batchSaveCityCategoryModuleRef, saveModuleAttributeTypeRef, batchSaveModuleAttributeTypeRef, saveModuleTagRef, batchSaveModuleTagRef, getAttributeTypesByModuleRefId, getTagsByModuleRefId, deleteCityCategoryRef, deleteCityCategoryModuleRef, deleteModuleAttributeTypeRef, deleteModuleTagRef, batchDeleteCityCategoryRef, batchDeleteCityCategoryModuleRef, batchDeleteModuleAttributeTypeRef, batchDeleteModuleTagRef, batchUpdateRank, batchUpdateRankSx } from '@/api/hnzsxH5/configRelation';\nimport { configRelationDataLoader, configRelationSaveOptimizer } from '@/utils/configRelationOptimizer';\nimport Sortable from 'sortablejs';\nexport default {\n  name: 'ConfigRelation',\n\n  data() {\n    return {\n      // 当前步骤\n      currentStep: 1,\n      // 地市相关数据\n      cities: [],\n      // 所有地市\n      selectedCities: [],\n      // 选中的地市\n      selectAllCities: false,\n      // 是否全选地市\n      isIndeterminate: false,\n      // 是否半选状态\n      // 模块分类相关数据\n      categories: [],\n      // 所有模块分类\n      categoryData: [],\n      // 穿梭框数据\n      selectedCategories: [],\n      // 选中的模块分类\n      // 模块相关数据\n      modules: [],\n      // 所有模块\n      moduleData: [],\n      // 穿梭框数据\n      selectedModules: [],\n      // 选中的模块\n      // 商品属性和标签相关数据\n      attributeTagTab: 'attribute',\n      // 当前选中的选项卡\n      attributeTypes: [],\n      // 所有商品属性类别\n      attributeTypeData: [],\n      // 穿梭框数据\n      selectedAttributeTypes: [],\n      // 选中的商品属性类别\n      tags: [],\n      // 所有商品标签\n      tagData: [],\n      // 穿梭框数据\n      selectedTags: [],\n      // 选中的商品标签\n      // 保存中间数据的映射关系\n      cityCategoryRefMap: {},\n      // 地市-模块分类关系映射\n      cityCategoryModuleRefMap: {},\n      // 地市分类-模块关系映射\n      // 用于存储ID与关联关系的映射\n      categoryRefIdMap: {},\n      // 分类ID到关联ID的映射\n      moduleRefIdMap: {},\n      // 模块ID到关联ID的映射 \n      attributeTypeRefIdMap: {},\n      // 属性类型ID到关联ID的映射\n      tagRefIdMap: {},\n      // 标签ID到关联ID的映射\n      // 新增的变量\n      cityCategories: {},\n      // 存储每个地市的模块分类\n      cityModules: {},\n      // 存储每个地市和分类的模块\n      attributeTagTabs: {},\n      // 存储每个模块的属性标签选项卡\n      moduleAttributes: {},\n      // 存储每个模块的商品属性类别\n      moduleTags: {},\n      // 存储每个模块的商品标签\n      expandedCities: [],\n      // 存储展开的地市\n      expandedCityCategories: [],\n      // 存储展开的地市和分类\n      expandedCityModules: [],\n      // 存储展开的地市和模块\n      sortableRight: null,\n      // 用于保存sortable实例\n      draggedItem: null,\n      // 当前拖拽项\n      draggedItem2: null,\n      // 当前拖拽项\n      transferKey: 0\n    };\n  },\n\n  computed: {\n    hasCategorySelected() {\n      for (const cityCode in this.cityCategories) {\n        if ((this.cityCategories[cityCode] || []).length > 0) {\n          return true;\n        }\n      }\n\n      return false;\n    },\n\n    hasModuleSelected() {\n      for (const key in this.cityModules) {\n        if ((this.cityModules[key] || []).length > 0) {\n          return true;\n        }\n      }\n\n      return false;\n    }\n\n  },\n\n  created() {\n    this.fetchInitialData();\n  },\n\n  mounted() {\n    // 确保页面加载后能看到分类数据\n    if (this.categories.length === 0 || this.categoryData.length === 0) {\n      this.fetchCategoryData();\n    }\n  },\n\n  methods: {\n    // 排序\n    // 开始拖拽\n    dragStart(event, option) {\n      console.log(event, '================', option);\n      this.draggedItem = option;\n      event.dataTransfer.effectAllowed = 'move'; // 添加拖拽样式\n\n      event.target.classList.add('dragging'); // 设置拖拽数据\n\n      event.dataTransfer.setData('text/plain', JSON.stringify(option));\n    },\n\n    // 放置 - 属性类别\n    async drop(event, targetOption, cityCode, categoryId, moduleId) {\n      event.preventDefault();\n      console.log(targetOption, '==targetOption');\n      if (!this.draggedItem || this.draggedItem.key === targetOption.key) return;\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      const currentAttributes = this.moduleAttributes[key] || [];\n      const currentIndex = currentAttributes.indexOf(this.draggedItem.key);\n      const targetIndex = currentAttributes.indexOf(targetOption.key);\n      console.log(targetIndex, 'targetIndex');\n      console.log(currentIndex, 'currentIndex');\n      console.log(currentAttributes, '==currentAttributes');\n\n      if (currentIndex > -1 && targetIndex > -1) {\n        // 重新排序数组\n        const newAttributes = [...currentAttributes];\n        newAttributes.splice(currentIndex, 1);\n        newAttributes.splice(targetIndex, 0, this.draggedItem.key); // 更新属性数组\n\n        this.updateModuleAttributes(cityCode, categoryId, moduleId, newAttributes); // 保存排序到后台\n\n        await this.saveAttributeSort(cityCode, categoryId, moduleId, newAttributes);\n      } // 移除拖拽样式\n\n\n      const draggingElements = document.querySelectorAll('.dragging');\n      draggingElements.forEach(el => el.classList.remove('dragging'));\n      this.draggedItem = null;\n    },\n\n    dragStart2(event, option) {\n      console.log(event, '================', option);\n      this.draggedItem2 = option;\n      event.dataTransfer.effectAllowed = 'move';\n      event.dataTransfer.setData('text/plain', JSON.stringify(option)); // 添加拖拽样式\n\n      event.target.classList.add('dragging');\n    },\n\n    // 放置 - 标签\n    async drop2(event, targetOption, cityCode, categoryId, moduleId) {\n      event.preventDefault();\n      if (!this.draggedItem2 || this.draggedItem2.key === targetOption.key) return;\n      const key = `${cityCode}-${categoryId}-${moduleId}`; // 创建当前数据的深拷贝\n\n      const currentTags = JSON.parse(JSON.stringify(this.moduleTags[key] || []));\n      console.log(currentTags, '=currentTags');\n      const currentIndex = currentTags.indexOf(this.draggedItem2.key);\n      const targetIndex = currentTags.indexOf(targetOption.key);\n\n      if (currentIndex > -1 && targetIndex > -1) {\n        // 创建新数组\n        const newTags = [...currentTags]; // 移除拖拽项\n\n        newTags.splice(currentIndex, 1); // 插入到目标位置\n\n        newTags.splice(targetIndex, 0, this.draggedItem2.key);\n        console.log(newTags, '==newTags'); // 更新数据\n\n        await this.updateModuleTags(cityCode, categoryId, moduleId, newTags); // 保存排序到后台\n\n        await this.saveTagSort(cityCode, categoryId, moduleId, newTags);\n      } // 移除拖拽样式\n\n\n      const draggingElements = document.querySelectorAll('.dragging');\n      draggingElements.forEach(el => el.classList.remove('dragging'));\n      this.draggedItem2 = null;\n    },\n\n    // 获取分类数据\n    async fetchCategoryData() {\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '加载分类数据中...'\n        });\n        const categoriesRes = await getCategories({\n          status: 1 // 有效状态\n\n        });\n        console.log('单独获取分类数据结果:', categoriesRes);\n\n        if (categoriesRes && categoriesRes.rows) {\n          this.categories = categoriesRes.rows;\n          this.categoryData = this.categories.map(item => ({\n            key: item.id,\n            label: item.moduleTypeName,\n            disabled: false\n          }));\n        } else if (Array.isArray(categoriesRes)) {\n          this.categories = categoriesRes;\n          this.categoryData = this.categories.map(item => ({\n            key: item.id,\n            label: item.moduleTypeName,\n            disabled: false\n          }));\n        }\n\n        loading.close();\n      } catch (error) {\n        console.error('获取分类数据失败:', error);\n        this.$message.error('加载分类数据失败');\n      }\n    },\n\n    // 获取初始数据\n    async fetchInitialData() {\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '加载数据中...'\n        }); // 获取地市数据\n\n        this.cities = await getAllCities(); // 获取模块分类数据\n\n        const categoriesRes = await getCategories();\n        console.log('分类数据结果:', categoriesRes);\n\n        if (categoriesRes && categoriesRes.rows) {\n          this.categories = categoriesRes.rows;\n          this.categoryData = this.categories.map(item => ({\n            key: item.id,\n            label: item.moduleTypeName,\n            disabled: false\n          }));\n        } else if (Array.isArray(categoriesRes)) {\n          this.categories = categoriesRes;\n          this.categoryData = this.categories.map(item => ({\n            key: item.id,\n            label: item.moduleTypeName,\n            disabled: false\n          }));\n        } // 获取模块数据\n\n\n        const modulesRes = await getModules();\n\n        if (modulesRes && modulesRes.rows) {\n          this.modules = modulesRes.rows;\n          this.moduleData = modulesRes.rows.map(item => ({\n            key: item.id,\n            label: item.moduleName,\n            disabled: false\n          }));\n        } else if (Array.isArray(modulesRes)) {\n          this.modules = modulesRes;\n          this.moduleData = modulesRes.map(item => ({\n            key: item.id,\n            label: item.moduleName,\n            disabled: false\n          }));\n        } // 获取商品属性类别数据\n\n\n        const attributeTypesRes = await getAttributeTypes();\n\n        if (attributeTypesRes && attributeTypesRes.rows) {\n          this.attributeTypes = attributeTypesRes.rows;\n          this.attributeTypeData = attributeTypesRes.rows.map(item => ({\n            key: item.id,\n            label: item.attributeTypeName,\n            disabled: false\n          }));\n        } else if (Array.isArray(attributeTypesRes)) {\n          this.attributeTypes = attributeTypesRes;\n          this.attributeTypeData = attributeTypesRes.map(item => ({\n            key: item.id,\n            label: item.attributeTypeName,\n            disabled: false\n          }));\n        } // 获取商品标签数据\n\n\n        const tagsRes = await getTags();\n\n        if (tagsRes && tagsRes.rows) {\n          this.tags = tagsRes.rows;\n          this.tagData = tagsRes.rows.map(item => ({\n            key: item.id,\n            label: item.tagName,\n            disabled: false\n          }));\n        } else if (Array.isArray(tagsRes)) {\n          this.tags = tagsRes;\n          this.tagData = tagsRes.map(item => ({\n            key: item.id,\n            label: item.tagName,\n            disabled: false\n          }));\n        }\n\n        loading.close();\n      } catch (error) {\n        this.$message.error(error.message || '数据加载失败');\n      }\n    },\n\n    // 全选地市\n    handleCheckAllCitiesChange(checked) {\n      this.selectedCities = checked ? this.cities.map(item => item.cityCode) : [];\n      this.isIndeterminate = false;\n    },\n\n    // 地市选择变化\n    handleCitiesChange(value) {\n      const checkedCount = value.length;\n      this.selectAllCities = checkedCount === this.cities.length;\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;\n    },\n\n    // 获取地市名称\n    getCityName(cityCode) {\n      const city = this.cities.find(item => item.cityCode === cityCode);\n      return city ? city.cityName : cityCode;\n    },\n\n    // 获取模块分类名称\n    getCategoryName(categoryId) {\n      const category = this.categories.find(item => item.id === categoryId);\n      return category ? category.moduleTypeName : categoryId;\n    },\n\n    // 获取模块名称\n    getModuleName(moduleId) {\n      const module = this.modules.find(item => item.id === moduleId);\n      return module ? module.moduleName : moduleId;\n    },\n\n    // 上一步\n    prevStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    },\n\n    // 下一步\n    async nextStep() {\n      if (this.currentStep < 4) {\n        // 根据当前步骤进行相应的预加载操作\n        if (this.currentStep === 1) {\n          // 从步骤1到步骤2：预加载已选地市的模块分类关系\n          const loading = this.$loading({\n            lock: true,\n            text: '加载地市关联的模块分类中...'\n          });\n\n          try {\n            // 清空之前的关联映射\n            this.cityCategoryRefMap = {};\n            this.cityCategories = {}; // 重置cityCategories\n            // 加载地市-模块分类关系\n\n            await this.loadCategoryRelations(); // 展开有分类的地市\n\n            for (const cityCode of this.selectedCities) {\n              if ((this.cityCategories[cityCode] || []).length > 0 && !this.expandedCities.includes(cityCode)) {\n                this.expandedCities.push(cityCode);\n              }\n            } // 同步到selectedCategories\n\n\n            this.syncAllCategories();\n          } catch (error) {\n            console.error('预加载地市-分类关系失败:', error);\n            this.$message.error('加载关联数据失败，请重试');\n          } finally {\n            loading.close();\n          }\n        } else if (this.currentStep === 2) {\n          // 从步骤2到步骤3：预加载已选地市和分类的模块关系\n          const loading = this.$loading({\n            lock: true,\n            text: '加载分类关联的模块中...'\n          });\n\n          try {\n            // 清空之前的模块关联映射\n            this.cityCategoryModuleRefMap = {};\n            this.cityModules = {}; // 重置cityModules\n            // 加载地市分类-模块关系\n\n            await this.loadModuleRelations(); // 展开有模块的地市-分类\n\n            for (const cityCode of this.selectedCities) {\n              for (const categoryId of this.cityCategories[cityCode] || []) {\n                const key = `${cityCode}-${categoryId}`;\n\n                if ((this.cityModules[key] || []).length > 0 && !this.expandedCityCategories.includes(key)) {\n                  this.expandedCityCategories.push(key);\n                }\n              }\n            } // 同步到selectedModules\n\n\n            this.syncAllModules();\n          } catch (error) {\n            console.error('预加载分类-模块关系失败:', error);\n            this.$message.error('加载关联数据失败，请重试');\n          } finally {\n            loading.close();\n          }\n        } else if (this.currentStep === 3) {\n          // 从步骤3到步骤4：预加载已选模块的商品属性和标签关系\n          const loading = this.$loading({\n            lock: true,\n            text: '加载模块关联的属性和标签中...'\n          });\n\n          try {\n            // 重置属性和标签相关数据\n            this.moduleAttributes = {};\n            this.moduleTags = {};\n            this.attributeTagTabs = {}; // 加载模块-商品属性类别和标签关系\n\n            await this.loadAttributeAndTagRelations(); // 展开有属性或标签的模块组合\n\n            for (const cityCode of this.selectedCities) {\n              for (const categoryId of this.cityCategories[cityCode] || []) {\n                const cityCategoryKey = `${cityCode}-${categoryId}`;\n\n                for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n                  const key = `${cityCode}-${categoryId}-${moduleId}`;\n\n                  if (((this.moduleAttributes[key] || []).length > 0 || (this.moduleTags[key] || []).length > 0) && !this.expandedCityModules.includes(key)) {\n                    this.expandedCityModules.push(key);\n                  }\n                }\n              }\n            } // 同步到selectedAttributeTypes和selectedTags\n\n\n            this.syncAllAttributesAndTags();\n          } catch (error) {\n            console.error('预加载模块-属性标签关系失败:', error);\n            this.$message.error('加载关联数据失败，请重试');\n          } finally {\n            loading.close();\n          }\n        }\n\n        this.currentStep++;\n      }\n    },\n\n    // 优化后的保存配置\n    async saveConfiguration() {\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '保存配置中...'\n        }); // 使用优化的保存流程\n\n        await configRelationSaveOptimizer.optimizedSaveConfiguration(this);\n        loading.close();\n        this.$message.success('配置保存成功'); // 重置表单\n\n        this.resetForm(); // 清除优化器缓存\n\n        configRelationSaveOptimizer.clearCache();\n        configRelationDataLoader.clearCache();\n      } catch (error) {\n        console.error('保存配置失败:', error);\n        this.$message.error(error.message || '配置保存失败');\n      }\n    },\n\n    // 保留原有的保存配置方法作为备用\n    async saveConfigurationLegacy() {\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '保存配置中...'\n        }); // 1. 保存前准备，预加载现有关系数据减少后续查询\n\n        await this.prepareForSave(); // 2. 保存地市-模块分类关系\n\n        await this.saveCityCategoryRelations(); // 3. 保存地市分类-模块关系\n\n        await this.saveCityCategoryModuleRelations(); // 4. 保存模块-商品属性类别关系和模块-商品标签关系\n\n        await this.saveModuleAttributeTagRelations(); // 5. 删除不再需要的关联关系\n\n        await this.deleteUnusedRelations();\n        loading.close();\n        this.$message.success('配置保存成功'); // 重置表单\n\n        this.resetForm();\n      } catch (error) {\n        this.$message.error(error.message || '配置保存失败');\n      }\n    },\n\n    // 保存前准备，预加载现有关系数据减少后续查询\n    async prepareForSave() {\n      // 预先获取已有的地市-分类关联关系\n      await Promise.all(this.selectedCities.map(async cityCode => {\n        const existingCategoryRefs = await getCategoriesByCityCode(cityCode); // 将已有关联关系存入映射中\n\n        for (const ref of existingCategoryRefs) {\n          const key = `${cityCode}_${ref.categoryId}`;\n          this.cityCategoryRefMap[key] = ref.refId; // 预先加载分类对应的模块关系\n\n          if (this.selectedCategories.includes(ref.categoryId)) {\n            const existingModuleRefs = await getModulesByCityCategoryRefId(ref.refId); // 将已有模块关联存入映射中\n\n            for (const moduleRef of existingModuleRefs) {\n              const key = `${ref.refId}_${moduleRef.moduleId}`;\n              this.cityCategoryModuleRefMap[key] = moduleRef.refId;\n            }\n          }\n        }\n      }));\n    },\n\n    // 删除不再需要的关联关系\n    async deleteUnusedRelations() {\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '正在优化关联关系...'\n        }); // 预先获取所有关系数据，减少API调用次数\n\n        const cityRelationsMap = new Map(); // 存储地市-分类关系\n\n        const moduleRelationsMap = new Map(); // 存储地市分类-模块关系\n\n        const attributeRelationsMap = new Map(); // 存储模块-属性关系\n\n        const tagRelationsMap = new Map(); // 存储模块-标签关系\n        // 记录模块组合的属性和标签集合 (使用组合标识符确保每个分组都被正确处理)\n\n        const groupAttributeMap = new Map(); // 组合标识符 -> 属性ID集合\n\n        const groupTagMap = new Map(); // 组合标识符 -> 标签ID集合\n\n        const moduleToGroupsMap = new Map(); // 模块ID -> 组合标识符列表\n        // 构建组合->属性/标签映射，记录每个组合应该保留的属性和标签\n\n        console.log('开始构建组合->属性/标签映射...');\n\n        for (const cityCode of this.selectedCities) {\n          for (const categoryId of this.cityCategories[cityCode] || []) {\n            const cityCategoryKey = `${cityCode}-${categoryId}`;\n\n            for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n              const groupKey = `${cityCode}-${categoryId}-${moduleId}`; // 维护一个模块ID到所有包含它的组合的映射\n\n              if (!moduleToGroupsMap.has(moduleId)) {\n                moduleToGroupsMap.set(moduleId, []);\n              }\n\n              moduleToGroupsMap.get(moduleId).push(groupKey); // 记录该组合选中的属性\n\n              const selectedAttributes = this.moduleAttributes[groupKey] || [];\n\n              if (!groupAttributeMap.has(groupKey)) {\n                groupAttributeMap.set(groupKey, new Set());\n              }\n\n              selectedAttributes.forEach(attrId => {\n                groupAttributeMap.get(groupKey).add(attrId);\n              }); // 记录该组合选中的标签\n\n              const selectedTags = this.moduleTags[groupKey] || [];\n\n              if (!groupTagMap.has(groupKey)) {\n                groupTagMap.set(groupKey, new Set());\n              }\n\n              selectedTags.forEach(tagId => {\n                groupTagMap.get(groupKey).add(tagId);\n              });\n              console.log(`组合 ${groupKey} 选中了 ${selectedAttributes.length} 个属性和 ${selectedTags.length} 个标签`);\n            }\n          }\n        }\n\n        console.log('开始获取现有关系数据...'); // 一次性获取所有地市的关系数据\n\n        await Promise.all(this.selectedCities.map(async cityCode => {\n          const categoryRefs = await getCategoriesByCityCode(cityCode);\n          cityRelationsMap.set(cityCode, categoryRefs); // 同时获取每个地市-分类下的模块关系\n\n          await Promise.all(categoryRefs.map(async ref => {\n            if (ref.refId) {\n              const moduleRefs = await getModulesByCityCategoryRefId(ref.refId);\n              moduleRelationsMap.set(ref.refId, moduleRefs); // 获取每个模块关系下的属性和标签关系\n\n              await Promise.all(moduleRefs.map(async moduleRef => {\n                if (moduleRef.refId) {\n                  const [attrTypes, tags] = await Promise.all([getAttributeTypesByModuleRefId(moduleRef.refId), getTagsByModuleRefId(moduleRef.refId)]);\n                  attributeRelationsMap.set(moduleRef.refId, attrTypes);\n                  tagRelationsMap.set(moduleRef.refId, tags);\n                }\n              }));\n            }\n          }));\n        })); // 处理数据，确定要删除的项\n\n        const toDeleteCategoryRefIds = []; // 地市-分类关系\n\n        const toDeleteModuleRefIds = []; // 地市分类-模块关系\n\n        const toDeleteAttrRefIds = []; // 模块-属性关系\n\n        const toDeleteTagRefIds = []; // 模块-标签关系\n        // 处理地市-分类关系\n\n        for (const [cityCode, categoryRefs] of cityRelationsMap.entries()) {\n          for (const ref of categoryRefs) {\n            // 检查该地市下是否还有此分类\n            const isCategorySelected = (this.cityCategories[cityCode] || []).includes(ref.categoryId); // 如果分类不在该地市选中列表中，则标记该关系需要删除\n\n            if (!isCategorySelected) {\n              toDeleteCategoryRefIds.push(ref.refId); // 从映射中移除\n\n              const key = `${cityCode}_${ref.categoryId}`;\n              delete this.cityCategoryRefMap[key];\n            }\n          }\n        } // 处理地市分类-模块关系\n\n\n        for (const [cityCategoryRefId, moduleRefs] of moduleRelationsMap.entries()) {\n          for (const moduleRef of moduleRefs) {\n            // 找到对应的地市和分类\n            let found = false;\n            let cityCategoryKey = ''; // 寻找该cityCategoryRefId对应的cityCode和categoryId\n\n            for (const cityCode of this.selectedCities) {\n              for (const categoryId of this.cityCategories[cityCode] || []) {\n                const key = `${cityCode}_${categoryId}`;\n\n                if (this.cityCategoryRefMap[key] === cityCategoryRefId) {\n                  cityCategoryKey = `${cityCode}-${categoryId}`;\n                  found = true;\n                  break;\n                }\n              }\n\n              if (found) break;\n            } // 如果找到对应的地市-分类组合，检查模块是否在该组合下选中\n\n\n            if (found) {\n              const isModuleSelected = (this.cityModules[cityCategoryKey] || []).includes(moduleRef.moduleId); // 如果模块不在该组合的选中列表中，则标记该关系需要删除\n\n              if (!isModuleSelected) {\n                toDeleteModuleRefIds.push(moduleRef.refId); // 从映射中移除\n\n                for (const key in this.cityCategoryModuleRefMap) {\n                  if (this.cityCategoryModuleRefMap[key] === moduleRef.refId) {\n                    delete this.cityCategoryModuleRefMap[key];\n                    break;\n                  }\n                }\n              }\n            }\n          }\n        } // 获取城市代码-分类ID-模块ID的映射，用于后续定位模块所属的组\n\n\n        const moduleRefToGroupKeyMap = new Map(); // 模块关系ID -> 所属组key\n\n        for (const cityCode of this.selectedCities) {\n          for (const categoryId of this.cityCategories[cityCode] || []) {\n            const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n            if (!cityCategoryRefId) continue;\n\n            for (const moduleId of this.cityModules[`${cityCode}-${categoryId}`] || []) {\n              const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n              if (!moduleRefId) continue;\n              moduleRefToGroupKeyMap.set(moduleRefId, `${cityCode}-${categoryId}-${moduleId}`);\n            }\n          }\n        } // 处理模块-属性关系\n\n\n        for (const [moduleRefId, attrTypes] of attributeRelationsMap.entries()) {\n          // 找到该模块关系对应的组合key\n          const groupKey = moduleRefToGroupKeyMap.get(moduleRefId);\n\n          if (groupKey) {\n            // 获取该组合中选中的属性ID集合\n            const selectedAttributeIds = groupAttributeMap.get(groupKey) || new Set(); // 处理当前模块的属性关系\n\n            for (const attrType of attrTypes) {\n              // 如果属性不在该组合选中列表中，则标记该关系需要删除\n              if (!selectedAttributeIds.has(attrType.attributeTypeId)) {\n                toDeleteAttrRefIds.push(attrType.refId);\n                console.log(`标记要删除的属性关系: 组合=${groupKey}, 属性ID=${attrType.attributeTypeId}, 关系ID=${attrType.refId}`);\n              }\n            }\n          }\n        } // 处理模块-标签关系\n\n\n        for (const [moduleRefId, tags] of tagRelationsMap.entries()) {\n          // 找到该模块关系对应的组合key\n          const groupKey = moduleRefToGroupKeyMap.get(moduleRefId);\n\n          if (groupKey) {\n            // 获取该组合中选中的标签ID集合\n            const selectedTagIds = groupTagMap.get(groupKey) || new Set(); // 处理当前模块的标签关系\n\n            for (const tag of tags) {\n              // 如果标签不在该组合选中列表中，则标记该关系需要删除\n              if (!selectedTagIds.has(tag.tagId)) {\n                toDeleteTagRefIds.push(tag.refId);\n                console.log(`标记要删除的标签关系: 组合=${groupKey}, 标签ID=${tag.tagId}, 关系ID=${tag.refId}`);\n              }\n            }\n          }\n        }\n\n        console.log({\n          '要删除的地市-分类关系': toDeleteCategoryRefIds.length,\n          '要删除的地市分类-模块关系': toDeleteModuleRefIds.length,\n          '要删除的模块-属性关系': toDeleteAttrRefIds.length,\n          '要删除的模块-标签关系': toDeleteTagRefIds.length\n        }); // 批量删除关系，并行执行以提高速度\n\n        const deletePromises = [];\n\n        if (toDeleteCategoryRefIds.length > 0) {\n          deletePromises.push(batchDeleteCityCategoryRef(toDeleteCategoryRefIds).then(() => console.log(`批量删除地市-模块分类关系, 共${toDeleteCategoryRefIds.length}条`)));\n        }\n\n        if (toDeleteModuleRefIds.length > 0) {\n          deletePromises.push(batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds).then(() => console.log(`批量删除地市分类-模块关系, 共${toDeleteModuleRefIds.length}条`)));\n        }\n\n        if (toDeleteAttrRefIds.length > 0) {\n          deletePromises.push(batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds).then(() => console.log(`批量删除模块-商品属性类别关系, 共${toDeleteAttrRefIds.length}条`)));\n        }\n\n        if (toDeleteTagRefIds.length > 0) {\n          deletePromises.push(batchDeleteModuleTagRef(toDeleteTagRefIds).then(() => console.log(`批量删除模块-商品标签关系, 共${toDeleteTagRefIds.length}条`)));\n        }\n\n        await Promise.all(deletePromises);\n        loading.close();\n      } catch (error) {\n        console.error('删除不再需要的关联关系失败:', error);\n        throw new Error('删除不再需要的关联关系失败');\n      }\n    },\n\n    // 保存地市-模块分类关系\n    async saveCityCategoryRelations() {\n      // 构建需要保存的数据\n      const cityCategoryRefs = []; // 使用选中的地市和每个地市下的分类，确保所有分组都被考虑\n\n      for (const cityCode of this.selectedCities) {\n        // 获取该地市下选中的所有分类\n        const categories = this.cityCategories[cityCode] || []; // 只为尚未关联的分类创建新的关联关系\n\n        for (const categoryId of categories) {\n          const key = `${cityCode}_${categoryId}`; // 如果此地市和分类的关联关系不存在，则创建新的\n\n          if (!this.cityCategoryRefMap[key]) {\n            cityCategoryRefs.push({\n              cityCode: cityCode,\n              categoryId: categoryId,\n              status: 1,\n              // 有效状态\n              sort: 1 // 默认排序\n\n            });\n          }\n        }\n      } // 批量保存新增的关联关系\n\n\n      if (cityCategoryRefs.length > 0) {\n        console.log(`准备保存${cityCategoryRefs.length}个地市-模块分类关系`);\n        const result = await batchSaveCityCategoryRef(cityCategoryRefs);\n        console.log('保存地市-模块分类关系结果:', result); // 重新获取关联ID，确保映射表包含新创建的关联关系\n\n        await this.fetchCityCategoriesAfterSave();\n      }\n    },\n\n    // 保存地市-模块分类关系后重新获取关联ID\n    async fetchCityCategoriesAfterSave() {\n      // 只获取新添加的关系\n      const promises = this.selectedCities.map(async cityCode => {\n        // 查询当前地市下的所有关系\n        const categoryRefs = await getCategoriesByCityCode(cityCode); // 更新映射表\n\n        for (const ref of categoryRefs) {\n          const key = `${cityCode}_${ref.categoryId}`;\n\n          if (!this.cityCategoryRefMap[key]) {\n            this.cityCategoryRefMap[key] = ref.refId;\n          }\n        }\n      });\n      await Promise.all(promises);\n    },\n\n    // 保存地市分类-模块关系\n    async saveCityCategoryModuleRelations() {\n      // 构建需要保存的数据\n      const cityCategoryModuleRefs = [];\n      const processedGroups = new Set(); // 记录已处理的地市-分类组合\n      // 遍历所有地市和分类组合\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n          const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n          if (!refId) {\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\n            continue;\n          } // 标记该地市-分类组合为已处理\n\n\n          processedGroups.add(cityCategoryKey); // 获取该地市-分类下选中的所有模块\n\n          const modules = this.cityModules[cityCategoryKey] || []; // 只为尚未关联的模块创建新的关联关系\n\n          for (const moduleId of modules) {\n            const key = `${refId}_${moduleId}`; // 如果此地市分类和模块的关联关系不存在，则创建新的\n\n            if (!this.cityCategoryModuleRefMap[key]) {\n              cityCategoryModuleRefs.push({\n                cityCategoryRefId: refId,\n                moduleId: moduleId,\n                status: 1,\n                // 有效状态\n                isOneBeat: 1 // 一号一拍开关，默认开启\n                // sort: 1 // 默认排序\n\n              });\n            }\n          }\n        }\n      } // 批量保存新增的关联关系\n\n\n      if (cityCategoryModuleRefs.length > 0) {\n        console.log(`准备保存${cityCategoryModuleRefs.length}个地市分类-模块关系，涉及${processedGroups.size}个地市-分类组合`);\n        const result = await batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);\n        console.log('保存地市分类-模块关系结果:', result); // 重新获取关联ID，确保映射表包含新创建的关联关系\n\n        await this.fetchModuleRefsAfterSave();\n      }\n    },\n\n    // 保存模块关系后重新获取关联ID\n    async fetchModuleRefsAfterSave() {\n      const promises = [];\n      const processedRefIds = new Set(); // 避免重复查询\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.selectedCategories) {\n          const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n          if (refId && !processedRefIds.has(refId)) {\n            processedRefIds.add(refId); // 标记为已处理\n\n            promises.push((async () => {\n              const moduleRefs = await getModulesByCityCategoryRefId(refId); // 更新映射表\n\n              for (const moduleRef of moduleRefs) {\n                const key = `${refId}_${moduleRef.moduleId}`;\n\n                if (!this.cityCategoryModuleRefMap[key]) {\n                  this.cityCategoryModuleRefMap[key] = moduleRef.refId;\n                }\n              }\n            })());\n          }\n        }\n      }\n\n      await Promise.all(promises);\n    },\n\n    // 保存模块相关的属性和标签关系\n    async saveModuleAttributeTagRelations() {\n      console.log(this.selectedCities, '===selectedCities'); // 构建需要保存的商品属性类别关系数据\n\n      const moduleAttributeTypeRefs = []; // 构建需要保存的商品标签关系数据\n\n      const moduleTagRefs = []; // 收集已处理的城市-分类-模块组合，确保每个组都被处理\n\n      const processedGroups = new Set();\n      const allAttributesPromises = [];\n      const allTagsPromises = [];\n      const attributeTypeRefMap = {};\n      const tagRefMap = {}; // 1. 先获取所有的已有关系数据，减少重复API调用\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n          if (!cityCategoryRefId) {\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\n            continue;\n          } // 处理该地市-分类下的所有模块\n\n\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n            const key = `${cityCode}-${categoryId}-${moduleId}`;\n            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n            if (!moduleRefId) {\n              console.warn(`未找到模块${moduleId}的关联ID，跳过处理`);\n              continue;\n            } // 预加载该模块的属性和标签关系数据\n\n\n            const attributePromise = getAttributeTypesByModuleRefId(moduleRefId).then(attributes => {\n              attributeTypeRefMap[moduleRefId] = attributes;\n            });\n            const tagPromise = getTagsByModuleRefId(moduleRefId).then(tags => {\n              tagRefMap[moduleRefId] = tags;\n            });\n            allAttributesPromises.push(attributePromise);\n            allTagsPromises.push(tagPromise);\n          }\n        }\n      } // 等待所有数据加载完成\n\n\n      await Promise.all([...allAttributesPromises, ...allTagsPromises]); // 2. 遍历所有模块组合，确保每个组都被考虑\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n          if (!cityCategoryRefId) {\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\n            continue;\n          } // 处理该地市-分类下的所有模块\n\n\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n            const key = `${cityCode}-${categoryId}-${moduleId}`;\n            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n            if (!moduleRefId) {\n              console.warn(`未找到模块${moduleId}的关联ID，跳过处理`);\n              continue;\n            } // 标记该组合为已处理\n\n\n            processedGroups.add(key); // 获取为该模块选择的属性和标签\n\n            const selectedAttributes = this.moduleAttributes[key] || [];\n            const selectedTags = this.moduleTags[key] || [];\n            console.log(`处理组合：${key}, 选择的属性数量: ${selectedAttributes.length}, 标签数量: ${selectedTags.length}`); // 获取已有的商品属性类别关系\n\n            const existingAttributeTypes = attributeTypeRefMap[moduleRefId] || [];\n            const existingAttributeTypeIds = existingAttributeTypes.map(item => item.attributeTypeId); // 获取已有的商品标签关系\n\n            const existingTags = tagRefMap[moduleRefId] || [];\n            const existingTagIds = existingTags.map(item => item.tagId);\n            console.log(`已有属性数量: ${existingAttributeTypeIds.length}, 标签数量: ${existingTagIds.length}`); // 只为尚未关联的商品属性类别创建新的关联关系\n\n            for (const attributeTypeId of selectedAttributes) {\n              if (!existingAttributeTypeIds.includes(attributeTypeId)) {\n                moduleAttributeTypeRefs.push({\n                  cityCategoryModuleRefId: moduleRefId,\n                  goodsAttributeTypeId: attributeTypeId,\n                  status: 1,\n                  // 有效状态\n                  sort: 1 // 默认排序\n\n                });\n              }\n            } // 只为尚未关联的商品标签创建新的关联关系\n\n\n            for (const tagId of selectedTags) {\n              if (!existingTagIds.includes(tagId)) {\n                moduleTagRefs.push({\n                  goodsTagId: tagId,\n                  cityCategoryModuleRefId: moduleRefId,\n                  status: 1,\n                  // 有效状态\n                  sort: 1 // 默认排序\n\n                });\n              }\n            }\n          }\n        }\n      } // 并行批量保存新增的关系\n\n\n      const savePromises = [];\n\n      if (moduleAttributeTypeRefs.length > 0) {\n        console.log(`准备保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);\n        savePromises.push(batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs).then(result => console.log('保存模块-商品属性类别关系结果:', result)));\n      }\n\n      if (moduleTagRefs.length > 0) {\n        console.log(`准备保存${moduleTagRefs.length}个模块-商品标签关系`);\n        savePromises.push(batchSaveModuleTagRef(moduleTagRefs).then(result => console.log('保存模块-商品标签关系结果:', result)));\n      }\n\n      await Promise.all(savePromises);\n      console.log(`总共处理了${processedGroups.size}个城市-分类-模块组合`);\n    },\n\n    // 重置表单\n    resetForm() {\n      this.currentStep = 1;\n      this.selectedCities = [];\n      this.selectAllCities = false;\n      this.isIndeterminate = false;\n      this.selectedCategories = [];\n      this.selectedModules = [];\n      this.attributeTagTab = 'attribute';\n      this.selectedAttributeTypes = [];\n      this.selectedTags = [];\n      this.cityCategoryRefMap = {};\n      this.cityCategoryModuleRefMap = {};\n      this.categoryRefIdMap = {};\n      this.moduleRefIdMap = {};\n      this.attributeTypeRefIdMap = {};\n      this.tagRefIdMap = {};\n      this.cityCategories = {};\n      this.cityModules = {};\n      this.attributeTagTabs = {};\n      this.moduleAttributes = {};\n      this.moduleTags = {};\n      this.expandedCities = [];\n      this.expandedCityCategories = [];\n      this.expandedCityModules = [];\n    },\n\n    // 加载地市-模块分类关系\n    async loadCategoryRelations() {\n      this.categoryRefIdMap = {}; // 重置映射\n      // 首先清空所有地市的分类数据\n\n      for (const cityCode of this.selectedCities) {\n        if (!this.cityCategories[cityCode]) {\n          this.$set(this.cityCategories, cityCode, []);\n        } else {\n          this.cityCategories[cityCode] = [];\n        }\n      }\n\n      try {\n        // 使用优化的批量加载器 - 一次性获取所有地市的分类关系\n        const cityCategories = await configRelationDataLoader.batchLoadCityCategories(this.selectedCities); // 处理返回的数据\n\n        for (const cityCode of this.selectedCities) {\n          const cityName = this.getCityName(cityCode);\n          const categoryRefs = cityCategories[cityCode] || [];\n          console.log(`加载地市 ${cityName}(${cityCode}) 的分类关系，获取到 ${categoryRefs.length} 条数据`); // 将已有关联关系存入映射中\n\n          for (const ref of categoryRefs) {\n            const key = `${cityCode}_${ref.categoryId}`;\n            this.cityCategoryRefMap[key] = ref.refId; // 直接将分类ID添加到对应地市的分类列表中\n\n            if (!this.cityCategories[cityCode].includes(ref.categoryId)) {\n              this.cityCategories[cityCode].push(ref.categoryId);\n            } // 添加到ID映射表，使用cityCode+categoryId作为唯一键\n\n\n            const mapKey = `${cityCode}_${ref.categoryId}`;\n            this.categoryRefIdMap[mapKey] = {\n              refId: ref.refId,\n              cityCode: cityCode,\n              cityName: cityName,\n              categoryId: ref.categoryId,\n              categoryName: ref.categoryName\n            };\n          }\n        }\n      } catch (error) {\n        console.error('批量加载地市分类关系失败:', error);\n        throw error;\n      } // 打印每个地市的分类数据，用于调试\n\n\n      for (const cityCode of this.selectedCities) {\n        console.log(`地市 ${this.getCityName(cityCode)}(${cityCode}) 的分类数量: ${this.cityCategories[cityCode].length}`);\n      }\n    },\n\n    // 加载地市分类-模块关系\n    async loadModuleRelations() {\n      this.moduleRefIdMap = {}; // 重置映射\n      // 清空所有组合的模块数据\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const key = `${cityCode}-${categoryId}`;\n\n          if (!this.cityModules[key]) {\n            this.$set(this.cityModules, key, []);\n          } else {\n            this.cityModules[key] = [];\n          }\n        }\n      }\n\n      try {\n        // 收集所有需要查询的地市分类关系ID\n        const cityCategoryRefIds = [];\n        const refIdToInfoMap = {}; // 关系ID到地市分类信息的映射\n\n        for (const cityCode of this.selectedCities) {\n          for (const categoryId of this.cityCategories[cityCode] || []) {\n            const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n            if (refId) {\n              cityCategoryRefIds.push(refId);\n              refIdToInfoMap[refId] = {\n                cityCode,\n                categoryId,\n                cityName: this.getCityName(cityCode),\n                categoryName: this.getCategoryName(categoryId),\n                cityCategoryKey: `${cityCode}-${categoryId}`\n              };\n            }\n          }\n        }\n\n        if (cityCategoryRefIds.length > 0) {\n          // 使用优化的批量加载器 - 一次性获取所有模块关系\n          const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds); // 处理返回的数据\n\n          for (const refId of cityCategoryRefIds) {\n            const moduleRefs = categoryModules[refId] || [];\n            const info = refIdToInfoMap[refId];\n            console.log(`加载组合 ${info.cityName}(${info.cityCode})-${info.categoryName}(${info.categoryId}) 的模块关系，获取到 ${moduleRefs.length} 条数据`); // 添加到ID映射表，并更新该组合的模块列表\n\n            for (const moduleRef of moduleRefs) {\n              const key = `${refId}_${moduleRef.moduleId}`;\n              this.cityCategoryModuleRefMap[key] = moduleRef.refId; // 直接将模块ID添加到对应地市分类组合的模块列表中\n\n              if (!this.cityModules[info.cityCategoryKey].includes(moduleRef.moduleId)) {\n                this.cityModules[info.cityCategoryKey].push(moduleRef.moduleId);\n              } // 使用完整的组合键来确保不同地市分类组合下的同一模块被分别处理\n\n\n              const mapKey = `${info.cityCode}_${info.categoryId}_${moduleRef.moduleId}`;\n              this.moduleRefIdMap[mapKey] = {\n                refId: moduleRef.refId,\n                cityCode: info.cityCode,\n                cityName: info.cityName,\n                categoryId: info.categoryId,\n                categoryName: info.categoryName,\n                moduleId: moduleRef.moduleId,\n                moduleName: moduleRef.moduleName\n              };\n            }\n          }\n        }\n      } catch (error) {\n        console.error('批量加载模块关系失败:', error);\n        throw error;\n      } // 打印每个组合的模块数据，用于调试\n\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const key = `${cityCode}-${categoryId}`;\n          console.log(`组合 ${this.getCityName(cityCode)}(${cityCode})-${this.getCategoryName(categoryId)}(${categoryId}) 的模块数量: ${(this.cityModules[key] || []).length}`);\n        }\n      }\n    },\n\n    // 加载模块-商品属性类别和标签关系\n    async loadAttributeAndTagRelations() {\n      this.attributeTypeRefIdMap = {}; // 重置映射\n\n      this.tagRefIdMap = {}; // 重置映射\n      // 清空所有组合的属性和标签数据\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n            const key = `${cityCode}-${categoryId}-${moduleId}`; // 初始化该组合的属性和标签列表\n\n            if (!this.moduleAttributes[key]) {\n              this.$set(this.moduleAttributes, key, []);\n            } else {\n              this.moduleAttributes[key] = [];\n            }\n\n            if (!this.moduleTags[key]) {\n              this.$set(this.moduleTags, key, []);\n            } else {\n              this.moduleTags[key] = [];\n            }\n\n            if (!this.attributeTagTabs[key]) {\n              this.$set(this.attributeTagTabs, key, 'attribute');\n            }\n          }\n        }\n      } // 为每个地市-分类-模块组合单独获取属性和标签关系\n\n\n      const loadPromises = [];\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n          if (!cityCategoryRefId) {\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\n            continue;\n          }\n\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n            const moduleKey = `${cityCode}-${categoryId}-${moduleId}`;\n            const moduleName = this.getModuleName(moduleId);\n            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n            if (moduleRefId) {\n              loadPromises.push((async () => {\n                // 加载商品属性类别关系\n                const attributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);\n                console.log(`加载组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${moduleName} 的属性关系，获取到 ${attributeTypes.length} 条数据`);\n\n                for (const attrType of attributeTypes) {\n                  // 将属性ID添加到该组合的属性列表中\n                  if (!this.moduleAttributes[moduleKey].includes(attrType.attributeTypeId)) {\n                    this.moduleAttributes[moduleKey].push(attrType.attributeTypeId);\n                  } // 添加到ID映射表，使用组合键确保不同组合下的同一属性被分别处理\n\n\n                  const mapKey = `${cityCode}_${categoryId}_${moduleId}_${attrType.attributeTypeId}`;\n                  this.attributeTypeRefIdMap[mapKey] = {\n                    refId: attrType.refId,\n                    moduleId: moduleId,\n                    moduleName: moduleName,\n                    attributeTypeId: attrType.attributeTypeId,\n                    attributeTypeName: attrType.attributeTypeName,\n                    groupKey: moduleKey\n                  };\n                } // 加载商品标签关系\n\n\n                const tags = await getTagsByModuleRefId(moduleRefId);\n                console.log(`加载组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${moduleName} 的标签关系，获取到 ${tags.length} 条数据`);\n\n                for (const tag of tags) {\n                  // 将标签ID添加到该组合的标签列表中\n                  if (!this.moduleTags[moduleKey].includes(tag.tagId)) {\n                    this.moduleTags[moduleKey].push(tag.tagId);\n                  } // 添加到ID映射表，使用组合键确保不同组合下的同一标签被分别处理\n\n\n                  const mapKey = `${cityCode}_${categoryId}_${moduleId}_${tag.tagId}`;\n                  this.tagRefIdMap[mapKey] = {\n                    refId: tag.refId,\n                    moduleId: moduleId,\n                    moduleName: moduleName,\n                    tagId: tag.tagId,\n                    tagName: tag.tagName,\n                    groupKey: moduleKey\n                  };\n                }\n              })());\n            }\n          }\n        }\n      }\n\n      await Promise.all(loadPromises); // 打印每个组合的属性和标签数据，用于调试\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n            const key = `${cityCode}-${categoryId}-${moduleId}`;\n            console.log(`组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${this.getModuleName(moduleId)} 的属性数量: ${(this.moduleAttributes[key] || []).length}, 标签数量: ${(this.moduleTags[key] || []).length}`);\n          }\n        }\n      }\n    },\n\n    // 处理移除模块分类\n    async handleRemoveCategories(cityCode, value, direction, movedKeys) {\n      if (direction === 'left') {\n        return; // 只处理从右向左移除（删除）的情况\n      } // 获取被移除的分类ID\n\n\n      const removedCategoryIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\n\n      if (removedCategoryIds.length === 0) {\n        return;\n      }\n\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '正在删除关联关系...'\n        }); // 收集需要删除的关系ID\n\n        const toDeleteRefIds = [];\n\n        for (const categoryId of removedCategoryIds) {\n          // 查找该分类在当前地市下的关系ID\n          const key = `${cityCode}_${categoryId}`;\n          const refId = this.cityCategoryRefMap[key];\n\n          if (refId) {\n            toDeleteRefIds.push(refId); // 从映射中移除\n\n            delete this.cityCategoryRefMap[key]; // 从地市分类列表中移除\n\n            const index = this.cityCategories[cityCode].indexOf(categoryId);\n\n            if (index !== -1) {\n              this.cityCategories[cityCode].splice(index, 1);\n            }\n          }\n        } // 批量删除关系\n\n\n        if (toDeleteRefIds.length > 0) {\n          await batchDeleteCityCategoryRef(toDeleteRefIds);\n        } // 同步到selectedCategories\n\n\n        this.syncAllCategories();\n        loading.close();\n        this.$message.success(`已删除${toDeleteRefIds.length}个模块分类的关联关系`);\n      } catch (error) {\n        this.$message.error(error.message || '删除关联关系失败');\n      }\n    },\n\n    // 处理移除模块\n    async handleRemoveModules(cityCode, categoryId, value, direction, movedKeys) {\n      if (direction === 'left') {\n        return; // 只处理从右向左移除（删除）的情况\n      } // 获取被移除的模块ID\n\n\n      const removedModuleIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\n\n      if (removedModuleIds.length === 0) {\n        return;\n      }\n\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '正在删除关联关系...'\n        }); // 收集需要删除的关系ID\n\n        const toDeleteRefIds = [];\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (cityCategoryRefId) {\n          for (const moduleId of removedModuleIds) {\n            const key = `${cityCategoryRefId}_${moduleId}`;\n            const refId = this.cityCategoryModuleRefMap[key];\n\n            if (refId) {\n              toDeleteRefIds.push(refId); // 从映射中移除\n\n              delete this.cityCategoryModuleRefMap[key]; // 从地市-分类-模块列表中移除\n\n              const cityCategoryKey = `${cityCode}-${categoryId}`;\n              const index = this.cityModules[cityCategoryKey].indexOf(moduleId);\n\n              if (index !== -1) {\n                this.cityModules[cityCategoryKey].splice(index, 1);\n              }\n            }\n          }\n        } // 批量删除关系\n\n\n        if (toDeleteRefIds.length > 0) {\n          await batchDeleteCityCategoryModuleRef(toDeleteRefIds);\n        } // 同步到selectedModules\n\n\n        this.syncAllModules();\n        loading.close();\n        this.$message.success(`已删除${toDeleteRefIds.length}个模块的关联关系`);\n      } catch (error) {\n        this.$message.error(error.message || '删除关联关系失败');\n      }\n    },\n\n    // 处理移除商品属性类别\n    async handleRemoveAttributeTypes(cityCode, categoryId, moduleId, value, direction, movedKeys) {\n      if (direction === 'left') {\n        return; // 只处理从右向左移除（删除）的情况\n      } // 获取被移除的属性类别ID\n\n\n      const removedAttributeTypeIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\n\n      if (removedAttributeTypeIds.length === 0) {\n        return;\n      }\n\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '正在删除关联关系...'\n        }); // 收集需要删除的关系ID\n\n        const toDeleteRefIds = [];\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (cityCategoryRefId) {\n          const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n          if (moduleRefId) {\n            // 获取当前模块的属性类别关系\n            const existingAttributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);\n\n            for (const attributeTypeId of removedAttributeTypeIds) {\n              // 在现有关系中查找对应的refId\n              const attrType = existingAttributeTypes.find(item => item.attributeTypeId === attributeTypeId);\n\n              if (attrType && attrType.refId) {\n                toDeleteRefIds.push(attrType.refId); // 从模块-属性列表中移除\n\n                const key = `${cityCode}-${categoryId}-${moduleId}`;\n                const index = this.moduleAttributes[key].indexOf(attributeTypeId);\n\n                if (index !== -1) {\n                  this.moduleAttributes[key].splice(index, 1);\n                }\n              }\n            }\n          }\n        } // 批量删除关系\n\n\n        if (toDeleteRefIds.length > 0) {\n          await batchDeleteModuleAttributeTypeRef(toDeleteRefIds);\n        } // 同步到selectedAttributeTypes\n\n\n        this.syncAllAttributesAndTags();\n        loading.close();\n        this.$message.success(`已删除${toDeleteRefIds.length}个商品属性类别的关联关系`);\n      } catch (error) {\n        this.$message.error(error.message || '删除关联关系失败');\n      }\n    },\n\n    // 处理移除商品标签\n    async handleRemoveTags(cityCode, categoryId, moduleId, value, direction, movedKeys) {\n      if (direction === 'left') {\n        return; // 只处理从右向左移除（删除）的情况\n      } // 获取被移除的标签ID\n\n\n      const removedTagIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\n\n      if (removedTagIds.length === 0) {\n        return;\n      }\n\n      try {\n        const loading = this.$loading({\n          lock: true,\n          text: '正在删除关联关系...'\n        }); // 收集需要删除的关系ID\n\n        const toDeleteRefIds = [];\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (cityCategoryRefId) {\n          const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n          if (moduleRefId) {\n            // 获取当前模块的标签关系\n            const existingTags = await getTagsByModuleRefId(moduleRefId);\n\n            for (const tagId of removedTagIds) {\n              // 在现有关系中查找对应的refId\n              const tag = existingTags.find(item => item.tagId === tagId);\n\n              if (tag && tag.refId) {\n                toDeleteRefIds.push(tag.refId); // 从模块-标签列表中移除\n\n                const key = `${cityCode}-${categoryId}-${moduleId}`;\n                const index = this.moduleTags[key].indexOf(tagId);\n\n                if (index !== -1) {\n                  this.moduleTags[key].splice(index, 1);\n                }\n              }\n            }\n          }\n        } // 批量删除关系\n\n\n        if (toDeleteRefIds.length > 0) {\n          await batchDeleteModuleTagRef(toDeleteRefIds);\n        } // 同步到selectedTags\n\n\n        this.syncAllAttributesAndTags();\n        loading.close();\n        this.$message.success(`已删除${toDeleteRefIds.length}个商品标签的关联关系`);\n      } catch (error) {\n        this.$message.error(error.message || '删除关联关系失败');\n      }\n    },\n\n    // 处理地市下的模块分类变化\n    handleCityCategoriesChange(cityCode, value) {\n      // 更新对应地市的模块分类\n      this.$set(this.cityCategories, cityCode, value); // 展开刚刚选择的地市\n\n      if (value.length > 0 && !this.expandedCities.includes(cityCode)) {\n        this.expandedCities.push(cityCode);\n      }\n    },\n\n    // 处理地市和分类下的模块变化\n    handleCityModulesChange(cityCode, categoryId, value) {\n      const key = `${cityCode}-${categoryId}`; // 更新对应地市和分类的模块\n\n      this.$set(this.cityModules, key, value); // 展开刚刚选择的地市-分类\n\n      if (value.length > 0 && !this.expandedCityCategories.includes(key)) {\n        this.expandedCityCategories.push(key);\n      }\n    },\n\n    // 处理模块下的商品属性类别变化\n    handleModuleAttributesChange(cityCode, categoryId, moduleId, value) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`; // 更新对应模块的商品属性类别\n\n      this.$set(this.moduleAttributes, key, value); // 展开刚刚选择的地市-分类-模块\n\n      if (value.length > 0 && !this.expandedCityModules.includes(key)) {\n        this.expandedCityModules.push(key);\n      }\n    },\n\n    // 处理模块下的商品标签变化\n    handleModuleTagsChange(cityCode, categoryId, moduleId, value) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`; // 更新对应模块的商品标签\n\n      this.$set(this.moduleTags, key, value);\n      console.log(this.expandedCityModules, '=this.expandedCityModules'); // 展开刚刚选择的地市-分类-模块\n\n      if (value.length > 0 && !this.expandedCityModules.includes(key)) {\n        this.expandedCityModules.push(key);\n      }\n    },\n\n    // 同步所有模块分类到selectedCategories\n    syncAllCategories() {\n      // 重置selectedCategories\n      this.selectedCategories = []; // 收集所有地市下的模块分类\n\n      for (const cityCode in this.cityCategories) {\n        const categories = this.cityCategories[cityCode] || []; // 将未添加的分类添加到总分类列表中\n\n        for (const categoryId of categories) {\n          if (!this.selectedCategories.includes(categoryId)) {\n            this.selectedCategories.push(categoryId);\n          }\n        }\n      }\n    },\n\n    // 同步所有模块到selectedModules\n    syncAllModules() {\n      // 重置selectedModules\n      this.selectedModules = []; // 收集所有地市-分类下的模块\n\n      for (const key in this.cityModules) {\n        const modules = this.cityModules[key] || []; // 将未添加的模块添加到总模块列表中\n\n        for (const moduleId of modules) {\n          if (!this.selectedModules.includes(moduleId)) {\n            this.selectedModules.push(moduleId);\n          }\n        }\n      }\n    },\n\n    // 同步所有属性类别和标签\n    syncAllAttributesAndTags() {\n      // 清空选中的属性和标签\n      this.selectedAttributeTypes = [];\n      this.selectedTags = []; // 收集所有组合中已选的属性和标签，避免重复\n\n      const attributeSet = new Set();\n      const tagSet = new Set(); // 遍历所有模块组合\n\n      for (const cityCode of this.selectedCities) {\n        for (const categoryId of this.cityCategories[cityCode] || []) {\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\n            const moduleKey = `${cityCode}-${categoryId}-${moduleId}`; // 添加该组合下的属性\n\n            for (const attributeTypeId of this.moduleAttributes[moduleKey] || []) {\n              attributeSet.add(attributeTypeId);\n            } // 添加该组合下的标签\n\n\n            for (const tagId of this.moduleTags[moduleKey] || []) {\n              tagSet.add(tagId);\n            }\n          }\n        }\n      } // 转换为数组\n\n\n      this.selectedAttributeTypes = Array.from(attributeSet);\n      this.selectedTags = Array.from(tagSet);\n      console.log('同步后的全局属性数量:', this.selectedAttributeTypes.length);\n      console.log('同步后的全局标签数量:', this.selectedTags.length);\n    },\n\n    // 获取地市下选中的模块分类数量\n    getCityCategoryCount(cityCode) {\n      return (this.cityCategories[cityCode] || []).length;\n    },\n\n    // 获取地市和分类下选中的模块数量\n    getCityCategoryModuleCount(cityCode, categoryId) {\n      const key = `${cityCode}-${categoryId}`;\n      return (this.cityModules[key] || []).length;\n    },\n\n    // 获取模块下选中的商品属性类别数量\n    getModuleAttributeCount(cityCode, categoryId, moduleId) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      return (this.moduleAttributes[key] || []).length;\n    },\n\n    // 获取模块下选中的商品标签数量\n    getModuleTagCount(cityCode, categoryId, moduleId) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      return (this.moduleTags[key] || []).length;\n    },\n\n    // 获取地市下的模块分类数组\n    getCityModulesArray(cityCode, categoryId) {\n      const key = `${cityCode}-${categoryId}`;\n      return this.cityModules[key] || [];\n    },\n\n    // 获取模块的标签页值\n    getModuleTabsValue(cityCode, categoryId, moduleId) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      return this.attributeTagTabs[key] || 'attribute';\n    },\n\n    // 获取模块的商品属性类别数组\n    getModuleAttributesArray(cityCode, categoryId, moduleId) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      return this.moduleAttributes[key] || [];\n    },\n\n    // 获取模块的商品标签数组\n    getModuleTagsArray(cityCode, categoryId, moduleId) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      return this.moduleTags[key] || [];\n    },\n\n    // 批量更新某个地市的模块分类选择\n    updateCityCategories(cityCode, selectedCategories) {\n      // 更新数据\n      this.$set(this.cityCategories, cityCode, selectedCategories); // 同步到全局的selectedCategories\n\n      this.syncAllCategories();\n    },\n\n    // 更新地市+分类的模块\n    updateCityModules(cityCode, categoryId, value) {\n      const key = `${cityCode}-${categoryId}`;\n      this.$set(this.cityModules, key, value);\n      this.handleCityModulesChange(cityCode, categoryId, value);\n    },\n\n    // 更新模块的标签页\n    updateModuleTab(cityCode, categoryId, moduleId, value) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      this.$set(this.attributeTagTabs, key, value);\n    },\n\n    // 更新模块的商品属性类别\n    updateModuleAttributes(cityCode, categoryId, moduleId, value) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\n      this.$set(this.moduleAttributes, key, value);\n      this.handleModuleAttributesChange(cityCode, categoryId, moduleId, value);\n    },\n\n    // 更新模块的商品标签\n    async updateModuleTags(cityCode, categoryId, moduleId, value) {\n      const key = `${cityCode}-${categoryId}-${moduleId}`; // 使用Vue.set确保响应式更新\n\n      this.$set(this.moduleTags, key, [...value]); // this.$set(this.moduleTags, key, value);\n      // 触发后续处理\n\n      this.handleModuleTagsChange(cityCode, categoryId, moduleId, value);\n    },\n\n    // 保存属性类别排序\n    async saveAttributeSort(cityCode, categoryId, moduleId, sortedAttributeIds) {\n      try {\n        // 获取模块关联ID\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (!cityCategoryRefId) {\n          console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID`);\n          return;\n        }\n\n        const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n        if (!moduleRefId) {\n          console.warn(`未找到模块${moduleId}的关联ID`);\n          return;\n        } // 获取当前的属性关联关系\n\n\n        const attributeTypes = await getAttributeTypesByModuleRefId(moduleRefId); // 构建排序更新数据\n\n        const updateList = [];\n        sortedAttributeIds.forEach((attributeTypeId, index) => {\n          const existingRef = attributeTypes.find(attr => attr.attributeTypeId === attributeTypeId);\n\n          if (existingRef) {\n            updateList.push({\n              id: existingRef.refId,\n              sort: index + 1 // 排序从1开始\n\n            });\n          }\n        });\n\n        if (updateList.length > 0) {\n          await batchUpdateRankSx(updateList);\n          console.log(`属性排序保存成功，更新了${updateList.length}条记录`);\n        }\n      } catch (error) {\n        console.error('保存属性排序失败:', error);\n        this.$message.error('保存属性排序失败');\n      }\n    },\n\n    // 保存标签排序\n    async saveTagSort(cityCode, categoryId, moduleId, sortedTagIds) {\n      try {\n        // 获取模块关联ID\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (!cityCategoryRefId) {\n          console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID`);\n          return;\n        }\n\n        const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n        if (!moduleRefId) {\n          console.warn(`未找到模块${moduleId}的关联ID`);\n          return;\n        } // 获取当前的标签关联关系\n\n\n        const tags = await getTagsByModuleRefId(moduleRefId); // 构建排序更新数据\n\n        const updateList = [];\n        sortedTagIds.forEach((tagId, index) => {\n          const existingRef = tags.find(tag => tag.tagId === tagId);\n\n          if (existingRef) {\n            updateList.push({\n              id: existingRef.refId,\n              sort: index + 1 // 排序从1开始\n\n            });\n          }\n        });\n\n        if (updateList.length > 0) {\n          await batchUpdateRankSx(updateList);\n          console.log(`标签排序保存成功，更新了${updateList.length}条记录`);\n        }\n      } catch (error) {\n        console.error('保存标签排序失败:', error);\n        this.$message.error('保存标签排序失败');\n      }\n    }\n\n  }\n};", "map": {"version": 3, "mappings": ";;;AAgRA,SACAA,YADA,EAEAC,aAFA,EAGAC,UAHA,EAIAC,iBAJA,EAKAC,OALA,EAMAC,uBANA,EAOAC,6BAPA,EAQAC,mBARA,EASAC,wBATA,EAUAC,yBAVA,EAWAC,8BAXA,EAYAC,0BAZA,EAaAC,+BAbA,EAcAC,gBAdA,EAeAC,qBAfA,EAgBAC,8BAhBA,EAiBAC,oBAjBA,EAkBAC,qBAlBA,EAmBAC,2BAnBA,EAoBAC,4BApBA,EAqBAC,kBArBA,EAsBAC,0BAtBA,EAuBAC,gCAvBA,EAwBAC,iCAxBA,EAyBAC,uBAzBA,EA0BAC,eA1BA,EA2BAC,iBA3BA,QA4BA,8BA5BA;AA6BA;AACA;AACA;EACAC,sBADA;;EAEAC;IACA;MACA;MACAC,cAFA;MAIA;MACAC,UALA;MAKA;MACAC,kBANA;MAMA;MACAC,sBAPA;MAOA;MACAC,sBARA;MAQA;MAEA;MACAC,cAXA;MAWA;MACAC,gBAZA;MAYA;MACAC,sBAbA;MAaA;MAEA;MACAC,WAhBA;MAgBA;MACAC,cAjBA;MAiBA;MACAC,mBAlBA;MAkBA;MAEA;MACAC,4BArBA;MAqBA;MACAC,kBAtBA;MAsBA;MACAC,qBAvBA;MAuBA;MACAC,0BAxBA;MAwBA;MACAC,QAzBA;MAyBA;MACAC,WA1BA;MA0BA;MACAC,gBA3BA;MA2BA;MAEA;MACAC,sBA9BA;MA8BA;MACAC,4BA/BA;MA+BA;MAEA;MACAC,oBAlCA;MAkCA;MACAC,kBAnCA;MAmCA;MACAC,yBApCA;MAoCA;MACAC,eArCA;MAqCA;MAEA;MACAC,kBAxCA;MAwCA;MACAC,eAzCA;MAyCA;MACAC,oBA1CA;MA0CA;MACAC,oBA3CA;MA2CA;MACAC,cA5CA;MA4CA;MACAC,kBA7CA;MA6CA;MACAC,0BA9CA;MA8CA;MACAC,uBA/CA;MA+CA;MACAC,mBAhDA;MAgDA;MACAC,iBAjDA;MAiDA;MACAC,kBAlDA;MAkDA;MACAC;IAnDA;EAqDA,CAxDA;;EAyDAC;IACAC;MACA;QACA;UACA;QACA;MACA;;MACA;IACA,CARA;;IAUAC;MACA;QACA;UACA;QACA;MACA;;MACA;IACA;;EAjBA,CAzDA;;EA4EAC;IACA;EACA,CA9EA;;EA+EAC;IACA;IACA;MACA;IACA;EACA,CApFA;;EAqFAC;IACA;IACA;IACAC;MACAC;MAEA;MACAC,0CAJA,CAMA;;MACAA,uCAPA,CASA;;MACAA;IACA,CAdA;;IAgBA;IACA;MACAA;MACAD;MAEA;MAEA;MACA;MAEA;MACA;MACAA;MACAA;MACAA;;MAEA;QACA;QACA;QACAE;QACAA,2DAJA,CAMA;;QACA,2EAPA,CASA;;QACA;MACA,CA1BA,CA4BA;;;MACA;MACAC;MAEA;IACA,CAlDA;;IAmDAC;MACAJ;MACA;MACAC;MACAA,iEAJA,CAMA;;MACAA;IACA,CA3DA;;IA4DA;IACA;MACAA;MAEA;MAEA,oDALA,CAMA;;MACA;MACAD;MAEA;MACA;;MAGA;QACA;QACA,iCAFA,CAGA;;QACAK,gCAJA,CAKA;;QACAA;QACAL,kCAPA,CASA;;QACA,qEAVA,CAYA;;QACA;MACA,CA5BA,CA8BA;;;MACA;MACAG;MAEA;IACA,CAhGA;;IAmGA;IACA;MACA;QACA;UAAAG;UAAAC;QAAA;QACA;UACAC,SADA,CACA;;QADA;QAIAR;;QAEA;UACA;UACA;YACAS,YADA;YAEAC,0BAFA;YAGAC;UAHA;QAKA,CAPA,MAOA;UACA;UACA;YACAF,YADA;YAEAC,0BAFA;YAGAC;UAHA;QAKA;;QAEAC;MACA,CAzBA,CAyBA;QACAZ;QACA;MACA;IACA,CAlIA;;IAmIA;IACA;MACA;QACA;UAAAM;UAAAC;QAAA,GADA,CAEA;;QACA,mCAHA,CAKA;;QACA;QACAP;;QACA;UACA;UACA;YACAS,YADA;YAEAC,0BAFA;YAGAC;UAHA;QAKA,CAPA,MAOA;UACA;UACA;YACAF,YADA;YAEAC,0BAFA;YAGAC;UAHA;QAKA,CAtBA,CAwBA;;;QACA;;QACA;UACA;UACA;YACAF,YADA;YAEAC,sBAFA;YAGAC;UAHA;QAKA,CAPA,MAOA;UACA;UACA;YACAF,YADA;YAEAC,sBAFA;YAGAC;UAHA;QAKA,CAxCA,CA0CA;;;QACA;;QACA;UACA;UACA;YACAF,YADA;YAEAC,6BAFA;YAGAC;UAHA;QAKA,CAPA,MAOA;UACA;UACA;YACAF,YADA;YAEAC,6BAFA;YAGAC;UAHA;QAKA,CA1DA,CA4DA;;;QACA;;QACA;UACA;UACA;YACAF,YADA;YAEAC,mBAFA;YAGAC;UAHA;QAKA,CAPA,MAOA;UACA;UACA;YACAF,YADA;YAEAC,mBAFA;YAGAC;UAHA;QAKA;;QAEAC;MACA,CA/EA,CA+EA;QACA;MACA;IACA,CAvNA;;IAyNA;IACAC;MACA;MACA;IACA,CA7NA;;IA+NA;IACAC;MACA;MACA;MACA;IACA,CApOA;;IAsOA;IACAC;MACA;MACA;IACA,CA1OA;;IA4OA;IACAC;MACA;MACA;IACA,CAhPA;;IAkPA;IACAC;MACA;MACA;IACA,CAtPA;;IAwPA;IACAC;MACA;QACA;MACA;IACA,CA7PA;;IA+PA;IACA;MACA;QACA;QACA;UACA;UACA;YAAAZ;YAAAC;UAAA;;UACA;YACA;YACA;YACA,yBAHA,CAGA;YAEA;;YACA,mCANA,CAQA;;YACA;cACA;gBACA;cACA;YACA,CAbA,CAeA;;;YACA;UACA,CAjBA,CAiBA;YACAP;YACA;UACA,CApBA,SAoBA;YACAY;UACA;QACA,CA1BA,MA0BA;UACA;UACA;YAAAN;YAAAC;UAAA;;UACA;YACA;YACA;YACA,sBAHA,CAGA;YAEA;;YACA,iCANA,CAQA;;YACA;cACA;gBACA;;gBACA;kBACA;gBACA;cACA;YACA,CAhBA,CAkBA;;;YACA;UACA,CApBA,CAoBA;YACAP;YACA;UACA,CAvBA,SAuBA;YACAY;UACA;QACA,CA7BA,MA6BA;UACA;UACA;YAAAN;YAAAC;UAAA;;UACA;YACA;YACA;YACA;YACA,2BAJA,CAMA;;YACA,0CAPA,CASA;;YACA;cACA;gBACA;;gBAEA;kBACA;;kBAEA,kGACA,uCADA,EACA;oBACA;kBACA;gBACA;cACA;YACA,CAvBA,CAyBA;;;YACA;UACA,CA3BA,CA2BA;YACAP;YACA;UACA,CA9BA,SA8BA;YACAY;UACA;QACA;;QAEA;MACA;IACA,CAlWA;;IAoWA;IACA;MACA;QACA;UAAAN;UAAAC;QAAA,GADA,CAGA;;QACA;QAEAK;QACA,gCAPA,CASA;;QACA,iBAVA,CAYA;;QACAO;QACAC;MACA,CAfA,CAeA;QACApB;QACA;MACA;IACA,CAzXA;;IA2XA;IACA;MACA;QACA;UAAAM;UAAAC;QAAA,GADA,CAGA;;QACA,4BAJA,CAMA;;QACA,uCAPA,CASA;;QACA,6CAVA,CAYA;;QACA,6CAbA,CAeA;;QACA;QAEAK;QACA,gCAnBA,CAqBA;;QACA;MACA,CAvBA,CAuBA;QACA;MACA;IACA,CAvZA;;IAyZA;IACA;MACA;MACA;QACA,qEADA,CAGA;;QACA;UACA;UACA,yCAFA,CAIA;;UACA;YACA,0EADA,CAGA;;YACA;cACA;cACA;YACA;UACA;QACA;MACA,CAnBA;IAoBA,CAhbA;;IAkbA;IACA;MACA;QACA;UAAAN;UAAAC;QAAA,GADA,CAGA;;QACA,mCAJA,CAIA;;QACA,qCALA,CAKA;;QACA,wCANA,CAMA;;QACA,kCAPA,CAOA;QAEA;;QACA,oCAVA,CAUA;;QACA,8BAXA,CAWA;;QACA,oCAZA,CAYA;QAEA;;QACAP;;QACA;UACA;YACA;;YAEA;cACA,yDADA,CAGA;;cACA;gBACAqB;cACA;;cACAA,+CAPA,CASA;;cACA;;cACA;gBACAC;cACA;;cACAC;gBACAD;cACA,CAFA,EAdA,CAkBA;;cACA;;cACA;gBACAE;cACA;;cACAlD;gBACAkD;cACA,CAFA;cAIAxB;YACA;UACA;QACA;;QAEAA,6BApDA,CAqDA;;QACA;UACA;UACAyB,6CAFA,CAIA;;UACA;YACA;cACA;cACAC,8CAFA,CAIA;;cACA;gBACA;kBACA,6CACAnF,+CADA,EAEAC,qCAFA;kBAIAmF;kBACAC;gBACA;cACA,CATA;YAUA;UACA,CAjBA;QAkBA,CAvBA,GAtDA,CA+EA;;QACA,kCAhFA,CAgFA;;QACA,gCAjFA,CAiFA;;QACA,8BAlFA,CAkFA;;QACA,6BAnFA,CAmFA;QAEA;;QACA;UACA;YACA;YACA,0FAFA,CAIA;;YACA;cACAC,uCADA,CAGA;;cACA;cACA;YACA;UACA;QACA,CApGA,CAsGA;;;QACA;UACA;YACA;YACA;YACA,yBAHA,CAKA;;YACA;cACA;gBACA;;gBACA;kBACAC;kBACAC;kBACA;gBACA;cACA;;cACA;YACA,CAhBA,CAkBA;;;YACA;cACA,gGADA,CAGA;;cACA;gBACAC,2CADA,CAGA;;gBACA;kBACA;oBACA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA,CA5IA,CA8IA;;;QACA,yCA/IA,CA+IA;;QACA;UACA;YACA;YACA;;YAEA;cACA;cACA;cAEAC;YACA;UACA;QACA,CA5JA,CA8JA;;;QACA;UACA;UACA;;UAEA;YACA;YACA,0EAFA,CAIA;;YACA;cACA;cACA;gBACAC;gBACAlC;cACA;YACA;UACA;QACA,CAhLA,CAkLA;;;QACA;UACA;UACA;;UAEA;YACA;YACA,8DAFA,CAIA;;YACA;cACA;cACA;gBACAmC;gBACAnC;cACA;YACA;UACA;QACA;;QAEAA;UACA,4CADA;UAEA,4CAFA;UAGA,wCAHA;UAIA;QAJA,GAtMA,CA6MA;;QACA;;QAEA;UACAoC,uEACAC,IADA,CACA,sEADA;QAEA;;QAEA;UACAD,2EACAC,IADA,CACA,oEADA;QAEA;;QAEA;UACAD,0EACAC,IADA,CACA,oEADA;QAEA;;QAEA;UACAD,+DACAC,IADA,CACA,iEADA;QAEA;;QAEA;QACAzB;MAEA,CAvOA,CAuOA;QACAZ;QACA;MACA;IACA,CA/pBA;;IAiqBA;IACA;MACA;MACA,4BAFA,CAIA;;MACA;QACA;QACA,uDAFA,CAIA;;QACA;UACA,wCADA,CAGA;;UACA;YACAsC;cACAC,kBADA;cAEAC,sBAFA;cAGAhC,SAHA;cAGA;cACAiC,OAJA,CAIA;;YAJA;UAMA;QACA;MACA,CAvBA,CAyBA;;;MACA;QACAzC;QACA;QACAA,sCAHA,CAKA;;QACA;MACA;IACA,CApsBA;;IAssBA;IACA;MACA;MACA;QACA;QACA,6DAFA,CAIA;;QACA;UACA;;UACA;YACA;UACA;QACA;MACA,CAXA;MAaA;IACA,CAvtBA;;IAytBA;IACA;MACA;MACA;MACA,kCAHA,CAGA;MAEA;;MACA;QACA;UACA;UACA;;UAEA;YACAA;YACA;UACA,CAPA,CASA;;;UACA0C,qCAVA,CAYA;;UACA,wDAbA,CAeA;;UACA;YACA,mCADA,CAGA;;YACA;cACAC;gBACAC,wBADA;gBAEAC,kBAFA;gBAGArC,SAHA;gBAGA;gBACAsC,YAJA,CAIA;gBACA;;cALA;YAOA;UACA;QACA;MACA,CAtCA,CAwCA;;;MACA;QACA9C;QACA;QACAA,sCAHA,CAKA;;QACA;MACA;IACA,CA3wBA;;IA6wBA;IACA;MACA;MACA,kCAFA,CAEA;;MAEA;QACA;UACA;;UAEA;YACA+C,2BADA,CACA;;YAEAC;cACA,8DADA,CAGA;;cACA;gBACA;;gBACA;kBACA;gBACA;cACA;YACA,CAVA;UAWA;QACA;MACA;;MAEA;IACA,CAzyBA;;IA2yBA;IACA;MACAhD,sDADA,CAGA;;MACA,mCAJA,CAMA;;MACA,yBAPA,CASA;;MACA;MAEA;MACA;MACA;MACA,qBAfA,CAiBA;;MACA;QACA;UACA;UACA;;UAEA;YACAA;YACA;UACA,CAPA,CASA;;;UACA;YACA;YACA;;YAEA;cACAA;cACA;YACA,CAPA,CASA;;;YACA;cACAiD;YACA,CAFA;YAIA;cACAC;YACA,CAFA;YAIAC;YACAC;UACA;QACA;MACA,CAnDA,CAqDA;;;MACA,kEAtDA,CAwDA;;MACA;QACA;UACA;UACA;;UAEA;YACApD;YACA;UACA,CAPA,CASA;;;UACA;YACA;YACA;;YAEA;cACAA;cACA;YACA,CAPA,CASA;;;YACA0C,yBAVA,CAYA;;YACA;YACA;YAEA1C,gGAhBA,CAkBA;;YACA;YACA,0FApBA,CAsBA;;YACA;YACA;YAEAA,0FA1BA,CA4BA;;YACA;cACA;gBACAqD;kBACAC,oCADA;kBAEAC,qCAFA;kBAGA/C,SAHA;kBAGA;kBACAiC,OAJA,CAIA;;gBAJA;cAMA;YACA,CAtCA,CAwCA;;;YACA;cACA;gBACAe;kBACAC,iBADA;kBAEAH,oCAFA;kBAGA9C,SAHA;kBAGA;kBACAiC,OAJA,CAIA;;gBAJA;cAMA;YACA;UACA;QACA;MACA,CAzHA,CA2HA;;;MACA;;MAEA;QACAzC;QACA0D,kBACAtH,yDACAiG,IADA,CACAsB,iDADA,CADA;MAIA;;MAEA;QACA3D;QACA0D,kBACApH,qCACA+F,IADA,CACAsB,+CADA,CADA;MAIA;;MAEA;MACA3D;IACA,CA57BA;;IA87BA;IACA4D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAv9BA;;IAy9BA;IACA;MACA,2BADA,CACA;MAEA;;MACA;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA;;MAEA;QACA;QACA,mGAFA,CAIA;;QACA;UACA;UACA;UACA5D,kFAHA,CAKA;;UACA;YACA;YACA,yCAFA,CAIA;;YACA;cACA;YACA,CAPA,CASA;;;YACA;YACA;cACA6D,gBADA;cAEAtB,kBAFA;cAGAuB,kBAHA;cAIAtB,0BAJA;cAKAuB;YALA;UAOA;QACA;MACA,CA/BA,CA+BA;QACA/D;QACA;MACA,CA9CA,CAgDA;;;MACA;QACAA;MACA;IACA,CA9gCA;;IAghCA;IACA;MACA,yBADA,CACA;MAEA;;MACA;QACA;UACA;;UACA;YACA;UACA,CAFA,MAEA;YACA;UACA;QACA;MACA;;MAEA;QACA;QACA;QACA,0BAHA,CAGA;;QAEA;UACA;YACA;;YACA;cACAgE;cACAC;gBACA1B,QADA;gBAEAC,UAFA;gBAGAsB,oCAHA;gBAIAC,8CAJA;gBAKAjC;cALA;YAOA;UACA;QACA;;QAEA;UACA;UACA,oGAFA,CAIA;;UACA;YACA;YACA;YAEA9B,mIAJA,CAMA;;YACA;cACA;cACA,qDAFA,CAIA;;cACA;gBACA;cACA,CAPA,CASA;;;cACA;cACA;gBACA6D,sBADA;gBAEAtB,uBAFA;gBAGAuB,uBAHA;gBAIAtB,2BAJA;gBAKAuB,+BALA;gBAMAlB,4BANA;gBAOAqB;cAPA;YASA;UACA;QACA;MACA,CAxDA,CAwDA;QACAlE;QACA;MACA,CA1EA,CA4EA;;;MACA;QACA;UACA;UACAA;QACA;MACA;IACA,CApmCA;;IAsmCA;IACA;MACA,gCADA,CACA;;MACA,sBAFA,CAEA;MAEA;;MACA;QACA;UACA;;UAEA;YACA,oDADA,CAGA;;YACA;cACA;YACA,CAFA,MAEA;cACA;YACA;;YAEA;cACA;YACA,CAFA,MAEA;cACA;YACA;;YAEA;cACA;YACA;UACA;QACA;MACA,CA9BA,CAgCA;;;MACA;;MAEA;QACA;UACA;UACA;;UAEA;YACAA;YACA;UACA;;UAEA;YACA;YACA;YACA;;YAEA;cACAmE;gBACA;gBACA;gBACAnE;;gBAEA;kBACA;kBACA;oBACA;kBACA,CAJA,CAMA;;;kBACA;kBACA;oBACA6D,qBADA;oBAEAhB,kBAFA;oBAGAqB,sBAHA;oBAIAE,yCAJA;oBAKAC,6CALA;oBAMAC;kBANA;gBAQA,CArBA,CAuBA;;;gBACA;gBACAtE;;gBAEA;kBACA;kBACA;oBACA;kBACA,CAJA,CAMA;;;kBACA;kBACA;oBACA6D,gBADA;oBAEAhB,kBAFA;oBAGAqB,sBAHA;oBAIAK,gBAJA;oBAKAC,oBALA;oBAMAF;kBANA;gBAQA;cACA,CA5CA;YA6CA;UACA;QACA;MACA;;MAEA,gCArGA,CAuGA;;MACA;QACA;UACA;;UAEA;YACA;YACAtE;UACA;QACA;MACA;IACA,CAztCA;;IA2tCA;IACA;MACA;QACA,OADA,CACA;MACA,CAHA,CAKA;;;MACA;;MAEA;QACA;MACA;;MAEA;QACA;UAAAM;UAAAC;QAAA,GADA,CAGA;;QACA;;QAEA;UACA;UACA;UACA;;UAEA;YACAkE,2BADA,CAGA;;YACA,oCAJA,CAMA;;YACA;;YACA;cACA;YACA;UACA;QACA,CAvBA,CAyBA;;;QACA;UACA;QACA,CA5BA,CA8BA;;;QACA;QAEA7D;QACA;MACA,CAnCA,CAmCA;QACA;MACA;IACA,CA9wCA;;IAgxCA;IACA;MACA;QACA,OADA,CACA;MACA,CAHA,CAKA;;;MACA;;MAEA;QACA;MACA;;MAEA;QACA;UAAAN;UAAAC;QAAA,GADA,CAGA;;QACA;QAEA;;QACA;UACA;YACA;YACA;;YAEA;cACAkE,2BADA,CAGA;;cACA,0CAJA,CAMA;;cACA;cACA;;cACA;gBACA;cACA;YACA;UACA;QACA,CA1BA,CA4BA;;;QACA;UACA;QACA,CA/BA,CAiCA;;;QACA;QAEA7D;QACA;MACA,CAtCA,CAsCA;QACA;MACA;IACA,CAt0CA;;IAw0CA;IACA;MACA;QACA,OADA,CACA;MACA,CAHA,CAKA;;;MACA;;MAEA;QACA;MACA;;MAEA;QACA;UAAAN;UAAAC;QAAA,GADA,CAGA;;QACA;QAEA;;QACA;UACA;;UACA;YACA;YACA;;YAEA;cACA;cACA;;cACA;gBACAkE,oCADA,CAGA;;gBACA;gBACA;;gBACA;kBACA;gBACA;cACA;YACA;UACA;QACA,CA5BA,CA8BA;;;QACA;UACA;QACA,CAjCA,CAmCA;;;QACA;QAEA7D;QACA;MACA,CAxCA,CAwCA;QACA;MACA;IACA,CAh4CA;;IAk4CA;IACA;MACA;QACA,OADA,CACA;MACA,CAHA,CAKA;;;MACA;;MAEA;QACA;MACA;;MAEA;QACA;UAAAN;UAAAC;QAAA,GADA,CAGA;;QACA;QAEA;;QACA;UACA;;UACA;YACA;YACA;;YAEA;cACA;cACA;;cACA;gBACAkE,+BADA,CAGA;;gBACA;gBACA;;gBACA;kBACA;gBACA;cACA;YACA;UACA;QACA,CA5BA,CA8BA;;;QACA;UACA;QACA,CAjCA,CAmCA;;;QACA;QAEA7D;QACA;MACA,CAxCA,CAwCA;QACA;MACA;IACA,CA17CA;;IA47CA;IACA8D;MACA;MACA,gDAFA,CAIA;;MACA;QACA;MACA;IACA,CAr8CA;;IAu8CA;IACAC;MACA,wCADA,CAGA;;MACA,wCAJA,CAMA;;MACA;QACA;MACA;IACA,CAl9CA;;IAo9CA;IACAC;MACA,oDADA,CAGA;;MACA,6CAJA,CAMA;;MACA;QACA;MACA;IACA,CA/9CA;;IAi+CA;IACAC;MACA,oDADA,CAGA;;MACA;MACA7E,mEALA,CAOA;;MACA;QACA;MACA;IAEA,CA9+CA;;IAg/CA;IACA8E;MACA;MACA,6BAFA,CAIA;;MACA;QACA,uDADA,CAGA;;QACA;UACA;YACA;UACA;QACA;MACA;IACA,CAhgDA;;IAkgDA;IACAC;MACA;MACA,0BAFA,CAIA;;MACA;QACA,4CADA,CAGA;;QACA;UACA;YACA;UACA;QACA;MACA;IACA,CAlhDA;;IAohDA;IACAC;MACA;MACA;MACA,uBAHA,CAKA;;MACA;MACA,yBAPA,CASA;;MACA;QACA;UACA;;UAEA;YACA,0DADA,CAGA;;YACA;cACAC;YACA,CANA,CAQA;;;YACA;cACAC;YACA;UACA;QACA;MACA,CA5BA,CA8BA;;;MACA;MACA;MAEAlF;MACAA;IACA,CAzjDA;;IA2jDA;IACAmF;MACA;IACA,CA9jDA;;IAgkDA;IACAC;MACA;MACA;IACA,CApkDA;;IAskDA;IACAC;MACA;MACA;IACA,CA1kDA;;IA4kDA;IACAC;MACA;MACA;IACA,CAhlDA;;IAklDA;IACAC;MACA;MACA;IACA,CAtlDA;;IAwlDA;IACAC;MACA;MACA;IACA,CA5lDA;;IA8lDA;IACAC;MACA;MACA;IACA,CAlmDA;;IAomDA;IACAC;MACA;MACA;IACA,CAxmDA;;IA0mDA;IACAC;MACA;MACA,6DAFA,CAIA;;MACA;IACA,CAjnDA;;IAmnDA;IACAC;MACA;MACA;MACA;IACA,CAxnDA;;IA0nDA;IACAC;MACA;MACA;IACA,CA9nDA;;IAgoDA;IACAC;MACA;MACA;MACA;IACA,CAroDA;;IAuoDA;IACA;MACA,oDADA,CAEA;;MACA,4CAHA,CAIA;MAIA;;MACA;IACA,CAlpDA;;IAopDA;IACA;MACA;QACA;QACA;;QACA;UACA9F;UACA;QACA;;QAEA;;QACA;UACAA;UACA;QACA,CAZA,CAcA;;;QACA,yEAfA,CAiBA;;QACA;QACA+F;UACA;;UACA;YACAC;cACAC,qBADA;cAEAxD,eAFA,CAEA;;YAFA;UAIA;QACA,CARA;;QAUA;UACA;UACAzC;QACA;MACA,CAjCA,CAiCA;QACAA;QACA;MACA;IACA,CA3rDA;;IA6rDA;IACA;MACA;QACA;QACA;;QACA;UACAA;UACA;QACA;;QAEA;;QACA;UACAA;UACA;QACA,CAZA,CAcA;;;QACA,qDAfA,CAiBA;;QACA;QACAkG;UACA;;UACA;YACAF;cACAC,qBADA;cAEAxD,eAFA,CAEA;;YAFA;UAIA;QACA,CARA;;QAUA;UACA;UACAzC;QACA;MACA,CAjCA,CAiCA;QACAA;QACA;MACA;IACA;;EApuDA;AArFA", "names": ["getAllCities", "getCategories", "getModules", "getAttributeTypes", "getTags", "getCategoriesByCityCode", "getModulesByCityCategoryRefId", "saveCityCategoryRef", "batchSaveCityCategoryRef", "saveCityCategoryModuleRef", "batchSaveCityCategoryModuleRef", "saveModuleAttributeTypeRef", "batchSaveModuleAttributeTypeRef", "saveModuleTagRef", "batchSaveModuleTagRef", "getAttributeTypesByModuleRefId", "getTagsByModuleRefId", "deleteCityCategoryRef", "deleteCityCategoryModuleRef", "deleteModuleAttributeTypeRef", "deleteModuleTagRef", "batchDeleteCityCategoryRef", "batchDeleteCityCategoryModuleRef", "batchDeleteModuleAttributeTypeRef", "batchDeleteModuleTagRef", "batchUpdateRank", "batchUpdateRankSx", "name", "data", "currentStep", "cities", "selectedCities", "selectAllCities", "isIndeterminate", "categories", "categoryData", "selectedCategories", "modules", "moduleData", "selectedModules", "attributeTagTab", "attributeTypes", "attributeTypeData", "selectedAttributeTypes", "tags", "tagData", "selectedTags", "cityCategoryRefMap", "cityCategoryModuleRefMap", "categoryRefIdMap", "moduleRefIdMap", "attributeTypeRefIdMap", "tagRefIdMap", "cityCategories", "cityModules", "attributeTagTabs", "moduleAttributes", "moduleTags", "expandedCities", "expandedCityCategories", "expandedCityModules", "sortableRight", "draggedItem", "draggedItem2", "transfer<PERSON><PERSON>", "computed", "hasCategorySelected", "hasModuleSelected", "created", "mounted", "methods", "dragStart", "console", "event", "newAttributes", "draggingElements", "dragStart2", "newTags", "lock", "text", "status", "key", "label", "disabled", "loading", "handleCheckAllCitiesChange", "handleCitiesChange", "getCityName", "getCategoryName", "getModuleName", "prevStep", "configRelationSaveOptimizer", "configRelationDataLoader", "moduleToGroupsMap", "groupAttributeMap", "selectedAttributes", "groupTagMap", "cityRelationsMap", "moduleRelationsMap", "attributeRelationsMap", "tagRelationsMap", "toDeleteCategoryRefIds", "cityCategoryKey", "found", "toDeleteModuleRefIds", "moduleRefToGroupKeyMap", "toDeleteAttrRefIds", "toDeleteTagRefIds", "deletePromises", "then", "cityCategoryRefs", "cityCode", "categoryId", "sort", "processedGroups", "cityCategoryModuleRefs", "cityCategoryRefId", "moduleId", "isOneBeat", "processedRefIds", "promises", "attributeTypeRefMap", "tagRefMap", "allAttributesPromises", "allTagsPromises", "moduleAttributeTypeRefs", "cityCategoryModuleRefId", "goodsAttributeTypeId", "moduleTagRefs", "goodsTagId", "savePromises", "result", "resetForm", "refId", "cityName", "categoryName", "cityCategoryRefIds", "refIdToInfoMap", "moduleName", "loadPromises", "attributeTypeId", "attributeTypeName", "groupKey", "tagId", "tagName", "toDeleteRefIds", "handleCityCategoriesChange", "handleCityModulesChange", "handleModuleAttributesChange", "handleModuleTagsChange", "syncAllCategories", "syncAllModules", "syncAllAttributesAndTags", "attributeSet", "tagSet", "getCityCategoryCount", "getCityCategoryModuleCount", "getModuleAttributeCount", "getModuleTagCount", "getCityModulesArray", "getModuleTabsValue", "getModuleAttributesArray", "getModuleTagsArray", "updateCityCategories", "updateCityModules", "updateModuleTab", "updateModuleAttributes", "sortedAttributeIds", "updateList", "id", "sortedTagIds"], "sourceRoot": "src/views/hnzsxH5/configRelation", "sources": ["index.vue"], "sourcesContent": ["<!-- H5即时受理-关联配置管理 -->\r\n<template>\r\n  <div class=\"ele-body\">\r\n    <el-card shadow=\"never\">\r\n      <div class=\"config-header\">\r\n        <h2>H5即时受理-关联配置管理</h2>\r\n        <div class=\"config-desc\">\r\n          <p>在此页面可以配置地市、模块分类、模块、商品属性类别、商品标签之间的关联关系</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 操作区域 -->\r\n      <div class=\"config-operation\">\r\n        <el-steps :active=\"currentStep\" finish-status=\"success\" simple>\r\n          <el-step title=\"选择地市\" icon=\"el-icon-map-location\"></el-step>\r\n          <el-step title=\"配置模块分类\" icon=\"el-icon-s-grid\"></el-step>\r\n          <el-step title=\"配置模块\" icon=\"el-icon-s-platform\"></el-step>\r\n          <el-step title=\"配置商品属性和标签\" icon=\"el-icon-s-goods\"></el-step>\r\n        </el-steps>\r\n      </div>\r\n\r\n      <!-- 步骤内容区域 -->\r\n      <div class=\"config-content\">\r\n        <!-- 步骤1: 选择地市 -->\r\n        <div v-show=\"currentStep === 1\" class=\"step-content\">\r\n          <div class=\"step-title\">第一步: 选择需要配置的地市</div>\r\n          <div class=\"city-selection\">\r\n            <div class=\"city-select-header\">\r\n              <el-checkbox v-model=\"selectAllCities\" :indeterminate=\"isIndeterminate\"\r\n                @change=\"handleCheckAllCitiesChange\">全选</el-checkbox>\r\n            </div>\r\n            <div class=\"city-checkbox-group\">\r\n              <el-checkbox-group v-model=\"selectedCities\" @change=\"handleCitiesChange\">\r\n                <el-checkbox v-for=\"city in cities\" :key=\"city.cityCode\" :label=\"city.cityCode\">\r\n                  {{ city.cityName }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n            </div>\r\n          </div>\r\n          <div class=\"step-actions\">\r\n            <el-button type=\"primary\" @click=\"nextStep\" :disabled=\"selectedCities.length === 0\">下一步</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 步骤2: 配置模块分类 -->\r\n        <div v-show=\"currentStep === 2\" class=\"step-content\">\r\n          <div class=\"step-title\">第二步: 为选中的地市配置模块分类</div>\r\n\r\n          <!-- 地市分组显示 -->\r\n          <div class=\"city-groups\">\r\n            <el-collapse v-model=\"expandedCities\">\r\n              <el-collapse-item v-for=\"cityCode in selectedCities\" :key=\"cityCode\" :name=\"cityCode\">\r\n                <template slot=\"title\">\r\n                  <div class=\"group-header\">\r\n                    <span class=\"group-title\">{{ getCityName(cityCode) }}</span>\r\n                    <el-tag size=\"mini\" type=\"info\" class=\"count-tag\">\r\n                      已选{{ getCityCategoryCount(cityCode) }}项\r\n                    </el-tag>\r\n                  </div>\r\n                </template>\r\n\r\n                <!-- 每个地市的模块分类配置 -->\r\n                <div class=\"category-selection\">\r\n                  <div class=\"category-transfer\">\r\n                    <el-transfer :value=\"cityCategories[cityCode] ? cityCategories[cityCode] : []\"\r\n                      @input=\"val => updateCityCategories(cityCode, val)\" :data=\"categoryData\"\r\n                      :titles=\"['可选模块分类', '已选模块分类']\" :button-texts=\"['移除', '添加']\" :format=\"{\r\n                        noChecked: '${total}',\r\n                        hasChecked: '${checked}/${total}'\r\n                      }\" @remove=\"handleRemoveCategories(cityCode, $event, 'left', $event)\">\r\n                      <span slot-scope=\"{ option }\" class=\"transfer-item\">\r\n                        {{ option.label }}\r\n                      </span>\r\n                    </el-transfer>\r\n                  </div>\r\n                </div>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n          </div>\r\n\r\n          <div class=\"step-actions\">\r\n            <el-button @click=\"prevStep\">上一步</el-button>\r\n            <el-button type=\"primary\" @click=\"syncAllCategories(); nextStep()\"\r\n              :disabled=\"!hasCategorySelected\">下一步</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 步骤3: 配置模块 -->\r\n        <div v-show=\"currentStep === 3\" class=\"step-content\">\r\n          <div class=\"step-title\">第三步: 为选中的地市和模块分类配置模块</div>\r\n\r\n          <!-- 地市+分类分组显示 -->\r\n          <div class=\"city-category-groups\">\r\n            <el-collapse v-model=\"expandedCityCategories\">\r\n              <template v-for=\"cityCode in selectedCities\">\r\n                <template v-if=\"(cityCategories[cityCode] || []).length > 0\">\r\n                  <el-collapse-item v-for=\"categoryId in cityCategories[cityCode]\" :key=\"`${cityCode}-${categoryId}`\"\r\n                    :name=\"`${cityCode}-${categoryId}`\">\r\n                    <template slot=\"title\">\r\n                      <div class=\"group-header\">\r\n                        <span class=\"group-title\">{{ getCityName(cityCode) }} - {{ getCategoryName(categoryId) }}</span>\r\n                        <el-tag size=\"mini\" type=\"info\" class=\"count-tag\">\r\n                          已选{{ getCityCategoryModuleCount(cityCode, categoryId) }}项\r\n                        </el-tag>\r\n                      </div>\r\n                    </template>\r\n\r\n                    <!-- 每个地市+分类的模块配置 -->\r\n                    <div class=\"module-selection\">\r\n                      <div class=\"module-transfer\">\r\n                        <el-transfer :value=\"getCityModulesArray(cityCode, categoryId)\"\r\n                          @input=\"val => updateCityModules(cityCode, categoryId, val)\" :data=\"moduleData\"\r\n                          :titles=\"['可选模块', '已选模块']\" :button-texts=\"['移除', '添加']\" :format=\"{\r\n                            noChecked: '${total}',\r\n                            hasChecked: '${checked}/${total}'\r\n                          }\" @remove=\"handleRemoveModules(cityCode, categoryId, $event, 'left', $event)\">\r\n                          <span slot-scope=\"{ option }\" class=\"transfer-item\">\r\n                            <span>{{ option.label }}</span>\r\n                          </span>\r\n                        </el-transfer>\r\n                      </div>\r\n                    </div>\r\n                  </el-collapse-item>\r\n                </template>\r\n              </template>\r\n            </el-collapse>\r\n          </div>\r\n\r\n          <div class=\"step-actions\">\r\n            <el-button @click=\"prevStep\">上一步</el-button>\r\n            <el-button type=\"primary\" @click=\"syncAllModules(); nextStep()\"\r\n              :disabled=\"!hasModuleSelected\">下一步</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 步骤4: 配置商品属性和标签 -->\r\n        <div v-show=\"currentStep === 4\" class=\"step-content\">\r\n          <div class=\"step-title\">第四步: 为选中的模块配置商品属性类别和标签</div>\r\n\r\n          <!-- 地市+分类+模块分组显示 -->\r\n          <div class=\"city-category-module-groups\">\r\n            <el-collapse v-model=\"expandedCityModules\">\r\n              <template v-for=\"cityCode in selectedCities\">\r\n                <template v-if=\"(cityCategories[cityCode] || []).length > 0\">\r\n                  <template v-for=\"categoryId in cityCategories[cityCode]\">\r\n                    <template v-if=\"getCityModulesArray(cityCode, categoryId).length > 0\">\r\n                      <el-collapse-item v-for=\"moduleId in getCityModulesArray(cityCode, categoryId)\"\r\n                        :key=\"`${cityCode}-${categoryId}-${moduleId}`\" :name=\"`${cityCode}-${categoryId}-${moduleId}`\">\r\n                        <template slot=\"title\">\r\n                          <div class=\"group-header\">\r\n                            <span class=\"group-title\">{{ getCityName(cityCode) }} - {{ getCategoryName(categoryId) }} -\r\n                              {{ getModuleName(moduleId) }}</span>\r\n                            <div>\r\n                              <el-tag size=\"mini\" type=\"success\" class=\"count-tag\">\r\n                                属性{{ getModuleAttributeCount(cityCode, categoryId, moduleId) }}项\r\n                              </el-tag>\r\n                              <el-tag size=\"mini\" type=\"warning\" class=\"count-tag\">\r\n                                标签{{ getModuleTagCount(cityCode, categoryId, moduleId) }}项\r\n                              </el-tag>\r\n                            </div>\r\n                          </div>\r\n                        </template>\r\n\r\n                        <!-- 每个模块的属性类别和标签配置 -->\r\n                        <div class=\"attribute-tag-selection\">\r\n                          <el-tabs :value=\"getModuleTabsValue(cityCode, categoryId, moduleId)\"\r\n                            @input=\"val => updateModuleTab(cityCode, categoryId, moduleId, val)\" type=\"border-card\"\r\n                            class=\"attribute-tag-tabs\">\r\n                            <el-tab-pane label=\"商品属性类别配置\" name=\"attribute\">\r\n                              <div class=\"drag-sort-tip\">\r\n                                <i class=\"el-icon-sort\"></i>\r\n                                <span>在右侧已选择区域，您可以通过拖拽 <i class=\"el-icon-rank\"></i> 图标调整属性类别的显示顺序</span>\r\n                              </div>\r\n                              <div class=\"attribute-transfer enhanced-transfer\">\r\n                                <el-transfer ref=\"sortTransfer\" target-order=\"unshift\"\r\n                                  :value=\"getModuleAttributesArray(cityCode, categoryId, moduleId)\"\r\n                                  @input=\"val => updateModuleAttributes(cityCode, categoryId, moduleId, val)\"\r\n                                  :data=\"attributeTypeData\" :titles=\"['可选商品属性类别', '已选商品属性类别']\"\r\n                                  :button-texts=\"['移除', '添加']\" :format=\"{\r\n                                    noChecked: '${total}',\r\n                                    hasChecked: '${checked}/${total}'\r\n                                  }\"\r\n                                  @remove=\"handleRemoveAttributeTypes(cityCode, categoryId, moduleId, $event, 'left', $event)\">\r\n\r\n                                  <div slot-scope=\"{ option }\"\r\n                                    class=\"enhanced-transfer-item\"\r\n                                    :class=\"{\r\n                                      'is-selected': getModuleAttributesArray(cityCode, categoryId, moduleId).includes(option.key),\r\n                                      'is-dragging': draggedItem && draggedItem.key === option.key,\r\n                                      'drag-over': dragOverItem && dragOverItem.key === option.key\r\n                                    }\"\r\n                                    draggable=\"true\"\r\n                                    @dragstart=\"dragStart($event, option)\"\r\n                                    @dragover=\"dragOver($event, option)\"\r\n                                    @dragenter=\"dragEnter($event, option)\"\r\n                                    @dragleave=\"dragLeave($event, option)\"\r\n                                    @drop=\"drop($event, option, cityCode, categoryId, moduleId)\"\r\n                                    @dragend=\"dragEnd($event)\">\r\n\r\n                                    <div class=\"item-content\">\r\n                                      <div class=\"drag-handle-area\" v-if=\"getModuleAttributesArray(cityCode, categoryId, moduleId).includes(option.key)\">\r\n                                        <i class=\"el-icon-rank drag-handle\"></i>\r\n                                      </div>\r\n\r\n                                      <div class=\"item-label\">{{ option.label }}</div>\r\n\r\n                                      <div class=\"sort-input-area\" v-if=\"getModuleAttributesArray(cityCode, categoryId, moduleId).includes(option.key)\">\r\n                                        <el-input-number\r\n                                          v-model=\"attributeSortInputs[`${cityCode}-${categoryId}-${moduleId}-${option.key}`]\"\r\n                                          :min=\"1\"\r\n                                          :max=\"getModuleAttributesArray(cityCode, categoryId, moduleId).length\"\r\n                                          size=\"mini\"\r\n                                          controls-position=\"right\"\r\n                                          @change=\"handleSortInputChange('attribute', cityCode, categoryId, moduleId, option.key, $event)\"\r\n                                          class=\"sort-input\">\r\n                                        </el-input-number>\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n\r\n                                </el-transfer>\r\n                              </div>\r\n                            </el-tab-pane>\r\n                            <el-tab-pane label=\"商品标签配置\" name=\"tag\">\r\n                              <div class=\"drag-sort-tip\">\r\n                                <i class=\"el-icon-sort\"></i>\r\n                                <span>在右侧已选择区域，您可以通过拖拽 <i class=\"el-icon-rank\"></i> 图标调整标签的显示顺序</span>\r\n                              </div>\r\n                              <div class=\"tag-transfer\">\r\n                                <el-transfer\r\n                                  :value=\"getModuleTagsArray(cityCode, categoryId, moduleId)\" target-order=\"unshift\"\r\n                                  @input=\"val => updateModuleTags(cityCode, categoryId, moduleId, val)\" :data=\"tagData\"\r\n                                  :titles=\"['可选商品标签', '已选商品标签']\" :button-texts=\"['移除', '添加']\" :format=\"{\r\n                                    noChecked: '${total}',\r\n                                    hasChecked: '${checked}/${total}'\r\n                                  }\"\r\n                                  @remove=\"handleRemoveTags(cityCode, categoryId, moduleId, $event, 'left', $event)\">\r\n                                  <span slot-scope=\"{ option }\"\r\n                                    class=\"transfer-item draggable-item\"\r\n                                    draggable=\"!option.disabled\"\r\n                                    @dragstart=\"dragStart2($event, option)\"\r\n                                    @dragover.prevent\r\n                                    @dragenter.prevent\r\n                                    @drop=\"drop2($event, option, cityCode, categoryId, moduleId)\"\r\n                                    :title=\"'拖拽可调整排序：' + option.label\">\r\n                                    <i v-if=\"getModuleTagsArray(cityCode, categoryId, moduleId).includes(option.key)\" class=\"el-icon-rank drag-handle\"></i>\r\n                                    {{ option.label }}\r\n                                  </span>\r\n                                </el-transfer>\r\n                              </div>\r\n                            </el-tab-pane>\r\n                          </el-tabs>\r\n                        </div>\r\n                      </el-collapse-item>\r\n                    </template>\r\n                  </template>\r\n                </template>\r\n              </template>\r\n            </el-collapse>\r\n          </div>\r\n\r\n          <div class=\"step-actions\">\r\n            <el-button @click=\"prevStep\">上一步</el-button>\r\n            <el-button type=\"primary\" @click=\"syncAllAttributesAndTags(); saveConfiguration()\">保存配置</el-button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getAllCities,\r\n  getCategories,\r\n  getModules,\r\n  getAttributeTypes,\r\n  getTags,\r\n  getCategoriesByCityCode,\r\n  getModulesByCityCategoryRefId,\r\n  saveCityCategoryRef,\r\n  batchSaveCityCategoryRef,\r\n  saveCityCategoryModuleRef,\r\n  batchSaveCityCategoryModuleRef,\r\n  saveModuleAttributeTypeRef,\r\n  batchSaveModuleAttributeTypeRef,\r\n  saveModuleTagRef,\r\n  batchSaveModuleTagRef,\r\n  getAttributeTypesByModuleRefId,\r\n  getTagsByModuleRefId,\r\n  deleteCityCategoryRef,\r\n  deleteCityCategoryModuleRef,\r\n  deleteModuleAttributeTypeRef,\r\n  deleteModuleTagRef,\r\n  batchDeleteCityCategoryRef,\r\n  batchDeleteCityCategoryModuleRef,\r\n  batchDeleteModuleAttributeTypeRef,\r\n  batchDeleteModuleTagRef,\r\n  batchUpdateRank,\r\n  batchUpdateRankSx\r\n} from '@/api/hnzsxH5/configRelation';\r\nimport { configRelationDataLoader, configRelationSaveOptimizer } from '@/utils/configRelationOptimizer';\r\nimport Sortable from 'sortablejs'\r\nexport default {\r\n  name: 'ConfigRelation',\r\n  data() {\r\n    return {\r\n      // 当前步骤\r\n      currentStep: 1,\r\n\r\n      // 地市相关数据\r\n      cities: [], // 所有地市\r\n      selectedCities: [], // 选中的地市\r\n      selectAllCities: false, // 是否全选地市\r\n      isIndeterminate: false, // 是否半选状态\r\n\r\n      // 模块分类相关数据\r\n      categories: [], // 所有模块分类\r\n      categoryData: [], // 穿梭框数据\r\n      selectedCategories: [], // 选中的模块分类\r\n\r\n      // 模块相关数据\r\n      modules: [], // 所有模块\r\n      moduleData: [], // 穿梭框数据\r\n      selectedModules: [], // 选中的模块\r\n\r\n      // 商品属性和标签相关数据\r\n      attributeTagTab: 'attribute', // 当前选中的选项卡\r\n      attributeTypes: [], // 所有商品属性类别\r\n      attributeTypeData: [], // 穿梭框数据\r\n      selectedAttributeTypes: [], // 选中的商品属性类别\r\n      tags: [], // 所有商品标签\r\n      tagData: [], // 穿梭框数据\r\n      selectedTags: [], // 选中的商品标签\r\n\r\n      // 保存中间数据的映射关系\r\n      cityCategoryRefMap: {}, // 地市-模块分类关系映射\r\n      cityCategoryModuleRefMap: {}, // 地市分类-模块关系映射\r\n\r\n      // 用于存储ID与关联关系的映射\r\n      categoryRefIdMap: {}, // 分类ID到关联ID的映射\r\n      moduleRefIdMap: {}, // 模块ID到关联ID的映射 \r\n      attributeTypeRefIdMap: {}, // 属性类型ID到关联ID的映射\r\n      tagRefIdMap: {}, // 标签ID到关联ID的映射\r\n\r\n      // 新增的变量\r\n      cityCategories: {}, // 存储每个地市的模块分类\r\n      cityModules: {}, // 存储每个地市和分类的模块\r\n      attributeTagTabs: {}, // 存储每个模块的属性标签选项卡\r\n      moduleAttributes: {}, // 存储每个模块的商品属性类别\r\n      moduleTags: {}, // 存储每个模块的商品标签\r\n      expandedCities: [], // 存储展开的地市\r\n      expandedCityCategories: [], // 存储展开的地市和分类\r\n      expandedCityModules: [], // 存储展开的地市和模块\r\n      sortableRight: null, // 用于保存sortable实例\r\n      draggedItem: null,  // 当前拖拽项\r\n      draggedItem2: null,  // 当前拖拽项\r\n      transferKey: 0\r\n    };\r\n  },\r\n  computed: {\r\n    hasCategorySelected() {\r\n      for (const cityCode in this.cityCategories) {\r\n        if ((this.cityCategories[cityCode] || []).length > 0) {\r\n          return true;\r\n        }\r\n      }\r\n      return false;\r\n    },\r\n\r\n    hasModuleSelected() {\r\n      for (const key in this.cityModules) {\r\n        if ((this.cityModules[key] || []).length > 0) {\r\n          return true;\r\n        }\r\n      }\r\n      return false;\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchInitialData();\r\n  },\r\n  mounted() {\r\n    // 确保页面加载后能看到分类数据\r\n    if (this.categories.length === 0 || this.categoryData.length === 0) {\r\n      this.fetchCategoryData();\r\n    }\r\n  },\r\n  methods: {\r\n    // 排序\r\n    // 开始拖拽\r\n    dragStart(event, option) {\r\n      console.log(event, '================', option);\r\n\r\n      this.draggedItem = option;\r\n      event.dataTransfer.effectAllowed = 'move';\r\n\r\n      // 添加拖拽样式\r\n      event.target.classList.add('dragging');\r\n\r\n      // 设置拖拽数据\r\n      event.dataTransfer.setData('text/plain', JSON.stringify(option));\r\n    },\r\n\r\n    // 放置 - 属性类别\r\n    async drop(event, targetOption, cityCode, categoryId, moduleId) {\r\n      event.preventDefault();\r\n      console.log(targetOption, '==targetOption');\r\n\r\n      if (!this.draggedItem || this.draggedItem.key === targetOption.key) return;\r\n\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      const currentAttributes = this.moduleAttributes[key] || [];\r\n\r\n      const currentIndex = currentAttributes.indexOf(this.draggedItem.key);\r\n      const targetIndex = currentAttributes.indexOf(targetOption.key);\r\n      console.log(targetIndex, 'targetIndex');\r\n      console.log(currentIndex, 'currentIndex');\r\n      console.log(currentAttributes, '==currentAttributes');\r\n\r\n      if (currentIndex > -1 && targetIndex > -1) {\r\n        // 重新排序数组\r\n        const newAttributes = [...currentAttributes];\r\n        newAttributes.splice(currentIndex, 1);\r\n        newAttributes.splice(targetIndex, 0, this.draggedItem.key);\r\n\r\n        // 更新属性数组\r\n        this.updateModuleAttributes(cityCode, categoryId, moduleId, newAttributes);\r\n\r\n        // 保存排序到后台\r\n        await this.saveAttributeSort(cityCode, categoryId, moduleId, newAttributes);\r\n      }\r\n\r\n      // 移除拖拽样式\r\n      const draggingElements = document.querySelectorAll('.dragging');\r\n      draggingElements.forEach(el => el.classList.remove('dragging'));\r\n\r\n      this.draggedItem = null;\r\n    },\r\n    dragStart2(event, option) {\r\n      console.log(event, '================', option);\r\n      this.draggedItem2 = option;\r\n      event.dataTransfer.effectAllowed = 'move';\r\n      event.dataTransfer.setData('text/plain', JSON.stringify(option));\r\n\r\n      // 添加拖拽样式\r\n      event.target.classList.add('dragging');\r\n    },\r\n    // 放置 - 标签\r\n    async drop2(event, targetOption, cityCode, categoryId, moduleId) {\r\n      event.preventDefault();\r\n\r\n      if (!this.draggedItem2 || this.draggedItem2.key === targetOption.key) return;\r\n\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      // 创建当前数据的深拷贝\r\n      const currentTags = JSON.parse(JSON.stringify(this.moduleTags[key] || []));\r\n      console.log(currentTags,'=currentTags');\r\n\r\n      const currentIndex = currentTags.indexOf(this.draggedItem2.key);\r\n      const targetIndex = currentTags.indexOf(targetOption.key);\r\n\r\n\r\n      if (currentIndex > -1 && targetIndex > -1) {\r\n        // 创建新数组\r\n        const newTags = [...currentTags];\r\n        // 移除拖拽项\r\n        newTags.splice(currentIndex, 1);\r\n        // 插入到目标位置\r\n        newTags.splice(targetIndex, 0, this.draggedItem2.key);\r\n        console.log(newTags,'==newTags');\r\n\r\n        // 更新数据\r\n        await this.updateModuleTags(cityCode, categoryId, moduleId, newTags);\r\n\r\n        // 保存排序到后台\r\n        await this.saveTagSort(cityCode, categoryId, moduleId, newTags);\r\n      }\r\n\r\n      // 移除拖拽样式\r\n      const draggingElements = document.querySelectorAll('.dragging');\r\n      draggingElements.forEach(el => el.classList.remove('dragging'));\r\n\r\n      this.draggedItem2 = null;\r\n    },\r\n\r\n    \r\n    // 获取分类数据\r\n    async fetchCategoryData() {\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '加载分类数据中...' });\r\n        const categoriesRes = await getCategories({\r\n          status: 1 // 有效状态\r\n        });\r\n\r\n        console.log('单独获取分类数据结果:', categoriesRes);\r\n\r\n        if (categoriesRes && categoriesRes.rows) {\r\n          this.categories = categoriesRes.rows;\r\n          this.categoryData = this.categories.map(item => ({\r\n            key: item.id,\r\n            label: item.moduleTypeName,\r\n            disabled: false\r\n          }));\r\n        } else if (Array.isArray(categoriesRes)) {\r\n          this.categories = categoriesRes;\r\n          this.categoryData = this.categories.map(item => ({\r\n            key: item.id,\r\n            label: item.moduleTypeName,\r\n            disabled: false\r\n          }));\r\n        }\r\n\r\n        loading.close();\r\n      } catch (error) {\r\n        console.error('获取分类数据失败:', error);\r\n        this.$message.error('加载分类数据失败');\r\n      }\r\n    },\r\n    // 获取初始数据\r\n    async fetchInitialData() {\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '加载数据中...' });\r\n        // 获取地市数据\r\n        this.cities = await getAllCities();\r\n\r\n        // 获取模块分类数据\r\n        const categoriesRes = await getCategories();\r\n        console.log('分类数据结果:', categoriesRes);\r\n        if (categoriesRes && categoriesRes.rows) {\r\n          this.categories = categoriesRes.rows;\r\n          this.categoryData = this.categories.map(item => ({\r\n            key: item.id,\r\n            label: item.moduleTypeName,\r\n            disabled: false\r\n          }));\r\n        } else if (Array.isArray(categoriesRes)) {\r\n          this.categories = categoriesRes;\r\n          this.categoryData = this.categories.map(item => ({\r\n            key: item.id,\r\n            label: item.moduleTypeName,\r\n            disabled: false\r\n          }));\r\n        }\r\n\r\n        // 获取模块数据\r\n        const modulesRes = await getModules();\r\n        if (modulesRes && modulesRes.rows) {\r\n          this.modules = modulesRes.rows;\r\n          this.moduleData = modulesRes.rows.map(item => ({\r\n            key: item.id,\r\n            label: item.moduleName,\r\n            disabled: false\r\n          }));\r\n        } else if (Array.isArray(modulesRes)) {\r\n          this.modules = modulesRes;\r\n          this.moduleData = modulesRes.map(item => ({\r\n            key: item.id,\r\n            label: item.moduleName,\r\n            disabled: false\r\n          }));\r\n        }\r\n\r\n        // 获取商品属性类别数据\r\n        const attributeTypesRes = await getAttributeTypes();\r\n        if (attributeTypesRes && attributeTypesRes.rows) {\r\n          this.attributeTypes = attributeTypesRes.rows;\r\n          this.attributeTypeData = attributeTypesRes.rows.map(item => ({\r\n            key: item.id,\r\n            label: item.attributeTypeName,\r\n            disabled: false\r\n          }));\r\n        } else if (Array.isArray(attributeTypesRes)) {\r\n          this.attributeTypes = attributeTypesRes;\r\n          this.attributeTypeData = attributeTypesRes.map(item => ({\r\n            key: item.id,\r\n            label: item.attributeTypeName,\r\n            disabled: false\r\n          }));\r\n        }\r\n\r\n        // 获取商品标签数据\r\n        const tagsRes = await getTags();\r\n        if (tagsRes && tagsRes.rows) {\r\n          this.tags = tagsRes.rows;\r\n          this.tagData = tagsRes.rows.map(item => ({\r\n            key: item.id,\r\n            label: item.tagName,\r\n            disabled: false\r\n          }));\r\n        } else if (Array.isArray(tagsRes)) {\r\n          this.tags = tagsRes;\r\n          this.tagData = tagsRes.map(item => ({\r\n            key: item.id,\r\n            label: item.tagName,\r\n            disabled: false\r\n          }));\r\n        }\r\n\r\n        loading.close();\r\n      } catch (error) {\r\n        this.$message.error(error.message || '数据加载失败');\r\n      }\r\n    },\r\n\r\n    // 全选地市\r\n    handleCheckAllCitiesChange(checked) {\r\n      this.selectedCities = checked ? this.cities.map(item => item.cityCode) : [];\r\n      this.isIndeterminate = false;\r\n    },\r\n\r\n    // 地市选择变化\r\n    handleCitiesChange(value) {\r\n      const checkedCount = value.length;\r\n      this.selectAllCities = checkedCount === this.cities.length;\r\n      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;\r\n    },\r\n\r\n    // 获取地市名称\r\n    getCityName(cityCode) {\r\n      const city = this.cities.find(item => item.cityCode === cityCode);\r\n      return city ? city.cityName : cityCode;\r\n    },\r\n\r\n    // 获取模块分类名称\r\n    getCategoryName(categoryId) {\r\n      const category = this.categories.find(item => item.id === categoryId);\r\n      return category ? category.moduleTypeName : categoryId;\r\n    },\r\n\r\n    // 获取模块名称\r\n    getModuleName(moduleId) {\r\n      const module = this.modules.find(item => item.id === moduleId);\r\n      return module ? module.moduleName : moduleId;\r\n    },\r\n\r\n    // 上一步\r\n    prevStep() {\r\n      if (this.currentStep > 1) {\r\n        this.currentStep--;\r\n      }\r\n    },\r\n\r\n    // 下一步\r\n    async nextStep() {\r\n      if (this.currentStep < 4) {\r\n        // 根据当前步骤进行相应的预加载操作\r\n        if (this.currentStep === 1) {\r\n          // 从步骤1到步骤2：预加载已选地市的模块分类关系\r\n          const loading = this.$loading({ lock: true, text: '加载地市关联的模块分类中...' });\r\n          try {\r\n            // 清空之前的关联映射\r\n            this.cityCategoryRefMap = {};\r\n            this.cityCategories = {}; // 重置cityCategories\r\n\r\n            // 加载地市-模块分类关系\r\n            await this.loadCategoryRelations();\r\n\r\n            // 展开有分类的地市\r\n            for (const cityCode of this.selectedCities) {\r\n              if ((this.cityCategories[cityCode] || []).length > 0 && !this.expandedCities.includes(cityCode)) {\r\n                this.expandedCities.push(cityCode);\r\n              }\r\n            }\r\n\r\n            // 同步到selectedCategories\r\n            this.syncAllCategories();\r\n          } catch (error) {\r\n            console.error('预加载地市-分类关系失败:', error);\r\n            this.$message.error('加载关联数据失败，请重试');\r\n          } finally {\r\n            loading.close();\r\n          }\r\n        } else if (this.currentStep === 2) {\r\n          // 从步骤2到步骤3：预加载已选地市和分类的模块关系\r\n          const loading = this.$loading({ lock: true, text: '加载分类关联的模块中...' });\r\n          try {\r\n            // 清空之前的模块关联映射\r\n            this.cityCategoryModuleRefMap = {};\r\n            this.cityModules = {}; // 重置cityModules\r\n\r\n            // 加载地市分类-模块关系\r\n            await this.loadModuleRelations();\r\n\r\n            // 展开有模块的地市-分类\r\n            for (const cityCode of this.selectedCities) {\r\n              for (const categoryId of this.cityCategories[cityCode] || []) {\r\n                const key = `${cityCode}-${categoryId}`;\r\n                if ((this.cityModules[key] || []).length > 0 && !this.expandedCityCategories.includes(key)) {\r\n                  this.expandedCityCategories.push(key);\r\n                }\r\n              }\r\n            }\r\n\r\n            // 同步到selectedModules\r\n            this.syncAllModules();\r\n          } catch (error) {\r\n            console.error('预加载分类-模块关系失败:', error);\r\n            this.$message.error('加载关联数据失败，请重试');\r\n          } finally {\r\n            loading.close();\r\n          }\r\n        } else if (this.currentStep === 3) {\r\n          // 从步骤3到步骤4：预加载已选模块的商品属性和标签关系\r\n          const loading = this.$loading({ lock: true, text: '加载模块关联的属性和标签中...' });\r\n          try {\r\n            // 重置属性和标签相关数据\r\n            this.moduleAttributes = {};\r\n            this.moduleTags = {};\r\n            this.attributeTagTabs = {};\r\n\r\n            // 加载模块-商品属性类别和标签关系\r\n            await this.loadAttributeAndTagRelations();\r\n\r\n            // 展开有属性或标签的模块组合\r\n            for (const cityCode of this.selectedCities) {\r\n              for (const categoryId of this.cityCategories[cityCode] || []) {\r\n                const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n\r\n                for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n                  const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n\r\n                  if (((this.moduleAttributes[key] || []).length > 0 || (this.moduleTags[key] || []).length > 0) &&\r\n                    !this.expandedCityModules.includes(key)) {\r\n                    this.expandedCityModules.push(key);\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            // 同步到selectedAttributeTypes和selectedTags\r\n            this.syncAllAttributesAndTags();\r\n          } catch (error) {\r\n            console.error('预加载模块-属性标签关系失败:', error);\r\n            this.$message.error('加载关联数据失败，请重试');\r\n          } finally {\r\n            loading.close();\r\n          }\r\n        }\r\n\r\n        this.currentStep++;\r\n      }\r\n    },\r\n\r\n    // 优化后的保存配置\r\n    async saveConfiguration() {\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '保存配置中...' });\r\n\r\n        // 使用优化的保存流程\r\n        await configRelationSaveOptimizer.optimizedSaveConfiguration(this);\r\n\r\n        loading.close();\r\n        this.$message.success('配置保存成功');\r\n\r\n        // 重置表单\r\n        this.resetForm();\r\n\r\n        // 清除优化器缓存\r\n        configRelationSaveOptimizer.clearCache();\r\n        configRelationDataLoader.clearCache();\r\n      } catch (error) {\r\n        console.error('保存配置失败:', error);\r\n        this.$message.error(error.message || '配置保存失败');\r\n      }\r\n    },\r\n\r\n    // 保留原有的保存配置方法作为备用\r\n    async saveConfigurationLegacy() {\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '保存配置中...' });\r\n\r\n        // 1. 保存前准备，预加载现有关系数据减少后续查询\r\n        await this.prepareForSave();\r\n\r\n        // 2. 保存地市-模块分类关系\r\n        await this.saveCityCategoryRelations();\r\n\r\n        // 3. 保存地市分类-模块关系\r\n        await this.saveCityCategoryModuleRelations();\r\n\r\n        // 4. 保存模块-商品属性类别关系和模块-商品标签关系\r\n        await this.saveModuleAttributeTagRelations();\r\n\r\n        // 5. 删除不再需要的关联关系\r\n        await this.deleteUnusedRelations();\r\n\r\n        loading.close();\r\n        this.$message.success('配置保存成功');\r\n\r\n        // 重置表单\r\n        this.resetForm();\r\n      } catch (error) {\r\n        this.$message.error(error.message || '配置保存失败');\r\n      }\r\n    },\r\n\r\n    // 保存前准备，预加载现有关系数据减少后续查询\r\n    async prepareForSave() {\r\n      // 预先获取已有的地市-分类关联关系\r\n      await Promise.all(this.selectedCities.map(async cityCode => {\r\n        const existingCategoryRefs = await getCategoriesByCityCode(cityCode);\r\n\r\n        // 将已有关联关系存入映射中\r\n        for (const ref of existingCategoryRefs) {\r\n          const key = `${cityCode}_${ref.categoryId}`;\r\n          this.cityCategoryRefMap[key] = ref.refId;\r\n\r\n          // 预先加载分类对应的模块关系\r\n          if (this.selectedCategories.includes(ref.categoryId)) {\r\n            const existingModuleRefs = await getModulesByCityCategoryRefId(ref.refId);\r\n\r\n            // 将已有模块关联存入映射中\r\n            for (const moduleRef of existingModuleRefs) {\r\n              const key = `${ref.refId}_${moduleRef.moduleId}`;\r\n              this.cityCategoryModuleRefMap[key] = moduleRef.refId;\r\n            }\r\n          }\r\n        }\r\n      }));\r\n    },\r\n\r\n    // 删除不再需要的关联关系\r\n    async deleteUnusedRelations() {\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '正在优化关联关系...' });\r\n\r\n        // 预先获取所有关系数据，减少API调用次数\r\n        const cityRelationsMap = new Map(); // 存储地市-分类关系\r\n        const moduleRelationsMap = new Map(); // 存储地市分类-模块关系\r\n        const attributeRelationsMap = new Map(); // 存储模块-属性关系\r\n        const tagRelationsMap = new Map(); // 存储模块-标签关系\r\n\r\n        // 记录模块组合的属性和标签集合 (使用组合标识符确保每个分组都被正确处理)\r\n        const groupAttributeMap = new Map(); // 组合标识符 -> 属性ID集合\r\n        const groupTagMap = new Map(); // 组合标识符 -> 标签ID集合\r\n        const moduleToGroupsMap = new Map(); // 模块ID -> 组合标识符列表\r\n\r\n        // 构建组合->属性/标签映射，记录每个组合应该保留的属性和标签\r\n        console.log('开始构建组合->属性/标签映射...');\r\n        for (const cityCode of this.selectedCities) {\r\n          for (const categoryId of this.cityCategories[cityCode] || []) {\r\n            const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n\r\n            for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n              const groupKey = `${cityCode}-${categoryId}-${moduleId}`;\r\n\r\n              // 维护一个模块ID到所有包含它的组合的映射\r\n              if (!moduleToGroupsMap.has(moduleId)) {\r\n                moduleToGroupsMap.set(moduleId, []);\r\n              }\r\n              moduleToGroupsMap.get(moduleId).push(groupKey);\r\n\r\n              // 记录该组合选中的属性\r\n              const selectedAttributes = this.moduleAttributes[groupKey] || [];\r\n              if (!groupAttributeMap.has(groupKey)) {\r\n                groupAttributeMap.set(groupKey, new Set());\r\n              }\r\n              selectedAttributes.forEach(attrId => {\r\n                groupAttributeMap.get(groupKey).add(attrId);\r\n              });\r\n\r\n              // 记录该组合选中的标签\r\n              const selectedTags = this.moduleTags[groupKey] || [];\r\n              if (!groupTagMap.has(groupKey)) {\r\n                groupTagMap.set(groupKey, new Set());\r\n              }\r\n              selectedTags.forEach(tagId => {\r\n                groupTagMap.get(groupKey).add(tagId);\r\n              });\r\n\r\n              console.log(`组合 ${groupKey} 选中了 ${selectedAttributes.length} 个属性和 ${selectedTags.length} 个标签`);\r\n            }\r\n          }\r\n        }\r\n\r\n        console.log('开始获取现有关系数据...');\r\n        // 一次性获取所有地市的关系数据\r\n        await Promise.all(this.selectedCities.map(async cityCode => {\r\n          const categoryRefs = await getCategoriesByCityCode(cityCode);\r\n          cityRelationsMap.set(cityCode, categoryRefs);\r\n\r\n          // 同时获取每个地市-分类下的模块关系\r\n          await Promise.all(categoryRefs.map(async ref => {\r\n            if (ref.refId) {\r\n              const moduleRefs = await getModulesByCityCategoryRefId(ref.refId);\r\n              moduleRelationsMap.set(ref.refId, moduleRefs);\r\n\r\n              // 获取每个模块关系下的属性和标签关系\r\n              await Promise.all(moduleRefs.map(async moduleRef => {\r\n                if (moduleRef.refId) {\r\n                  const [attrTypes, tags] = await Promise.all([\r\n                    getAttributeTypesByModuleRefId(moduleRef.refId),\r\n                    getTagsByModuleRefId(moduleRef.refId)\r\n                  ]);\r\n                  attributeRelationsMap.set(moduleRef.refId, attrTypes);\r\n                  tagRelationsMap.set(moduleRef.refId, tags);\r\n                }\r\n              }));\r\n            }\r\n          }));\r\n        }));\r\n\r\n        // 处理数据，确定要删除的项\r\n        const toDeleteCategoryRefIds = []; // 地市-分类关系\r\n        const toDeleteModuleRefIds = []; // 地市分类-模块关系\r\n        const toDeleteAttrRefIds = []; // 模块-属性关系\r\n        const toDeleteTagRefIds = []; // 模块-标签关系\r\n\r\n        // 处理地市-分类关系\r\n        for (const [cityCode, categoryRefs] of cityRelationsMap.entries()) {\r\n          for (const ref of categoryRefs) {\r\n            // 检查该地市下是否还有此分类\r\n            const isCategorySelected = (this.cityCategories[cityCode] || []).includes(ref.categoryId);\r\n\r\n            // 如果分类不在该地市选中列表中，则标记该关系需要删除\r\n            if (!isCategorySelected) {\r\n              toDeleteCategoryRefIds.push(ref.refId);\r\n\r\n              // 从映射中移除\r\n              const key = `${cityCode}_${ref.categoryId}`;\r\n              delete this.cityCategoryRefMap[key];\r\n            }\r\n          }\r\n        }\r\n\r\n        // 处理地市分类-模块关系\r\n        for (const [cityCategoryRefId, moduleRefs] of moduleRelationsMap.entries()) {\r\n          for (const moduleRef of moduleRefs) {\r\n            // 找到对应的地市和分类\r\n            let found = false;\r\n            let cityCategoryKey = '';\r\n\r\n            // 寻找该cityCategoryRefId对应的cityCode和categoryId\r\n            for (const cityCode of this.selectedCities) {\r\n              for (const categoryId of this.cityCategories[cityCode] || []) {\r\n                const key = `${cityCode}_${categoryId}`;\r\n                if (this.cityCategoryRefMap[key] === cityCategoryRefId) {\r\n                  cityCategoryKey = `${cityCode}-${categoryId}`;\r\n                  found = true;\r\n                  break;\r\n                }\r\n              }\r\n              if (found) break;\r\n            }\r\n\r\n            // 如果找到对应的地市-分类组合，检查模块是否在该组合下选中\r\n            if (found) {\r\n              const isModuleSelected = (this.cityModules[cityCategoryKey] || []).includes(moduleRef.moduleId);\r\n\r\n              // 如果模块不在该组合的选中列表中，则标记该关系需要删除\r\n              if (!isModuleSelected) {\r\n                toDeleteModuleRefIds.push(moduleRef.refId);\r\n\r\n                // 从映射中移除\r\n                for (const key in this.cityCategoryModuleRefMap) {\r\n                  if (this.cityCategoryModuleRefMap[key] === moduleRef.refId) {\r\n                    delete this.cityCategoryModuleRefMap[key];\r\n                    break;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 获取城市代码-分类ID-模块ID的映射，用于后续定位模块所属的组\r\n        const moduleRefToGroupKeyMap = new Map(); // 模块关系ID -> 所属组key\r\n        for (const cityCode of this.selectedCities) {\r\n          for (const categoryId of this.cityCategories[cityCode] || []) {\r\n            const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n            if (!cityCategoryRefId) continue;\r\n\r\n            for (const moduleId of this.cityModules[`${cityCode}-${categoryId}`] || []) {\r\n              const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n              if (!moduleRefId) continue;\r\n\r\n              moduleRefToGroupKeyMap.set(moduleRefId, `${cityCode}-${categoryId}-${moduleId}`);\r\n            }\r\n          }\r\n        }\r\n\r\n        // 处理模块-属性关系\r\n        for (const [moduleRefId, attrTypes] of attributeRelationsMap.entries()) {\r\n          // 找到该模块关系对应的组合key\r\n          const groupKey = moduleRefToGroupKeyMap.get(moduleRefId);\r\n\r\n          if (groupKey) {\r\n            // 获取该组合中选中的属性ID集合\r\n            const selectedAttributeIds = groupAttributeMap.get(groupKey) || new Set();\r\n\r\n            // 处理当前模块的属性关系\r\n            for (const attrType of attrTypes) {\r\n              // 如果属性不在该组合选中列表中，则标记该关系需要删除\r\n              if (!selectedAttributeIds.has(attrType.attributeTypeId)) {\r\n                toDeleteAttrRefIds.push(attrType.refId);\r\n                console.log(`标记要删除的属性关系: 组合=${groupKey}, 属性ID=${attrType.attributeTypeId}, 关系ID=${attrType.refId}`);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 处理模块-标签关系\r\n        for (const [moduleRefId, tags] of tagRelationsMap.entries()) {\r\n          // 找到该模块关系对应的组合key\r\n          const groupKey = moduleRefToGroupKeyMap.get(moduleRefId);\r\n\r\n          if (groupKey) {\r\n            // 获取该组合中选中的标签ID集合\r\n            const selectedTagIds = groupTagMap.get(groupKey) || new Set();\r\n\r\n            // 处理当前模块的标签关系\r\n            for (const tag of tags) {\r\n              // 如果标签不在该组合选中列表中，则标记该关系需要删除\r\n              if (!selectedTagIds.has(tag.tagId)) {\r\n                toDeleteTagRefIds.push(tag.refId);\r\n                console.log(`标记要删除的标签关系: 组合=${groupKey}, 标签ID=${tag.tagId}, 关系ID=${tag.refId}`);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        console.log({\r\n          '要删除的地市-分类关系': toDeleteCategoryRefIds.length,\r\n          '要删除的地市分类-模块关系': toDeleteModuleRefIds.length,\r\n          '要删除的模块-属性关系': toDeleteAttrRefIds.length,\r\n          '要删除的模块-标签关系': toDeleteTagRefIds.length\r\n        });\r\n\r\n        // 批量删除关系，并行执行以提高速度\r\n        const deletePromises = [];\r\n\r\n        if (toDeleteCategoryRefIds.length > 0) {\r\n          deletePromises.push(batchDeleteCityCategoryRef(toDeleteCategoryRefIds)\r\n            .then(() => console.log(`批量删除地市-模块分类关系, 共${toDeleteCategoryRefIds.length}条`)));\r\n        }\r\n\r\n        if (toDeleteModuleRefIds.length > 0) {\r\n          deletePromises.push(batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds)\r\n            .then(() => console.log(`批量删除地市分类-模块关系, 共${toDeleteModuleRefIds.length}条`)));\r\n        }\r\n\r\n        if (toDeleteAttrRefIds.length > 0) {\r\n          deletePromises.push(batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds)\r\n            .then(() => console.log(`批量删除模块-商品属性类别关系, 共${toDeleteAttrRefIds.length}条`)));\r\n        }\r\n\r\n        if (toDeleteTagRefIds.length > 0) {\r\n          deletePromises.push(batchDeleteModuleTagRef(toDeleteTagRefIds)\r\n            .then(() => console.log(`批量删除模块-商品标签关系, 共${toDeleteTagRefIds.length}条`)));\r\n        }\r\n\r\n        await Promise.all(deletePromises);\r\n        loading.close();\r\n\r\n      } catch (error) {\r\n        console.error('删除不再需要的关联关系失败:', error);\r\n        throw new Error('删除不再需要的关联关系失败');\r\n      }\r\n    },\r\n\r\n    // 保存地市-模块分类关系\r\n    async saveCityCategoryRelations() {\r\n      // 构建需要保存的数据\r\n      const cityCategoryRefs = [];\r\n\r\n      // 使用选中的地市和每个地市下的分类，确保所有分组都被考虑\r\n      for (const cityCode of this.selectedCities) {\r\n        // 获取该地市下选中的所有分类\r\n        const categories = this.cityCategories[cityCode] || [];\r\n\r\n        // 只为尚未关联的分类创建新的关联关系\r\n        for (const categoryId of categories) {\r\n          const key = `${cityCode}_${categoryId}`;\r\n\r\n          // 如果此地市和分类的关联关系不存在，则创建新的\r\n          if (!this.cityCategoryRefMap[key]) {\r\n            cityCategoryRefs.push({\r\n              cityCode: cityCode,\r\n              categoryId: categoryId,\r\n              status: 1, // 有效状态\r\n              sort: 1 // 默认排序\r\n            });\r\n          }\r\n        }\r\n      }\r\n\r\n      // 批量保存新增的关联关系\r\n      if (cityCategoryRefs.length > 0) {\r\n        console.log(`准备保存${cityCategoryRefs.length}个地市-模块分类关系`);\r\n        const result = await batchSaveCityCategoryRef(cityCategoryRefs);\r\n        console.log('保存地市-模块分类关系结果:', result);\r\n\r\n        // 重新获取关联ID，确保映射表包含新创建的关联关系\r\n        await this.fetchCityCategoriesAfterSave();\r\n      }\r\n    },\r\n\r\n    // 保存地市-模块分类关系后重新获取关联ID\r\n    async fetchCityCategoriesAfterSave() {\r\n      // 只获取新添加的关系\r\n      const promises = this.selectedCities.map(async cityCode => {\r\n        // 查询当前地市下的所有关系\r\n        const categoryRefs = await getCategoriesByCityCode(cityCode);\r\n\r\n        // 更新映射表\r\n        for (const ref of categoryRefs) {\r\n          const key = `${cityCode}_${ref.categoryId}`;\r\n          if (!this.cityCategoryRefMap[key]) {\r\n            this.cityCategoryRefMap[key] = ref.refId;\r\n          }\r\n        }\r\n      });\r\n\r\n      await Promise.all(promises);\r\n    },\r\n\r\n    // 保存地市分类-模块关系\r\n    async saveCityCategoryModuleRelations() {\r\n      // 构建需要保存的数据\r\n      const cityCategoryModuleRefs = [];\r\n      const processedGroups = new Set(); // 记录已处理的地市-分类组合\r\n\r\n      // 遍历所有地市和分类组合\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n          const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n\r\n          if (!refId) {\r\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\r\n            continue;\r\n          }\r\n\r\n          // 标记该地市-分类组合为已处理\r\n          processedGroups.add(cityCategoryKey);\r\n\r\n          // 获取该地市-分类下选中的所有模块\r\n          const modules = this.cityModules[cityCategoryKey] || [];\r\n\r\n          // 只为尚未关联的模块创建新的关联关系\r\n          for (const moduleId of modules) {\r\n            const key = `${refId}_${moduleId}`;\r\n\r\n            // 如果此地市分类和模块的关联关系不存在，则创建新的\r\n            if (!this.cityCategoryModuleRefMap[key]) {\r\n              cityCategoryModuleRefs.push({\r\n                cityCategoryRefId: refId,\r\n                moduleId: moduleId,\r\n                status: 1, // 有效状态\r\n                isOneBeat: 1, // 一号一拍开关，默认开启\r\n                // sort: 1 // 默认排序\r\n              });\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 批量保存新增的关联关系\r\n      if (cityCategoryModuleRefs.length > 0) {\r\n        console.log(`准备保存${cityCategoryModuleRefs.length}个地市分类-模块关系，涉及${processedGroups.size}个地市-分类组合`);\r\n        const result = await batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);\r\n        console.log('保存地市分类-模块关系结果:', result);\r\n\r\n        // 重新获取关联ID，确保映射表包含新创建的关联关系\r\n        await this.fetchModuleRefsAfterSave();\r\n      }\r\n    },\r\n\r\n    // 保存模块关系后重新获取关联ID\r\n    async fetchModuleRefsAfterSave() {\r\n      const promises = [];\r\n      const processedRefIds = new Set(); // 避免重复查询\r\n\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.selectedCategories) {\r\n          const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n\r\n          if (refId && !processedRefIds.has(refId)) {\r\n            processedRefIds.add(refId); // 标记为已处理\r\n\r\n            promises.push((async () => {\r\n              const moduleRefs = await getModulesByCityCategoryRefId(refId);\r\n\r\n              // 更新映射表\r\n              for (const moduleRef of moduleRefs) {\r\n                const key = `${refId}_${moduleRef.moduleId}`;\r\n                if (!this.cityCategoryModuleRefMap[key]) {\r\n                  this.cityCategoryModuleRefMap[key] = moduleRef.refId;\r\n                }\r\n              }\r\n            })());\r\n          }\r\n        }\r\n      }\r\n\r\n      await Promise.all(promises);\r\n    },\r\n \r\n    // 保存模块相关的属性和标签关系\r\n    async saveModuleAttributeTagRelations() {\r\n      console.log(this.selectedCities,'===selectedCities');\r\n      \r\n      // 构建需要保存的商品属性类别关系数据\r\n      const moduleAttributeTypeRefs = [];\r\n\r\n      // 构建需要保存的商品标签关系数据\r\n      const moduleTagRefs = [];\r\n\r\n      // 收集已处理的城市-分类-模块组合，确保每个组都被处理\r\n      const processedGroups = new Set();\r\n\r\n      const allAttributesPromises = [];\r\n      const allTagsPromises = [];\r\n      const attributeTypeRefMap = {};\r\n      const tagRefMap = {};\r\n\r\n      // 1. 先获取所有的已有关系数据，减少重复API调用\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n\r\n          if (!cityCategoryRefId) {\r\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\r\n            continue;\r\n          }\r\n\r\n          // 处理该地市-分类下的所有模块\r\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n            const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n\r\n            if (!moduleRefId) {\r\n              console.warn(`未找到模块${moduleId}的关联ID，跳过处理`);\r\n              continue;\r\n            }\r\n\r\n            // 预加载该模块的属性和标签关系数据\r\n            const attributePromise = getAttributeTypesByModuleRefId(moduleRefId).then(attributes => {\r\n              attributeTypeRefMap[moduleRefId] = attributes;\r\n            });\r\n\r\n            const tagPromise = getTagsByModuleRefId(moduleRefId).then(tags => {\r\n              tagRefMap[moduleRefId] = tags;\r\n            });\r\n\r\n            allAttributesPromises.push(attributePromise);\r\n            allTagsPromises.push(tagPromise);\r\n          }\r\n        }\r\n      }\r\n\r\n      // 等待所有数据加载完成\r\n      await Promise.all([...allAttributesPromises, ...allTagsPromises]);\r\n\r\n      // 2. 遍历所有模块组合，确保每个组都被考虑\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n\r\n          if (!cityCategoryRefId) {\r\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\r\n            continue;\r\n          }\r\n\r\n          // 处理该地市-分类下的所有模块\r\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n            const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n\r\n            if (!moduleRefId) {\r\n              console.warn(`未找到模块${moduleId}的关联ID，跳过处理`);\r\n              continue;\r\n            }\r\n\r\n            // 标记该组合为已处理\r\n            processedGroups.add(key);\r\n\r\n            // 获取为该模块选择的属性和标签\r\n            const selectedAttributes = this.moduleAttributes[key] || [];\r\n            const selectedTags = this.moduleTags[key] || [];\r\n\r\n            console.log(`处理组合：${key}, 选择的属性数量: ${selectedAttributes.length}, 标签数量: ${selectedTags.length}`);\r\n\r\n            // 获取已有的商品属性类别关系\r\n            const existingAttributeTypes = attributeTypeRefMap[moduleRefId] || [];\r\n            const existingAttributeTypeIds = existingAttributeTypes.map(item => item.attributeTypeId);\r\n\r\n            // 获取已有的商品标签关系\r\n            const existingTags = tagRefMap[moduleRefId] || [];\r\n            const existingTagIds = existingTags.map(item => item.tagId);\r\n\r\n            console.log(`已有属性数量: ${existingAttributeTypeIds.length}, 标签数量: ${existingTagIds.length}`);\r\n\r\n            // 只为尚未关联的商品属性类别创建新的关联关系\r\n            for (const attributeTypeId of selectedAttributes) {\r\n              if (!existingAttributeTypeIds.includes(attributeTypeId)) {\r\n                moduleAttributeTypeRefs.push({\r\n                  cityCategoryModuleRefId: moduleRefId,\r\n                  goodsAttributeTypeId: attributeTypeId,\r\n                  status: 1, // 有效状态\r\n                  sort: 1 // 默认排序\r\n                });\r\n              }\r\n            }\r\n\r\n            // 只为尚未关联的商品标签创建新的关联关系\r\n            for (const tagId of selectedTags) {\r\n              if (!existingTagIds.includes(tagId)) {\r\n                moduleTagRefs.push({\r\n                  goodsTagId: tagId,\r\n                  cityCategoryModuleRefId: moduleRefId,\r\n                  status: 1, // 有效状态\r\n                  sort: 1 // 默认排序\r\n                });\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 并行批量保存新增的关系\r\n      const savePromises = [];\r\n\r\n      if (moduleAttributeTypeRefs.length > 0) {\r\n        console.log(`准备保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);\r\n        savePromises.push(\r\n          batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs)\r\n            .then(result => console.log('保存模块-商品属性类别关系结果:', result))\r\n        );\r\n      }\r\n\r\n      if (moduleTagRefs.length > 0) {\r\n        console.log(`准备保存${moduleTagRefs.length}个模块-商品标签关系`);\r\n        savePromises.push(\r\n          batchSaveModuleTagRef(moduleTagRefs)\r\n            .then(result => console.log('保存模块-商品标签关系结果:', result))\r\n        );\r\n      }\r\n\r\n      await Promise.all(savePromises);\r\n      console.log(`总共处理了${processedGroups.size}个城市-分类-模块组合`);\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.currentStep = 1;\r\n      this.selectedCities = [];\r\n      this.selectAllCities = false;\r\n      this.isIndeterminate = false;\r\n      this.selectedCategories = [];\r\n      this.selectedModules = [];\r\n      this.attributeTagTab = 'attribute';\r\n      this.selectedAttributeTypes = [];\r\n      this.selectedTags = [];\r\n      this.cityCategoryRefMap = {};\r\n      this.cityCategoryModuleRefMap = {};\r\n      this.categoryRefIdMap = {};\r\n      this.moduleRefIdMap = {};\r\n      this.attributeTypeRefIdMap = {};\r\n      this.tagRefIdMap = {};\r\n      this.cityCategories = {};\r\n      this.cityModules = {};\r\n      this.attributeTagTabs = {};\r\n      this.moduleAttributes = {};\r\n      this.moduleTags = {};\r\n      this.expandedCities = [];\r\n      this.expandedCityCategories = [];\r\n      this.expandedCityModules = [];\r\n    },\r\n\r\n    // 加载地市-模块分类关系\r\n    async loadCategoryRelations() {\r\n      this.categoryRefIdMap = {}; // 重置映射\r\n\r\n      // 首先清空所有地市的分类数据\r\n      for (const cityCode of this.selectedCities) {\r\n        if (!this.cityCategories[cityCode]) {\r\n          this.$set(this.cityCategories, cityCode, []);\r\n        } else {\r\n          this.cityCategories[cityCode] = [];\r\n        }\r\n      }\r\n\r\n      try {\r\n        // 使用优化的批量加载器 - 一次性获取所有地市的分类关系\r\n        const cityCategories = await configRelationDataLoader.batchLoadCityCategories(this.selectedCities);\r\n\r\n        // 处理返回的数据\r\n        for (const cityCode of this.selectedCities) {\r\n          const cityName = this.getCityName(cityCode);\r\n          const categoryRefs = cityCategories[cityCode] || [];\r\n          console.log(`加载地市 ${cityName}(${cityCode}) 的分类关系，获取到 ${categoryRefs.length} 条数据`);\r\n\r\n          // 将已有关联关系存入映射中\r\n          for (const ref of categoryRefs) {\r\n            const key = `${cityCode}_${ref.categoryId}`;\r\n            this.cityCategoryRefMap[key] = ref.refId;\r\n\r\n            // 直接将分类ID添加到对应地市的分类列表中\r\n            if (!this.cityCategories[cityCode].includes(ref.categoryId)) {\r\n              this.cityCategories[cityCode].push(ref.categoryId);\r\n            }\r\n\r\n            // 添加到ID映射表，使用cityCode+categoryId作为唯一键\r\n            const mapKey = `${cityCode}_${ref.categoryId}`;\r\n            this.categoryRefIdMap[mapKey] = {\r\n              refId: ref.refId,\r\n              cityCode: cityCode,\r\n              cityName: cityName,\r\n              categoryId: ref.categoryId,\r\n              categoryName: ref.categoryName\r\n            };\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('批量加载地市分类关系失败:', error);\r\n        throw error;\r\n      }\r\n\r\n      // 打印每个地市的分类数据，用于调试\r\n      for (const cityCode of this.selectedCities) {\r\n        console.log(`地市 ${this.getCityName(cityCode)}(${cityCode}) 的分类数量: ${this.cityCategories[cityCode].length}`);\r\n      }\r\n    },\r\n\r\n    // 加载地市分类-模块关系\r\n    async loadModuleRelations() {\r\n      this.moduleRefIdMap = {}; // 重置映射\r\n\r\n      // 清空所有组合的模块数据\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const key = `${cityCode}-${categoryId}`;\r\n          if (!this.cityModules[key]) {\r\n            this.$set(this.cityModules, key, []);\r\n          } else {\r\n            this.cityModules[key] = [];\r\n          }\r\n        }\r\n      }\r\n\r\n      try {\r\n        // 收集所有需要查询的地市分类关系ID\r\n        const cityCategoryRefIds = [];\r\n        const refIdToInfoMap = {}; // 关系ID到地市分类信息的映射\r\n\r\n        for (const cityCode of this.selectedCities) {\r\n          for (const categoryId of this.cityCategories[cityCode] || []) {\r\n            const refId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n            if (refId) {\r\n              cityCategoryRefIds.push(refId);\r\n              refIdToInfoMap[refId] = {\r\n                cityCode,\r\n                categoryId,\r\n                cityName: this.getCityName(cityCode),\r\n                categoryName: this.getCategoryName(categoryId),\r\n                cityCategoryKey: `${cityCode}-${categoryId}`\r\n              };\r\n            }\r\n          }\r\n        }\r\n\r\n        if (cityCategoryRefIds.length > 0) {\r\n          // 使用优化的批量加载器 - 一次性获取所有模块关系\r\n          const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds);\r\n\r\n          // 处理返回的数据\r\n          for (const refId of cityCategoryRefIds) {\r\n            const moduleRefs = categoryModules[refId] || [];\r\n            const info = refIdToInfoMap[refId];\r\n\r\n            console.log(`加载组合 ${info.cityName}(${info.cityCode})-${info.categoryName}(${info.categoryId}) 的模块关系，获取到 ${moduleRefs.length} 条数据`);\r\n\r\n            // 添加到ID映射表，并更新该组合的模块列表\r\n            for (const moduleRef of moduleRefs) {\r\n              const key = `${refId}_${moduleRef.moduleId}`;\r\n              this.cityCategoryModuleRefMap[key] = moduleRef.refId;\r\n\r\n              // 直接将模块ID添加到对应地市分类组合的模块列表中\r\n              if (!this.cityModules[info.cityCategoryKey].includes(moduleRef.moduleId)) {\r\n                this.cityModules[info.cityCategoryKey].push(moduleRef.moduleId);\r\n              }\r\n\r\n              // 使用完整的组合键来确保不同地市分类组合下的同一模块被分别处理\r\n              const mapKey = `${info.cityCode}_${info.categoryId}_${moduleRef.moduleId}`;\r\n              this.moduleRefIdMap[mapKey] = {\r\n                refId: moduleRef.refId,\r\n                cityCode: info.cityCode,\r\n                cityName: info.cityName,\r\n                categoryId: info.categoryId,\r\n                categoryName: info.categoryName,\r\n                moduleId: moduleRef.moduleId,\r\n                moduleName: moduleRef.moduleName\r\n              };\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('批量加载模块关系失败:', error);\r\n        throw error;\r\n      }\r\n\r\n      // 打印每个组合的模块数据，用于调试\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const key = `${cityCode}-${categoryId}`;\r\n          console.log(`组合 ${this.getCityName(cityCode)}(${cityCode})-${this.getCategoryName(categoryId)}(${categoryId}) 的模块数量: ${(this.cityModules[key] || []).length}`);\r\n        }\r\n      }\r\n    },\r\n\r\n    // 加载模块-商品属性类别和标签关系\r\n    async loadAttributeAndTagRelations() {\r\n      this.attributeTypeRefIdMap = {}; // 重置映射\r\n      this.tagRefIdMap = {}; // 重置映射\r\n\r\n      // 清空所有组合的属性和标签数据\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n\r\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n            const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n\r\n            // 初始化该组合的属性和标签列表\r\n            if (!this.moduleAttributes[key]) {\r\n              this.$set(this.moduleAttributes, key, []);\r\n            } else {\r\n              this.moduleAttributes[key] = [];\r\n            }\r\n\r\n            if (!this.moduleTags[key]) {\r\n              this.$set(this.moduleTags, key, []);\r\n            } else {\r\n              this.moduleTags[key] = [];\r\n            }\r\n\r\n            if (!this.attributeTagTabs[key]) {\r\n              this.$set(this.attributeTagTabs, key, 'attribute');\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 为每个地市-分类-模块组合单独获取属性和标签关系\r\n      const loadPromises = [];\r\n\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n          const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n\r\n          if (!cityCategoryRefId) {\r\n            console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID，跳过处理`);\r\n            continue;\r\n          }\r\n\r\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n            const moduleKey = `${cityCode}-${categoryId}-${moduleId}`;\r\n            const moduleName = this.getModuleName(moduleId);\r\n            const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n\r\n            if (moduleRefId) {\r\n              loadPromises.push((async () => {\r\n                // 加载商品属性类别关系\r\n                const attributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);\r\n                console.log(`加载组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${moduleName} 的属性关系，获取到 ${attributeTypes.length} 条数据`);\r\n\r\n                for (const attrType of attributeTypes) {\r\n                  // 将属性ID添加到该组合的属性列表中\r\n                  if (!this.moduleAttributes[moduleKey].includes(attrType.attributeTypeId)) {\r\n                    this.moduleAttributes[moduleKey].push(attrType.attributeTypeId);\r\n                  }\r\n\r\n                  // 添加到ID映射表，使用组合键确保不同组合下的同一属性被分别处理\r\n                  const mapKey = `${cityCode}_${categoryId}_${moduleId}_${attrType.attributeTypeId}`;\r\n                  this.attributeTypeRefIdMap[mapKey] = {\r\n                    refId: attrType.refId,\r\n                    moduleId: moduleId,\r\n                    moduleName: moduleName,\r\n                    attributeTypeId: attrType.attributeTypeId,\r\n                    attributeTypeName: attrType.attributeTypeName,\r\n                    groupKey: moduleKey\r\n                  };\r\n                }\r\n\r\n                // 加载商品标签关系\r\n                const tags = await getTagsByModuleRefId(moduleRefId);\r\n                console.log(`加载组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${moduleName} 的标签关系，获取到 ${tags.length} 条数据`);\r\n\r\n                for (const tag of tags) {\r\n                  // 将标签ID添加到该组合的标签列表中\r\n                  if (!this.moduleTags[moduleKey].includes(tag.tagId)) {\r\n                    this.moduleTags[moduleKey].push(tag.tagId);\r\n                  }\r\n\r\n                  // 添加到ID映射表，使用组合键确保不同组合下的同一标签被分别处理\r\n                  const mapKey = `${cityCode}_${categoryId}_${moduleId}_${tag.tagId}`;\r\n                  this.tagRefIdMap[mapKey] = {\r\n                    refId: tag.refId,\r\n                    moduleId: moduleId,\r\n                    moduleName: moduleName,\r\n                    tagId: tag.tagId,\r\n                    tagName: tag.tagName,\r\n                    groupKey: moduleKey\r\n                  };\r\n                }\r\n              })());\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      await Promise.all(loadPromises);\r\n\r\n      // 打印每个组合的属性和标签数据，用于调试\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n\r\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n            const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n            console.log(`组合 ${this.getCityName(cityCode)}-${this.getCategoryName(categoryId)}-${this.getModuleName(moduleId)} 的属性数量: ${(this.moduleAttributes[key] || []).length}, 标签数量: ${(this.moduleTags[key] || []).length}`);\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理移除模块分类\r\n    async handleRemoveCategories(cityCode, value, direction, movedKeys) {\r\n      if (direction === 'left') {\r\n        return; // 只处理从右向左移除（删除）的情况\r\n      }\r\n\r\n      // 获取被移除的分类ID\r\n      const removedCategoryIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\r\n\r\n      if (removedCategoryIds.length === 0) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });\r\n\r\n        // 收集需要删除的关系ID\r\n        const toDeleteRefIds = [];\r\n\r\n        for (const categoryId of removedCategoryIds) {\r\n          // 查找该分类在当前地市下的关系ID\r\n          const key = `${cityCode}_${categoryId}`;\r\n          const refId = this.cityCategoryRefMap[key];\r\n\r\n          if (refId) {\r\n            toDeleteRefIds.push(refId);\r\n\r\n            // 从映射中移除\r\n            delete this.cityCategoryRefMap[key];\r\n\r\n            // 从地市分类列表中移除\r\n            const index = this.cityCategories[cityCode].indexOf(categoryId);\r\n            if (index !== -1) {\r\n              this.cityCategories[cityCode].splice(index, 1);\r\n            }\r\n          }\r\n        }\r\n\r\n        // 批量删除关系\r\n        if (toDeleteRefIds.length > 0) {\r\n          await batchDeleteCityCategoryRef(toDeleteRefIds);\r\n        }\r\n\r\n        // 同步到selectedCategories\r\n        this.syncAllCategories();\r\n\r\n        loading.close();\r\n        this.$message.success(`已删除${toDeleteRefIds.length}个模块分类的关联关系`);\r\n      } catch (error) {\r\n        this.$message.error(error.message || '删除关联关系失败');\r\n      }\r\n    },\r\n\r\n    // 处理移除模块\r\n    async handleRemoveModules(cityCode, categoryId, value, direction, movedKeys) {\r\n      if (direction === 'left') {\r\n        return; // 只处理从右向左移除（删除）的情况\r\n      }\r\n\r\n      // 获取被移除的模块ID\r\n      const removedModuleIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\r\n\r\n      if (removedModuleIds.length === 0) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });\r\n\r\n        // 收集需要删除的关系ID\r\n        const toDeleteRefIds = [];\r\n\r\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n        if (cityCategoryRefId) {\r\n          for (const moduleId of removedModuleIds) {\r\n            const key = `${cityCategoryRefId}_${moduleId}`;\r\n            const refId = this.cityCategoryModuleRefMap[key];\r\n\r\n            if (refId) {\r\n              toDeleteRefIds.push(refId);\r\n\r\n              // 从映射中移除\r\n              delete this.cityCategoryModuleRefMap[key];\r\n\r\n              // 从地市-分类-模块列表中移除\r\n              const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n              const index = this.cityModules[cityCategoryKey].indexOf(moduleId);\r\n              if (index !== -1) {\r\n                this.cityModules[cityCategoryKey].splice(index, 1);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 批量删除关系\r\n        if (toDeleteRefIds.length > 0) {\r\n          await batchDeleteCityCategoryModuleRef(toDeleteRefIds);\r\n        }\r\n\r\n        // 同步到selectedModules\r\n        this.syncAllModules();\r\n\r\n        loading.close();\r\n        this.$message.success(`已删除${toDeleteRefIds.length}个模块的关联关系`);\r\n      } catch (error) {\r\n        this.$message.error(error.message || '删除关联关系失败');\r\n      }\r\n    },\r\n\r\n    // 处理移除商品属性类别\r\n    async handleRemoveAttributeTypes(cityCode, categoryId, moduleId, value, direction, movedKeys) {\r\n      if (direction === 'left') {\r\n        return; // 只处理从右向左移除（删除）的情况\r\n      }\r\n\r\n      // 获取被移除的属性类别ID\r\n      const removedAttributeTypeIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\r\n\r\n      if (removedAttributeTypeIds.length === 0) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });\r\n\r\n        // 收集需要删除的关系ID\r\n        const toDeleteRefIds = [];\r\n\r\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n        if (cityCategoryRefId) {\r\n          const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n          if (moduleRefId) {\r\n            // 获取当前模块的属性类别关系\r\n            const existingAttributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);\r\n\r\n            for (const attributeTypeId of removedAttributeTypeIds) {\r\n              // 在现有关系中查找对应的refId\r\n              const attrType = existingAttributeTypes.find(item => item.attributeTypeId === attributeTypeId);\r\n              if (attrType && attrType.refId) {\r\n                toDeleteRefIds.push(attrType.refId);\r\n\r\n                // 从模块-属性列表中移除\r\n                const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n                const index = this.moduleAttributes[key].indexOf(attributeTypeId);\r\n                if (index !== -1) {\r\n                  this.moduleAttributes[key].splice(index, 1);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 批量删除关系\r\n        if (toDeleteRefIds.length > 0) {\r\n          await batchDeleteModuleAttributeTypeRef(toDeleteRefIds);\r\n        }\r\n\r\n        // 同步到selectedAttributeTypes\r\n        this.syncAllAttributesAndTags();\r\n\r\n        loading.close();\r\n        this.$message.success(`已删除${toDeleteRefIds.length}个商品属性类别的关联关系`);\r\n      } catch (error) {\r\n        this.$message.error(error.message || '删除关联关系失败');\r\n      }\r\n    },\r\n\r\n    // 处理移除商品标签\r\n    async handleRemoveTags(cityCode, categoryId, moduleId, value, direction, movedKeys) {\r\n      if (direction === 'left') {\r\n        return; // 只处理从右向左移除（删除）的情况\r\n      }\r\n\r\n      // 获取被移除的标签ID\r\n      const removedTagIds = Array.isArray(movedKeys) ? movedKeys : [movedKeys];\r\n\r\n      if (removedTagIds.length === 0) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const loading = this.$loading({ lock: true, text: '正在删除关联关系...' });\r\n\r\n        // 收集需要删除的关系ID\r\n        const toDeleteRefIds = [];\r\n\r\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n        if (cityCategoryRefId) {\r\n          const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n          if (moduleRefId) {\r\n            // 获取当前模块的标签关系\r\n            const existingTags = await getTagsByModuleRefId(moduleRefId);\r\n\r\n            for (const tagId of removedTagIds) {\r\n              // 在现有关系中查找对应的refId\r\n              const tag = existingTags.find(item => item.tagId === tagId);\r\n              if (tag && tag.refId) {\r\n                toDeleteRefIds.push(tag.refId);\r\n\r\n                // 从模块-标签列表中移除\r\n                const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n                const index = this.moduleTags[key].indexOf(tagId);\r\n                if (index !== -1) {\r\n                  this.moduleTags[key].splice(index, 1);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 批量删除关系\r\n        if (toDeleteRefIds.length > 0) {\r\n          await batchDeleteModuleTagRef(toDeleteRefIds);\r\n        }\r\n\r\n        // 同步到selectedTags\r\n        this.syncAllAttributesAndTags();\r\n\r\n        loading.close();\r\n        this.$message.success(`已删除${toDeleteRefIds.length}个商品标签的关联关系`);\r\n      } catch (error) {\r\n        this.$message.error(error.message || '删除关联关系失败');\r\n      }\r\n    },\r\n\r\n    // 处理地市下的模块分类变化\r\n    handleCityCategoriesChange(cityCode, value) {\r\n      // 更新对应地市的模块分类\r\n      this.$set(this.cityCategories, cityCode, value);\r\n\r\n      // 展开刚刚选择的地市\r\n      if (value.length > 0 && !this.expandedCities.includes(cityCode)) {\r\n        this.expandedCities.push(cityCode);\r\n      }\r\n    },\r\n\r\n    // 处理地市和分类下的模块变化\r\n    handleCityModulesChange(cityCode, categoryId, value) {\r\n      const key = `${cityCode}-${categoryId}`;\r\n\r\n      // 更新对应地市和分类的模块\r\n      this.$set(this.cityModules, key, value);\r\n\r\n      // 展开刚刚选择的地市-分类\r\n      if (value.length > 0 && !this.expandedCityCategories.includes(key)) {\r\n        this.expandedCityCategories.push(key);\r\n      }\r\n    },\r\n\r\n    // 处理模块下的商品属性类别变化\r\n    handleModuleAttributesChange(cityCode, categoryId, moduleId, value) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n\r\n      // 更新对应模块的商品属性类别\r\n      this.$set(this.moduleAttributes, key, value);\r\n\r\n      // 展开刚刚选择的地市-分类-模块\r\n      if (value.length > 0 && !this.expandedCityModules.includes(key)) {\r\n        this.expandedCityModules.push(key);\r\n      }\r\n    },\r\n\r\n    // 处理模块下的商品标签变化\r\n    handleModuleTagsChange(cityCode, categoryId, moduleId, value) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      \r\n      // 更新对应模块的商品标签\r\n      this.$set(this.moduleTags, key, value);\r\n     console.log(this.expandedCityModules,'=this.expandedCityModules');\r\n     \r\n      // 展开刚刚选择的地市-分类-模块\r\n      if (value.length > 0 && !this.expandedCityModules.includes(key)) {\r\n        this.expandedCityModules.push(key);\r\n      }\r\n \r\n    },\r\n\r\n    // 同步所有模块分类到selectedCategories\r\n    syncAllCategories() {\r\n      // 重置selectedCategories\r\n      this.selectedCategories = [];\r\n\r\n      // 收集所有地市下的模块分类\r\n      for (const cityCode in this.cityCategories) {\r\n        const categories = this.cityCategories[cityCode] || [];\r\n\r\n        // 将未添加的分类添加到总分类列表中\r\n        for (const categoryId of categories) {\r\n          if (!this.selectedCategories.includes(categoryId)) {\r\n            this.selectedCategories.push(categoryId);\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    // 同步所有模块到selectedModules\r\n    syncAllModules() {\r\n      // 重置selectedModules\r\n      this.selectedModules = [];\r\n\r\n      // 收集所有地市-分类下的模块\r\n      for (const key in this.cityModules) {\r\n        const modules = this.cityModules[key] || [];\r\n\r\n        // 将未添加的模块添加到总模块列表中\r\n        for (const moduleId of modules) {\r\n          if (!this.selectedModules.includes(moduleId)) {\r\n            this.selectedModules.push(moduleId);\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    // 同步所有属性类别和标签\r\n    syncAllAttributesAndTags() {\r\n      // 清空选中的属性和标签\r\n      this.selectedAttributeTypes = [];\r\n      this.selectedTags = [];\r\n\r\n      // 收集所有组合中已选的属性和标签，避免重复\r\n      const attributeSet = new Set();\r\n      const tagSet = new Set();\r\n\r\n      // 遍历所有模块组合\r\n      for (const cityCode of this.selectedCities) {\r\n        for (const categoryId of this.cityCategories[cityCode] || []) {\r\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\r\n\r\n          for (const moduleId of this.cityModules[cityCategoryKey] || []) {\r\n            const moduleKey = `${cityCode}-${categoryId}-${moduleId}`;\r\n\r\n            // 添加该组合下的属性\r\n            for (const attributeTypeId of this.moduleAttributes[moduleKey] || []) {\r\n              attributeSet.add(attributeTypeId);\r\n            }\r\n\r\n            // 添加该组合下的标签\r\n            for (const tagId of this.moduleTags[moduleKey] || []) {\r\n              tagSet.add(tagId);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 转换为数组\r\n      this.selectedAttributeTypes = Array.from(attributeSet);\r\n      this.selectedTags = Array.from(tagSet);\r\n\r\n      console.log('同步后的全局属性数量:', this.selectedAttributeTypes.length);\r\n      console.log('同步后的全局标签数量:', this.selectedTags.length);\r\n    },\r\n\r\n    // 获取地市下选中的模块分类数量\r\n    getCityCategoryCount(cityCode) {\r\n      return (this.cityCategories[cityCode] || []).length;\r\n    },\r\n\r\n    // 获取地市和分类下选中的模块数量\r\n    getCityCategoryModuleCount(cityCode, categoryId) {\r\n      const key = `${cityCode}-${categoryId}`;\r\n      return (this.cityModules[key] || []).length;\r\n    },\r\n\r\n    // 获取模块下选中的商品属性类别数量\r\n    getModuleAttributeCount(cityCode, categoryId, moduleId) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      return (this.moduleAttributes[key] || []).length;\r\n    },\r\n\r\n    // 获取模块下选中的商品标签数量\r\n    getModuleTagCount(cityCode, categoryId, moduleId) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      return (this.moduleTags[key] || []).length;\r\n    },\r\n\r\n    // 获取地市下的模块分类数组\r\n    getCityModulesArray(cityCode, categoryId) {\r\n      const key = `${cityCode}-${categoryId}`;\r\n      return this.cityModules[key] || [];\r\n    },\r\n\r\n    // 获取模块的标签页值\r\n    getModuleTabsValue(cityCode, categoryId, moduleId) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      return this.attributeTagTabs[key] || 'attribute';\r\n    },\r\n\r\n    // 获取模块的商品属性类别数组\r\n    getModuleAttributesArray(cityCode, categoryId, moduleId) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      return this.moduleAttributes[key] || [];\r\n    },\r\n\r\n    // 获取模块的商品标签数组\r\n    getModuleTagsArray(cityCode, categoryId, moduleId) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      return this.moduleTags[key] || [];\r\n    },\r\n\r\n    // 批量更新某个地市的模块分类选择\r\n    updateCityCategories(cityCode, selectedCategories) {\r\n      // 更新数据\r\n      this.$set(this.cityCategories, cityCode, selectedCategories);\r\n\r\n      // 同步到全局的selectedCategories\r\n      this.syncAllCategories();\r\n    },\r\n\r\n    // 更新地市+分类的模块\r\n    updateCityModules(cityCode, categoryId, value) {\r\n      const key = `${cityCode}-${categoryId}`;\r\n      this.$set(this.cityModules, key, value);\r\n      this.handleCityModulesChange(cityCode, categoryId, value);\r\n    },\r\n\r\n    // 更新模块的标签页\r\n    updateModuleTab(cityCode, categoryId, moduleId, value) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      this.$set(this.attributeTagTabs, key, value);\r\n    },\r\n\r\n    // 更新模块的商品属性类别\r\n    updateModuleAttributes(cityCode, categoryId, moduleId, value) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      this.$set(this.moduleAttributes, key, value);\r\n      this.handleModuleAttributesChange(cityCode, categoryId, moduleId, value);\r\n    },\r\n\r\n    // 更新模块的商品标签\r\n    async updateModuleTags(cityCode, categoryId, moduleId, value) {\r\n      const key = `${cityCode}-${categoryId}-${moduleId}`;\r\n      // 使用Vue.set确保响应式更新\r\n      this.$set(this.moduleTags, key, [...value]);\r\n      // this.$set(this.moduleTags, key, value);\r\n\r\n\r\n\r\n      // 触发后续处理\r\n      this.handleModuleTagsChange(cityCode, categoryId, moduleId, value);\r\n    },\r\n\r\n    // 保存属性类别排序\r\n    async saveAttributeSort(cityCode, categoryId, moduleId, sortedAttributeIds) {\r\n      try {\r\n        // 获取模块关联ID\r\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n        if (!cityCategoryRefId) {\r\n          console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID`);\r\n          return;\r\n        }\r\n\r\n        const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n        if (!moduleRefId) {\r\n          console.warn(`未找到模块${moduleId}的关联ID`);\r\n          return;\r\n        }\r\n\r\n        // 获取当前的属性关联关系\r\n        const attributeTypes = await getAttributeTypesByModuleRefId(moduleRefId);\r\n\r\n        // 构建排序更新数据\r\n        const updateList = [];\r\n        sortedAttributeIds.forEach((attributeTypeId, index) => {\r\n          const existingRef = attributeTypes.find(attr => attr.attributeTypeId === attributeTypeId);\r\n          if (existingRef) {\r\n            updateList.push({\r\n              id: existingRef.refId,\r\n              sort: index + 1 // 排序从1开始\r\n            });\r\n          }\r\n        });\r\n\r\n        if (updateList.length > 0) {\r\n          await batchUpdateRankSx(updateList);\r\n          console.log(`属性排序保存成功，更新了${updateList.length}条记录`);\r\n        }\r\n      } catch (error) {\r\n        console.error('保存属性排序失败:', error);\r\n        this.$message.error('保存属性排序失败');\r\n      }\r\n    },\r\n\r\n    // 保存标签排序\r\n    async saveTagSort(cityCode, categoryId, moduleId, sortedTagIds) {\r\n      try {\r\n        // 获取模块关联ID\r\n        const cityCategoryRefId = this.cityCategoryRefMap[`${cityCode}_${categoryId}`];\r\n        if (!cityCategoryRefId) {\r\n          console.warn(`未找到城市${cityCode}与分类${categoryId}的关联ID`);\r\n          return;\r\n        }\r\n\r\n        const moduleRefId = this.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\r\n        if (!moduleRefId) {\r\n          console.warn(`未找到模块${moduleId}的关联ID`);\r\n          return;\r\n        }\r\n\r\n        // 获取当前的标签关联关系\r\n        const tags = await getTagsByModuleRefId(moduleRefId);\r\n\r\n        // 构建排序更新数据\r\n        const updateList = [];\r\n        sortedTagIds.forEach((tagId, index) => {\r\n          const existingRef = tags.find(tag => tag.tagId === tagId);\r\n          if (existingRef) {\r\n            updateList.push({\r\n              id: existingRef.refId,\r\n              sort: index + 1 // 排序从1开始\r\n            });\r\n          }\r\n        });\r\n\r\n        if (updateList.length > 0) {\r\n          await batchUpdateRankSx(updateList);\r\n          console.log(`标签排序保存成功，更新了${updateList.length}条记录`);\r\n        }\r\n      } catch (error) {\r\n        console.error('保存标签排序失败:', error);\r\n        this.$message.error('保存标签排序失败');\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.config-header {\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.config-header h2 {\r\n  margin: 0 0 10px 0;\r\n  color: #303133;\r\n}\r\n\r\n.config-desc {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.config-operation {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.config-content {\r\n  min-height: 400px;\r\n}\r\n\r\n.step-content {\r\n  padding: 20px 0;\r\n}\r\n\r\n.step-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.step-actions {\r\n  margin-top: 30px;\r\n  text-align: right;\r\n}\r\n\r\n.city-selection, .category-selection, .module-selection, .attribute-tag-selection {\r\n  margin-top: 20px;\r\n}\r\n\r\n.city-list {\r\n  margin-top: 15px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.city-item {\r\n  margin-right: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.selected-cities-info, .selected-cities-categories-info, .selected-info {\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  background-color: #f8f8f8;\r\n  border-radius: 4px;\r\n}\r\n\r\n.info-row {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.info-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.info-label {\r\n  font-weight: bold;\r\n  margin-right: 10px;\r\n}\r\n\r\n.city-tag, .category-tag, .module-tag {\r\n  margin-right: 8px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.category-transfer, .module-transfer, .attribute-transfer, .tag-transfer {\r\n  margin-top: 15px;\r\n}\r\n\r\n.transfer-item {\r\n  display: block;\r\n  padding: 5px 0;\r\n}\r\n\r\n.attribute-tag-tabs {\r\n  margin-top: 15px;\r\n}\r\n\r\n.existing-relations {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.existing-relations h4 {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n\r\n.city-groups {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.city-groups .el-collapse-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.city-groups .el-collapse-item__header {\r\n  padding: 10px;\r\n  background-color: #f8f8f8;\r\n  border-bottom: none;\r\n}\r\n\r\n.city-groups .el-collapse-item__header .group-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.city-groups .el-collapse-item__header .group-title {\r\n  font-weight: bold;\r\n}\r\n\r\n.city-groups .el-collapse-item__header .count-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.city-category-groups {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.city-category-groups .el-collapse-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.city-category-groups .el-collapse-item__header {\r\n  padding: 10px;\r\n  background-color: #f8f8f8;\r\n  border-bottom: none;\r\n}\r\n\r\n.city-category-groups .el-collapse-item__header .group-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.city-category-groups .el-collapse-item__header .group-title {\r\n  font-weight: bold;\r\n}\r\n\r\n.city-category-groups .el-collapse-item__header .count-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n.city-category-module-groups {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.city-category-module-groups .el-collapse-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.city-category-module-groups .el-collapse-item__header {\r\n  padding: 10px;\r\n  background-color: #f8f8f8;\r\n  border-bottom: none;\r\n}\r\n\r\n.city-category-module-groups .el-collapse-item__header .group-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.city-category-module-groups .el-collapse-item__header .group-title {\r\n  font-weight: bold;\r\n}\r\n\r\n.city-category-module-groups .el-collapse-item__header .count-tag {\r\n  margin-left: 10px;\r\n}\r\n\r\n::v-deep .el-transfer-panel{\r\n  width:35%;\r\n}\r\n::v-deep .el-transfer__buttons{\r\n  width: 28%;\r\n  padding: 0;\r\n  text-align: center;\r\n}\r\n\r\n</style> "]}, "metadata": {}, "sourceType": "module"}