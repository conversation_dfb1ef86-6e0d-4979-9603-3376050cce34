/**
 * 关联配置管理优化工具类
 * 解决循环调用接口和保存速度慢的问题
 * <AUTHOR>
 * @date 2025-05-06
 */

import {
  batchGetCategoriesByCityCodes,
  batchGetModulesByCityCategoryRefIds,
  batchGetAttributeTypesByModuleRefIds,
  batchGetTagsByModuleRefIds,
  batchSaveCityCategoryRef,
  batchSaveCityCategoryModuleRef,
  batchSaveModuleAttributeTypeRef,
  batchSaveModuleTagRef,
  batchDeleteCityCategoryRef,
  batchDeleteCityCategoryModuleRef,
  batchDeleteModuleAttributeTypeRef,
  batchDeleteModuleTagRef,
  getCategoriesByCityCode,
  getModulesByCityCategoryRefId,
  getAttributeTypesByModuleRefId,
  getTagsByModuleRefId
} from '@/api/hnzsxH5/configRelation';

/**
 * 关联配置数据加载器
 */
export class ConfigRelationDataLoader {
  constructor() {
    this.cache = new Map(); // 数据缓存
    this.loadingPromises = new Map(); // 防止重复加载
  }

  /**
   * 批量加载地市分类关系数据
   * @param {string[]} cityCodes 地市编码数组
   * @returns {Promise<Object>} 地市分类关系数据
   */
  async batchLoadCityCategories(cityCodes) {
    const cacheKey = `city_categories_${cityCodes.sort().join(',')}`;

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // 检查是否正在加载
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // 开始加载
    const loadingPromise = this._loadCityCategoriesData(cityCodes);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const result = await loadingPromise;
      this.cache.set(cacheKey, result);
      return result;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 内部方法：加载地市分类数据
   */
  async _loadCityCategoriesData(cityCodes) {
    try {
      // 尝试使用批量接口
      const batchResult = await batchGetCategoriesByCityCodes(cityCodes);
      return batchResult;
    } catch (error) {
      console.warn('批量接口调用失败，降级为单个接口调用:', error);

      // 降级为单个接口调用
      const result = {};
      const promises = cityCodes.map(async cityCode => {
        const categoryRefs = await getCategoriesByCityCode(cityCode);
        result[cityCode] = categoryRefs;
      });

      await Promise.all(promises);
      return result;
    }
  }

  /**
   * 批量加载模块关系数据
   * @param {number[]} cityCategoryRefIds 地市分类关系ID数组
   * @returns {Promise<Object>} 模块关系数据
   */
  async batchLoadCategoryModules(cityCategoryRefIds) {
    const cacheKey = `category_modules_${cityCategoryRefIds.sort().join(',')}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const loadingPromise = this._loadCategoryModulesData(cityCategoryRefIds);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const result = await loadingPromise;
      this.cache.set(cacheKey, result);
      return result;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 内部方法：加载模块数据
   */
  async _loadCategoryModulesData(cityCategoryRefIds) {
    try {
      const batchResult = await batchGetModulesByCityCategoryRefIds(cityCategoryRefIds);
      return batchResult;
    } catch (error) {
      console.warn('批量接口调用失败，降级为单个接口调用:', error);

      const result = {};
      const promises = cityCategoryRefIds.map(async refId => {
        const moduleRefs = await getModulesByCityCategoryRefId(refId);
        result[refId] = moduleRefs;
      });

      await Promise.all(promises);
      return result;
    }
  }

  /**
   * 批量加载模块属性和标签数据
   * @param {number[]} moduleRefIds 模块关系ID数组
   * @returns {Promise<{attributes: Object, tags: Object}>} 属性和标签数据
   */
  async batchLoadModuleAttributesAndTags(moduleRefIds) {
    const cacheKey = `module_attrs_tags_${moduleRefIds.sort().join(',')}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const loadingPromise = this._loadModuleAttributesAndTagsData(moduleRefIds);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const result = await loadingPromise;
      this.cache.set(cacheKey, result);
      return result;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 内部方法：加载模块属性和标签数据
   */
  async _loadModuleAttributesAndTagsData(moduleRefIds) {
    try {
      // 并行调用批量接口
      const [attributesResult, tagsResult] = await Promise.all([
        batchGetAttributeTypesByModuleRefIds(moduleRefIds),
        batchGetTagsByModuleRefIds(moduleRefIds)
      ]);

      // 转换数据结构：从 {attributes: Map, tags: Map} 转换为 {moduleRefId: {attributes: [], tags: []}}
      const result = {};

      // 初始化所有模块的数据结构
      for (const moduleRefId of moduleRefIds) {
        result[moduleRefId] = {
          attributes: attributesResult[moduleRefId] || [],
          tags: tagsResult[moduleRefId] || []
        };
      }

      return result;
    } catch (error) {
      console.warn('批量接口调用失败，降级为单个接口调用:', error);

      const result = {};

      const promises = moduleRefIds.map(async refId => {
        const [attrTypes, tagList] = await Promise.all([
          getAttributeTypesByModuleRefId(refId),
          getTagsByModuleRefId(refId)
        ]);

        result[refId] = {
          attributes: attrTypes,
          tags: tagList
        };
      });

      await Promise.all(promises);
      return result;
    }
  }

  /**
   * 一次性加载所有关联数据
   * @param {string[]} cityCodes 地市编码数组
   * @returns {Promise<Object>} 完整的关联数据结构
   */
  async loadAllRelationData(cityCodes) {
    console.log('开始批量加载关联数据，地市数量:', cityCodes.length);
    const startTime = Date.now();

    // 第一步：加载地市分类关系
    const cityCategories = await this.batchLoadCityCategories(cityCodes);
    console.log('地市分类关系加载完成，耗时:', Date.now() - startTime, 'ms');

    // 收集所有地市分类关系ID
    const cityCategoryRefIds = [];
    const cityCategoryRefMap = {};

    for (const cityCode of cityCodes) {
      const categoryRefs = cityCategories[cityCode] || [];
      for (const ref of categoryRefs) {
        cityCategoryRefIds.push(ref.refId);
        cityCategoryRefMap[`${cityCode}_${ref.categoryId}`] = ref.refId;
      }
    }

    // 第二步：批量加载模块关系
    let categoryModules = {};
    if (cityCategoryRefIds.length > 0) {
      categoryModules = await this.batchLoadCategoryModules(cityCategoryRefIds);
      console.log('模块关系加载完成，耗时:', Date.now() - startTime, 'ms');
    }

    // 收集所有模块关系ID
    const moduleRefIds = [];
    const cityCategoryModuleRefMap = {};

    for (const refId of cityCategoryRefIds) {
      const moduleRefs = categoryModules[refId] || [];
      for (const moduleRef of moduleRefs) {
        moduleRefIds.push(moduleRef.refId);
        cityCategoryModuleRefMap[`${refId}_${moduleRef.moduleId}`] = moduleRef.refId;
      }
    }

    // 第三步：批量加载属性和标签
    let moduleAttributesAndTags = { attributes: {}, tags: {} };
    if (moduleRefIds.length > 0) {
      moduleAttributesAndTags = await this.batchLoadModuleAttributesAndTags(moduleRefIds);
      console.log('属性标签加载完成，总耗时:', Date.now() - startTime, 'ms');
    }

    return {
      cityCategories,
      categoryModules,
      moduleAttributesAndTags,
      cityCategoryRefMap,
      cityCategoryModuleRefMap
    };
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      loadingCount: this.loadingPromises.size
    };
  }
}

// 创建单例实例
export const configRelationDataLoader = new ConfigRelationDataLoader();

/**
 * 关联配置保存优化器
 */
export class ConfigRelationSaveOptimizer {
  constructor() {
    this.allRelationData = null; // 缓存的完整关联数据
  }

  /**
   * 优化的保存配置流程
   * @param {Object} componentInstance 组件实例
   * @returns {Promise<void>}
   */
  async optimizedSaveConfiguration(componentInstance) {
    const startTime = Date.now();
    console.log('开始优化的保存配置流程...');

    try {
      // 1. 一次性加载所有现有关联数据（如果还没有加载）
      if (!this.allRelationData) {
        await this.loadAllExistingRelations(componentInstance);
      }

      // 2. 并行执行所有保存操作
      await Promise.all([
        this.optimizedSaveCityCategoryRelations(componentInstance),
        this.optimizedSaveCityCategoryModuleRelations(componentInstance),
        this.optimizedSaveModuleAttributeTagRelations(componentInstance)
      ]);

      // 3. 最后执行清理操作
      await this.optimizedDeleteUnusedRelations(componentInstance);

      console.log(`优化的保存配置完成，总耗时: ${Date.now() - startTime}ms`);
    } catch (error) {
      console.error('优化的保存配置失败:', error);
      throw error;
    }
  }

  /**
   * 一次性加载所有现有关联数据
   */
  async loadAllExistingRelations(componentInstance) {
    console.log('开始加载所有现有关联数据...');
    const startTime = Date.now();

    this.allRelationData = await configRelationDataLoader.loadAllRelationData(componentInstance.selectedCities);

    console.log(`所有现有关联数据加载完成，耗时: ${Date.now() - startTime}ms`);
  }

  /**
   * 优化的地市-分类关系保存
   */
  async optimizedSaveCityCategoryRelations(componentInstance) {
    const cityCategoryRefs = [];
    const existingRelations = this.allRelationData.cityCategories;

    console.log('开始处理地市-分类关系保存...');
    console.log('选中的地市:', componentInstance.selectedCities);
    console.log('地市分类数据:', componentInstance.cityCategories);
    console.log('现有关系数据:', existingRelations);

    for (const cityCode of componentInstance.selectedCities) {
      const categories = componentInstance.cityCategories[cityCode] || [];
      console.log(`地市 ${cityCode} 的分类:`, categories);

      for (const categoryId of categories) {
        const key = `${cityCode}_${categoryId}`;

        // 检查是否已存在关联关系
        const existingRefs = existingRelations[cityCode] || [];
        const exists = existingRefs.some(ref => ref.categoryId === categoryId);

        console.log(`检查关系 ${key}: 已存在=${exists}`);

        if (!exists) {
          cityCategoryRefs.push({
            cityCode: cityCode,
            categoryId: categoryId,
            status: 1,
            sort: 1
          });
          console.log(`添加新的地市-分类关系: ${key}`);
        }
      }
    }

    if (cityCategoryRefs.length > 0) {
      console.log(`批量保存${cityCategoryRefs.length}个地市-模块分类关系`);
      await batchSaveCityCategoryRef(cityCategoryRefs);

      // 更新组件的映射关系
      await this.updateCityCategoryRefMap(componentInstance);
    }
  }

  /**
   * 优化的地市分类-模块关系保存
   */
  async optimizedSaveCityCategoryModuleRelations(componentInstance) {
    const cityCategoryModuleRefs = [];
    const existingModuleRelations = this.allRelationData.categoryModules;

    console.log('开始处理地市分类-模块关系保存...');
    console.log('地市模块数据:', componentInstance.cityModules);
    console.log('地市分类关系映射:', componentInstance.cityCategoryRefMap);
    console.log('现有模块关系数据:', existingModuleRelations);

    for (const cityCode of componentInstance.selectedCities) {
      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {
        const cityCategoryKey = `${cityCode}-${categoryId}`;
        const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];

        console.log(`处理组合 ${cityCategoryKey}, refId: ${refId}`);

        if (!refId) {
          console.log(`跳过组合 ${cityCategoryKey}: 没有找到refId`);
          continue;
        }

        const modules = componentInstance.cityModules[cityCategoryKey] || [];
        const existingModuleRefs = existingModuleRelations[refId] || [];

        console.log(`组合 ${cityCategoryKey} 的模块:`, modules);
        console.log(`组合 ${cityCategoryKey} 的现有模块关系:`, existingModuleRefs);

        for (const moduleId of modules) {
          const exists = existingModuleRefs.some(ref => ref.moduleId === moduleId);

          console.log(`检查模块关系 ${cityCategoryKey}-${moduleId}: 已存在=${exists}`);

          if (!exists) {
            cityCategoryModuleRefs.push({
              cityCategoryRefId: refId,
              moduleId: moduleId,
              status: 1,
              isOneBeat: 1
            });
            console.log(`添加新的模块关系: ${cityCategoryKey}-${moduleId}`);
          }
        }
      }
    }

    if (cityCategoryModuleRefs.length > 0) {
      console.log(`批量保存${cityCategoryModuleRefs.length}个地市分类-模块关系`);
      await batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);

      // 更新组件的映射关系
      await this.updateCityCategoryModuleRefMap(componentInstance);
    }
  }

  /**
   * 优化的模块-属性标签关系保存
   */
  async optimizedSaveModuleAttributeTagRelations(componentInstance) {
    const moduleAttributeTypeRefs = [];
    const moduleTagRefs = [];
    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;

    for (const cityCode of componentInstance.selectedCities) {
      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {
        const cityCategoryKey = `${cityCode}-${categoryId}`;
        const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];

        if (!cityCategoryRefId) continue;

        for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {
          const key = `${cityCode}-${categoryId}-${moduleId}`;
          const moduleRefId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];

          if (!moduleRefId) continue;

          // 处理属性类别
          const selectedAttributes = componentInstance.moduleAttributes[key] || [];
          const moduleData = existingAttributesAndTags[moduleRefId] || { attributes: [], tags: [] };
          const existingAttributes = moduleData.attributes || [];
          const existingAttributeIds = existingAttributes.map(item => item.attributeTypeId);

          for (const attributeTypeId of selectedAttributes) {
            if (!existingAttributeIds.includes(attributeTypeId)) {
              moduleAttributeTypeRefs.push({
                cityCategoryModuleRefId: moduleRefId,
                goodsAttributeTypeId: attributeTypeId,
                status: 1,
                sort: 1
              });
            }
          }

          // 处理标签
          const selectedTags = componentInstance.moduleTags[key] || [];
          const existingTags = moduleData.tags || [];
          const existingTagIds = existingTags.map(item => item.tagId);

          for (const tagId of selectedTags) {
            if (!existingTagIds.includes(tagId)) {
              moduleTagRefs.push({
                goodsTagId: tagId,
                cityCategoryModuleRefId: moduleRefId,
                status: 1,
                sort: 1
              });
            }
          }
        }
      }
    }

    // 并行保存属性和标签关系
    const savePromises = [];

    if (moduleAttributeTypeRefs.length > 0) {
      console.log(`批量保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);
      savePromises.push(batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs));
    }

    if (moduleTagRefs.length > 0) {
      console.log(`批量保存${moduleTagRefs.length}个模块-商品标签关系`);
      savePromises.push(batchSaveModuleTagRef(moduleTagRefs));
    }

    await Promise.all(savePromises);
  }

  /**
   * 优化的删除无用关系
   */
  async optimizedDeleteUnusedRelations(componentInstance) {
    // 基于已加载的数据进行删除操作，避免重复查询
    const toDeleteCategoryRefIds = [];
    const toDeleteModuleRefIds = [];
    const toDeleteAttrRefIds = [];
    const toDeleteTagRefIds = [];

    // 1. 找出需要删除的地市-分类关系
    this.findUnusedCityCategoryRelations(componentInstance, toDeleteCategoryRefIds);

    // 2. 找出需要删除的地市分类-模块关系
    this.findUnusedCityCategoryModuleRelations(componentInstance, toDeleteModuleRefIds);

    // 3. 找出需要删除的模块-属性关系
    this.findUnusedModuleAttributeRelations(componentInstance, toDeleteAttrRefIds);

    // 4. 找出需要删除的模块-标签关系
    this.findUnusedModuleTagRelations(componentInstance, toDeleteTagRefIds);

    // 并行执行删除操作
    const deletePromises = [];

    if (toDeleteCategoryRefIds.length > 0) {
      deletePromises.push(batchDeleteCityCategoryRef(toDeleteCategoryRefIds));
    }

    if (toDeleteModuleRefIds.length > 0) {
      deletePromises.push(batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds));
    }

    if (toDeleteAttrRefIds.length > 0) {
      deletePromises.push(batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds));
    }

    if (toDeleteTagRefIds.length > 0) {
      deletePromises.push(batchDeleteModuleTagRef(toDeleteTagRefIds));
    }

    await Promise.all(deletePromises);
  }

  /**
   * 找出需要删除的地市-分类关系
   */
  findUnusedCityCategoryRelations(componentInstance, toDeleteRefIds) {
    const existingRelations = this.allRelationData.cityCategories;

    for (const cityCode in existingRelations) {
      const existingRefs = existingRelations[cityCode] || [];
      const currentCategories = componentInstance.cityCategories[cityCode] || [];

      for (const ref of existingRefs) {
        // 如果现有的关系在当前选择中不存在，则标记为删除
        if (!currentCategories.includes(ref.categoryId)) {
          toDeleteRefIds.push(ref.refId);
          console.log(`标记删除地市-分类关系: ${cityCode}-${ref.categoryId} (refId: ${ref.refId})`);
        }
      }
    }
  }

  /**
   * 找出需要删除的地市分类-模块关系
   */
  findUnusedCityCategoryModuleRelations(componentInstance, toDeleteRefIds) {
    const existingModuleRelations = this.allRelationData.categoryModules;

    for (const cityCategoryRefId in existingModuleRelations) {
      const existingModuleRefs = existingModuleRelations[cityCategoryRefId] || [];

      // 找到对应的地市-分类组合
      let cityCategoryKey = null;
      for (const cityCode of componentInstance.selectedCities) {
        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {
          const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];
          if (refId == cityCategoryRefId) {
            cityCategoryKey = `${cityCode}-${categoryId}`;
            break;
          }
        }
        if (cityCategoryKey) break;
      }

      if (!cityCategoryKey) {
        // 如果找不到对应的组合，说明整个地市-分类关系都被删除了，删除所有模块关系
        for (const moduleRef of existingModuleRefs) {
          toDeleteRefIds.push(moduleRef.refId);
          console.log(`标记删除模块关系(地市分类已删除): refId ${moduleRef.refId}`);
        }
        continue;
      }

      const currentModules = componentInstance.cityModules[cityCategoryKey] || [];

      for (const moduleRef of existingModuleRefs) {
        // 如果现有的模块关系在当前选择中不存在，则标记为删除
        if (!currentModules.includes(moduleRef.moduleId)) {
          toDeleteRefIds.push(moduleRef.refId);
          console.log(`标记删除模块关系: ${cityCategoryKey}-${moduleRef.moduleId} (refId: ${moduleRef.refId})`);
        }
      }
    }
  }

  /**
   * 找出需要删除的模块-属性关系
   */
  findUnusedModuleAttributeRelations(componentInstance, toDeleteRefIds) {
    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;

    for (const moduleRefId in existingAttributesAndTags) {
      const moduleData = existingAttributesAndTags[moduleRefId];
      const existingAttributes = moduleData.attributes || [];

      // 找到对应的模块组合
      let moduleKey = null;
      for (const cityCode of componentInstance.selectedCities) {
        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {
          const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];
          if (!cityCategoryRefId) continue;

          const cityCategoryKey = `${cityCode}-${categoryId}`;
          for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {
            const refId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];
            if (refId == moduleRefId) {
              moduleKey = `${cityCode}-${categoryId}-${moduleId}`;
              break;
            }
          }
          if (moduleKey) break;
        }
        if (moduleKey) break;
      }

      if (!moduleKey) {
        // 如果找不到对应的模块组合，说明模块关系被删除了，删除所有属性关系
        for (const attrRef of existingAttributes) {
          toDeleteRefIds.push(attrRef.refId);
        }
        continue;
      }

      const currentAttributes = componentInstance.moduleAttributes[moduleKey] || [];

      for (const attrRef of existingAttributes) {
        // 如果现有的属性关系在当前选择中不存在，则标记为删除
        if (!currentAttributes.includes(attrRef.attributeTypeId)) {
          toDeleteRefIds.push(attrRef.refId);
        }
      }
    }
  }

  /**
   * 找出需要删除的模块-标签关系
   */
  findUnusedModuleTagRelations(componentInstance, toDeleteRefIds) {
    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;

    for (const moduleRefId in existingAttributesAndTags) {
      const moduleData = existingAttributesAndTags[moduleRefId];
      const existingTags = moduleData.tags || [];

      // 找到对应的模块组合
      let moduleKey = null;
      for (const cityCode of componentInstance.selectedCities) {
        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {
          const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];
          if (!cityCategoryRefId) continue;

          const cityCategoryKey = `${cityCode}-${categoryId}`;
          for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {
            const refId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];
            if (refId == moduleRefId) {
              moduleKey = `${cityCode}-${categoryId}-${moduleId}`;
              break;
            }
          }
          if (moduleKey) break;
        }
        if (moduleKey) break;
      }

      if (!moduleKey) {
        // 如果找不到对应的模块组合，说明模块关系被删除了，删除所有标签关系
        for (const tagRef of existingTags) {
          toDeleteRefIds.push(tagRef.refId);
        }
        continue;
      }

      const currentTags = componentInstance.moduleTags[moduleKey] || [];

      for (const tagRef of existingTags) {
        // 如果现有的标签关系在当前选择中不存在，则标记为删除
        if (!currentTags.includes(tagRef.tagId)) {
          toDeleteRefIds.push(tagRef.refId);
        }
      }
    }
  }

  /**
   * 更新地市分类关系映射
   */
  async updateCityCategoryRefMap(componentInstance) {
    // 重新获取关联ID
    const cityCategories = await configRelationDataLoader.batchLoadCityCategories(componentInstance.selectedCities);

    for (const cityCode of componentInstance.selectedCities) {
      const categoryRefs = cityCategories[cityCode] || [];
      for (const ref of categoryRefs) {
        const key = `${cityCode}_${ref.categoryId}`;
        componentInstance.cityCategoryRefMap[key] = ref.refId;
      }
    }
  }

  /**
   * 更新地市分类模块关系映射
   */
  async updateCityCategoryModuleRefMap(componentInstance) {
    const cityCategoryRefIds = Object.values(componentInstance.cityCategoryRefMap);
    const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds);

    for (const refId of cityCategoryRefIds) {
      const moduleRefs = categoryModules[refId] || [];
      for (const moduleRef of moduleRefs) {
        const key = `${refId}_${moduleRef.moduleId}`;
        componentInstance.cityCategoryModuleRefMap[key] = moduleRef.refId;
      }
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.allRelationData = null;
  }
}

// 创建保存优化器实例
export const configRelationSaveOptimizer = new ConfigRelationSaveOptimizer();
