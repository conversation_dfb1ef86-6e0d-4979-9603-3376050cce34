# H5即时受理 - 批量接口测试说明

## 概述

本文档说明如何测试新实现的4个批量接口，这些接口是为了优化前端配置关系管理组件的性能而开发的。

## 新增的批量接口

### 1. 批量获取地市分类关系接口
- **接口路径**: `POST /api/hnzsxH5/hnzsxh5-city-category-ref/batchGetCategoriesByCityCodes`
- **功能**: 一次性获取多个地市的分类关系数据
- **请求参数**:
```json
{
  "cityCodes": ["110000", "120000", "130000"]
}
```
- **响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "110000": [
      {
        "refId": 1,
        "categoryId": 1,
        "categoryName": "分类名称",
        "categoryCode": "分类编码"
      }
    ],
    "120000": [],
    "130000": [...]
  }
}
```

### 2. 批量获取模块关系接口
- **接口路径**: `POST /api/hnzsxH5/hnzsxh5-city-category-module-ref/batchGetModulesByCityCategoryRefIds`
- **功能**: 一次性获取多个地市分类关系的模块信息
- **请求参数**:
```json
{
  "cityCategoryRefIds": [1, 2, 3]
}
```
- **响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "1": [
      {
        "refId": 10,
        "moduleId": 5,
        "moduleName": "模块名称",
        "moduleCode": "模块编码"
      }
    ],
    "2": [],
    "3": [...]
  }
}
```

### 3. 批量获取属性类型关系接口
- **接口路径**: `POST /api/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchGetAttributeTypesByModuleRefIds`
- **功能**: 一次性获取多个模块关系的属性类型信息
- **请求参数**:
```json
{
  "moduleRefIds": [10, 11, 12]
}
```
- **响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "10": [
      {
        "refId": 100,
        "attributeTypeId": 20,
        "attributeTypeName": "属性类型名称",
        "attributeTypeCode": "属性类型编码",
        "sort": 1
      }
    ],
    "11": [],
    "12": [...]
  }
}
```

### 4. 批量获取标签关系接口
- **接口路径**: `POST /api/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchGetTagsByModuleRefIds`
- **功能**: 一次性获取多个模块关系的标签信息
- **请求参数**:
```json
{
  "moduleRefIds": [10, 11, 12]
}
```
- **响应格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "10": [
      {
        "refId": 200,
        "tagId": 30,
        "tagName": "标签名称",
        "sort": 1
      }
    ],
    "11": [],
    "12": [...]
  }
}
```

## 测试步骤

### 1. 准备测试数据
确保数据库中有以下测试数据：
- 地市数据（hnzsxh5_city表）
- 分类数据（hnzsxh5_category表）
- 模块数据（hnzsxh5_module表）
- 属性类型数据（hnzsxh5_goods_attribute_type表）
- 标签数据（hnzsxh5_goods_tag表）
- 相应的关系表数据

### 2. 使用Postman测试
1. 导入接口到Postman
2. 设置正确的请求头（Content-Type: application/json）
3. 添加认证信息（如果需要）
4. 按照上述请求格式发送测试请求

### 3. 前端集成测试
1. 启动前端项目
2. 进入配置关系管理页面
3. 观察浏览器开发者工具的Network面板
4. 验证是否调用了批量接口而不是单个接口
5. 检查数据加载速度是否有明显提升

### 4. 性能对比测试
- **优化前**: 约200+次API调用
- **优化后**: 约3-5次批量API调用
- **预期性能提升**: 90%以上的请求时间减少

## 错误处理

所有批量接口都包含错误处理：
- 参数验证：检查必需参数是否为空
- 异常捕获：数据库查询异常会返回友好的错误信息
- 降级处理：前端会在批量接口失败时自动降级为单个接口调用

## 注意事项

1. **数据一致性**: 批量接口返回的数据格式与单个接口保持一致
2. **权限控制**: 批量接口继承了原有的权限控制机制
3. **日志记录**: 所有批量操作都会记录操作日志
4. **缓存策略**: 前端优化工具类实现了缓存机制，避免重复请求

## 验证清单

- [ ] 所有4个批量接口都能正常响应
- [ ] 返回数据格式正确
- [ ] 空数据情况处理正确
- [ ] 错误情况处理正确
- [ ] 前端能正常调用批量接口
- [ ] 性能有明显提升
- [ ] 降级机制工作正常
