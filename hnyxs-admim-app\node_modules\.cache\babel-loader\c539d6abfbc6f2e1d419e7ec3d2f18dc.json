{"ast": null, "code": "import \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/es.array.sort.js\";\n\n/**\n * 关联配置管理优化工具类\n * 解决循环调用接口和保存速度慢的问题\n * <AUTHOR> Assistant\n * @date 2025-01-01\n */\nimport { batchGetCategoriesByCityCodes, batchGetModulesByCityCategoryRefIds, batchGetAttributeTypesByModuleRefIds, batchGetTagsByModuleRefIds, batchSaveCityCategoryRef, batchSaveCityCategoryModuleRef, batchSaveModuleAttributeTypeRef, batchSaveModuleTagRef, getCategoriesByCityCode, getModulesByCityCategoryRefId, getAttributeTypesByModuleRefId, getTagsByModuleRefId } from '@/api/hnzsxH5/configRelation';\n/**\n * 关联配置数据加载器\n */\n\nexport class ConfigRelationDataLoader {\n  constructor() {\n    this.cache = new Map(); // 数据缓存\n\n    this.loadingPromises = new Map(); // 防止重复加载\n  }\n  /**\n   * 批量加载地市分类关系数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 地市分类关系数据\n   */\n\n\n  async batchLoadCityCategories(cityCodes) {\n    const cacheKey = `city_categories_${cityCodes.sort().join(',')}`; // 检查缓存\n\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    } // 检查是否正在加载\n\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    } // 开始加载\n\n\n    const loadingPromise = this._loadCityCategoriesData(cityCodes);\n\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n  /**\n   * 内部方法：加载地市分类数据\n   */\n\n\n  async _loadCityCategoriesData(cityCodes) {\n    try {\n      // 尝试使用批量接口\n      const batchResult = await batchGetCategoriesByCityCodes(cityCodes);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error); // 降级为单个接口调用\n\n      const result = {};\n      const promises = cityCodes.map(async cityCode => {\n        const categoryRefs = await getCategoriesByCityCode(cityCode);\n        result[cityCode] = categoryRefs;\n      });\n      await Promise.all(promises);\n      return result;\n    }\n  }\n  /**\n   * 批量加载模块关系数据\n   * @param {number[]} cityCategoryRefIds 地市分类关系ID数组\n   * @returns {Promise<Object>} 模块关系数据\n   */\n\n\n  async batchLoadCategoryModules(cityCategoryRefIds) {\n    const cacheKey = `category_modules_${cityCategoryRefIds.sort().join(',')}`;\n\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadCategoryModulesData(cityCategoryRefIds);\n\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n  /**\n   * 内部方法：加载模块数据\n   */\n\n\n  async _loadCategoryModulesData(cityCategoryRefIds) {\n    try {\n      const batchResult = await batchGetModulesByCityCategoryRefIds(cityCategoryRefIds);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      const result = {};\n      const promises = cityCategoryRefIds.map(async refId => {\n        const moduleRefs = await getModulesByCityCategoryRefId(refId);\n        result[refId] = moduleRefs;\n      });\n      await Promise.all(promises);\n      return result;\n    }\n  }\n  /**\n   * 批量加载模块属性和标签数据\n   * @param {number[]} moduleRefIds 模块关系ID数组\n   * @returns {Promise<{attributes: Object, tags: Object}>} 属性和标签数据\n   */\n\n\n  async batchLoadModuleAttributesAndTags(moduleRefIds) {\n    const cacheKey = `module_attrs_tags_${moduleRefIds.sort().join(',')}`;\n\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadModuleAttributesAndTagsData(moduleRefIds);\n\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n  /**\n   * 内部方法：加载模块属性和标签数据\n   */\n\n\n  async _loadModuleAttributesAndTagsData(moduleRefIds) {\n    try {\n      // 并行调用批量接口\n      const [attributesResult, tagsResult] = await Promise.all([batchGetAttributeTypesByModuleRefIds(moduleRefIds), batchGetTagsByModuleRefIds(moduleRefIds)]);\n      return {\n        attributes: attributesResult,\n        tags: tagsResult\n      };\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      const attributes = {};\n      const tags = {};\n      const promises = moduleRefIds.map(async refId => {\n        const [attrTypes, tagList] = await Promise.all([getAttributeTypesByModuleRefId(refId), getTagsByModuleRefId(refId)]);\n        attributes[refId] = attrTypes;\n        tags[refId] = tagList;\n      });\n      await Promise.all(promises);\n      return {\n        attributes,\n        tags\n      };\n    }\n  }\n  /**\n   * 一次性加载所有关联数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 完整的关联数据结构\n   */\n\n\n  async loadAllRelationData(cityCodes) {\n    console.log('开始批量加载关联数据，地市数量:', cityCodes.length);\n    const startTime = Date.now(); // 第一步：加载地市分类关系\n\n    const cityCategories = await this.batchLoadCityCategories(cityCodes);\n    console.log('地市分类关系加载完成，耗时:', Date.now() - startTime, 'ms'); // 收集所有地市分类关系ID\n\n    const cityCategoryRefIds = [];\n    const cityCategoryRefMap = {};\n\n    for (const cityCode of cityCodes) {\n      const categoryRefs = cityCategories[cityCode] || [];\n\n      for (const ref of categoryRefs) {\n        cityCategoryRefIds.push(ref.refId);\n        cityCategoryRefMap[`${cityCode}_${ref.categoryId}`] = ref.refId;\n      }\n    } // 第二步：批量加载模块关系\n\n\n    let categoryModules = {};\n\n    if (cityCategoryRefIds.length > 0) {\n      categoryModules = await this.batchLoadCategoryModules(cityCategoryRefIds);\n      console.log('模块关系加载完成，耗时:', Date.now() - startTime, 'ms');\n    } // 收集所有模块关系ID\n\n\n    const moduleRefIds = [];\n    const cityCategoryModuleRefMap = {};\n\n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n\n      for (const moduleRef of moduleRefs) {\n        moduleRefIds.push(moduleRef.refId);\n        cityCategoryModuleRefMap[`${refId}_${moduleRef.moduleId}`] = moduleRef.refId;\n      }\n    } // 第三步：批量加载属性和标签\n\n\n    let moduleAttributesAndTags = {\n      attributes: {},\n      tags: {}\n    };\n\n    if (moduleRefIds.length > 0) {\n      moduleAttributesAndTags = await this.batchLoadModuleAttributesAndTags(moduleRefIds);\n      console.log('属性标签加载完成，总耗时:', Date.now() - startTime, 'ms');\n    }\n\n    return {\n      cityCategories,\n      categoryModules,\n      moduleAttributesAndTags,\n      cityCategoryRefMap,\n      cityCategoryModuleRefMap\n    };\n  }\n  /**\n   * 清除缓存\n   */\n\n\n  clearCache() {\n    this.cache.clear();\n    this.loadingPromises.clear();\n  }\n  /**\n   * 获取缓存统计信息\n   */\n\n\n  getCacheStats() {\n    return {\n      cacheSize: this.cache.size,\n      loadingCount: this.loadingPromises.size\n    };\n  }\n\n} // 创建单例实例\n\nexport const configRelationDataLoader = new ConfigRelationDataLoader();\n/**\n * 关联配置保存优化器\n */\n\nexport class ConfigRelationSaveOptimizer {\n  constructor() {\n    this.allRelationData = null; // 缓存的完整关联数据\n  }\n  /**\n   * 优化的保存配置流程\n   * @param {Object} componentInstance 组件实例\n   * @returns {Promise<void>}\n   */\n\n\n  async optimizedSaveConfiguration(componentInstance) {\n    const startTime = Date.now();\n    console.log('开始优化的保存配置流程...');\n\n    try {\n      // 1. 一次性加载所有现有关联数据（如果还没有加载）\n      if (!this.allRelationData) {\n        await this.loadAllExistingRelations(componentInstance);\n      } // 2. 并行执行所有保存操作\n\n\n      await Promise.all([this.optimizedSaveCityCategoryRelations(componentInstance), this.optimizedSaveCityCategoryModuleRelations(componentInstance), this.optimizedSaveModuleAttributeTagRelations(componentInstance)]); // 3. 最后执行清理操作\n\n      await this.optimizedDeleteUnusedRelations(componentInstance);\n      console.log(`优化的保存配置完成，总耗时: ${Date.now() - startTime}ms`);\n    } catch (error) {\n      console.error('优化的保存配置失败:', error);\n      throw error;\n    }\n  }\n  /**\n   * 一次性加载所有现有关联数据\n   */\n\n\n  async loadAllExistingRelations(componentInstance) {\n    console.log('开始加载所有现有关联数据...');\n    const startTime = Date.now();\n    this.allRelationData = await configRelationDataLoader.loadAllRelationData(componentInstance.selectedCities);\n    console.log(`所有现有关联数据加载完成，耗时: ${Date.now() - startTime}ms`);\n  }\n  /**\n   * 优化的地市-分类关系保存\n   */\n\n\n  async optimizedSaveCityCategoryRelations(componentInstance) {\n    const cityCategoryRefs = [];\n    const existingRelations = this.allRelationData.cityCategories;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categories = componentInstance.cityCategories[cityCode] || [];\n\n      for (const categoryId of categories) {\n        const key = `${cityCode}_${categoryId}`; // 检查是否已存在关联关系\n\n        const existingRefs = existingRelations[cityCode] || [];\n        const exists = existingRefs.some(ref => ref.categoryId === categoryId);\n\n        if (!exists) {\n          cityCategoryRefs.push({\n            cityCode: cityCode,\n            categoryId: categoryId,\n            status: 1,\n            sort: 1\n          });\n        }\n      }\n    }\n\n    if (cityCategoryRefs.length > 0) {\n      console.log(`批量保存${cityCategoryRefs.length}个地市-模块分类关系`);\n      await batchSaveCityCategoryRef(cityCategoryRefs); // 更新组件的映射关系\n\n      await this.updateCityCategoryRefMap(componentInstance);\n    }\n  }\n  /**\n   * 优化的地市分类-模块关系保存\n   */\n\n\n  async optimizedSaveCityCategoryModuleRelations(componentInstance) {\n    const cityCategoryModuleRefs = [];\n    const existingModuleRelations = this.allRelationData.categoryModules;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n        if (!refId) continue;\n        const modules = componentInstance.cityModules[cityCategoryKey] || [];\n        const existingModuleRefs = existingModuleRelations[refId] || [];\n\n        for (const moduleId of modules) {\n          const exists = existingModuleRefs.some(ref => ref.moduleId === moduleId);\n\n          if (!exists) {\n            cityCategoryModuleRefs.push({\n              cityCategoryRefId: refId,\n              moduleId: moduleId,\n              status: 1,\n              isOneBeat: 1\n            });\n          }\n        }\n      }\n    }\n\n    if (cityCategoryModuleRefs.length > 0) {\n      console.log(`批量保存${cityCategoryModuleRefs.length}个地市分类-模块关系`);\n      await componentInstance.batchSaveCityCategoryModuleRef(cityCategoryModuleRefs); // 更新组件的映射关系\n\n      await this.updateCityCategoryModuleRefMap(componentInstance);\n    }\n  }\n  /**\n   * 优化的模块-属性标签关系保存\n   */\n\n\n  async optimizedSaveModuleAttributeTagRelations(componentInstance) {\n    const moduleAttributeTypeRefs = [];\n    const moduleTagRefs = [];\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n        if (!cityCategoryRefId) continue;\n\n        for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n          const key = `${cityCode}-${categoryId}-${moduleId}`;\n          const moduleRefId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n          if (!moduleRefId) continue; // 处理属性类别\n\n          const selectedAttributes = componentInstance.moduleAttributes[key] || [];\n          const existingAttributes = existingAttributesAndTags.attributes[moduleRefId] || [];\n          const existingAttributeIds = existingAttributes.map(item => item.attributeTypeId);\n\n          for (const attributeTypeId of selectedAttributes) {\n            if (!existingAttributeIds.includes(attributeTypeId)) {\n              moduleAttributeTypeRefs.push({\n                cityCategoryModuleRefId: moduleRefId,\n                goodsAttributeTypeId: attributeTypeId,\n                status: 1,\n                sort: 1\n              });\n            }\n          } // 处理标签\n\n\n          const selectedTags = componentInstance.moduleTags[key] || [];\n          const existingTags = existingAttributesAndTags.tags[moduleRefId] || [];\n          const existingTagIds = existingTags.map(item => item.tagId);\n\n          for (const tagId of selectedTags) {\n            if (!existingTagIds.includes(tagId)) {\n              moduleTagRefs.push({\n                goodsTagId: tagId,\n                cityCategoryModuleRefId: moduleRefId,\n                status: 1,\n                sort: 1\n              });\n            }\n          }\n        }\n      }\n    } // 并行保存属性和标签关系\n\n\n    const savePromises = [];\n\n    if (moduleAttributeTypeRefs.length > 0) {\n      console.log(`批量保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);\n      savePromises.push(batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs));\n    }\n\n    if (moduleTagRefs.length > 0) {\n      console.log(`批量保存${moduleTagRefs.length}个模块-商品标签关系`);\n      savePromises.push(batchSaveModuleTagRef(moduleTagRefs));\n    }\n\n    await Promise.all(savePromises);\n  }\n  /**\n   * 优化的删除无用关系\n   */\n\n\n  async optimizedDeleteUnusedRelations(componentInstance) {\n    // 基于已加载的数据进行删除操作，避免重复查询\n    const toDeleteCategoryRefIds = [];\n    const toDeleteModuleRefIds = [];\n    const toDeleteAttrRefIds = [];\n    const toDeleteTagRefIds = []; // 使用缓存的数据进行比较和删除逻辑\n    // ... 删除逻辑实现（基于已有的allRelationData）\n    // 并行执行删除操作\n\n    const deletePromises = [];\n\n    if (toDeleteCategoryRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteCityCategoryRef(toDeleteCategoryRefIds));\n    }\n\n    if (toDeleteModuleRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds));\n    }\n\n    if (toDeleteAttrRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds));\n    }\n\n    if (toDeleteTagRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteModuleTagRef(toDeleteTagRefIds));\n    }\n\n    await Promise.all(deletePromises);\n  }\n  /**\n   * 更新地市分类关系映射\n   */\n\n\n  async updateCityCategoryRefMap(componentInstance) {\n    // 重新获取关联ID\n    const cityCategories = await configRelationDataLoader.batchLoadCityCategories(componentInstance.selectedCities);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categoryRefs = cityCategories[cityCode] || [];\n\n      for (const ref of categoryRefs) {\n        const key = `${cityCode}_${ref.categoryId}`;\n        componentInstance.cityCategoryRefMap[key] = ref.refId;\n      }\n    }\n  }\n  /**\n   * 更新地市分类模块关系映射\n   */\n\n\n  async updateCityCategoryModuleRefMap(componentInstance) {\n    const cityCategoryRefIds = Object.values(componentInstance.cityCategoryRefMap);\n    const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds);\n\n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n\n      for (const moduleRef of moduleRefs) {\n        const key = `${refId}_${moduleRef.moduleId}`;\n        componentInstance.cityCategoryModuleRefMap[key] = moduleRef.refId;\n      }\n    }\n  }\n  /**\n   * 清除缓存\n   */\n\n\n  clearCache() {\n    this.allRelationData = null;\n  }\n\n} // 创建保存优化器实例\n\nexport const configRelationSaveOptimizer = new ConfigRelationSaveOptimizer();", "map": {"version": 3, "names": ["batchGetCategoriesByCityCodes", "batchGetModulesByCityCategoryRefIds", "batchGetAttributeTypesByModuleRefIds", "batchGetTagsByModuleRefIds", "batchSaveCityCategoryRef", "batchSaveCityCategoryModuleRef", "batchSaveModuleAttributeTypeRef", "batchSaveModuleTagRef", "getCategoriesByCityCode", "getModulesByCityCategoryRefId", "getAttributeTypesByModuleRefId", "getTagsByModuleRefId", "ConfigRelationDataLoader", "constructor", "cache", "Map", "loadingPromises", "batchLoadCityCategories", "cityCodes", "cache<PERSON>ey", "sort", "join", "has", "get", "loadingPromise", "_loadCityCategoriesData", "set", "result", "delete", "batchResult", "error", "console", "warn", "promises", "map", "cityCode", "categoryRefs", "Promise", "all", "batchLoadCategoryModules", "cityCategoryRefIds", "_loadCategoryModulesData", "refId", "moduleRefs", "batchLoadModuleAttributesAndTags", "moduleRefIds", "_loadModuleAttributesAndTagsData", "attributesResult", "tagsResult", "attributes", "tags", "attrTypes", "tagList", "loadAllRelationData", "log", "length", "startTime", "Date", "now", "cityCategories", "cityCategoryRefMap", "ref", "push", "categoryId", "categoryModules", "cityCategoryModuleRefMap", "moduleRef", "moduleId", "moduleAttributesAndTags", "clearCache", "clear", "getCacheStats", "cacheSize", "size", "loadingCount", "configRelationDataLoader", "ConfigRelationSaveOptimizer", "allRelationData", "optimizedSaveConfiguration", "componentInstance", "loadAllExistingRelations", "optimizedSaveCityCategoryRelations", "optimizedSaveCityCategoryModuleRelations", "optimizedSaveModuleAttributeTagRelations", "optimizedDeleteUnusedRelations", "selectedCities", "cityCategoryRefs", "existingRelations", "categories", "key", "existingRefs", "exists", "some", "status", "updateCityCategoryRefMap", "cityCategoryModuleRefs", "existingModuleRelations", "cityCategoryKey", "modules", "cityModules", "existingModuleRefs", "cityCategoryRefId", "isOneBeat", "updateCityCategoryModuleRefMap", "moduleAttributeTypeRefs", "moduleTagRefs", "existingAttributesAndTags", "moduleRefId", "selectedAttributes", "moduleAttributes", "existingAttributes", "existingAttributeIds", "item", "attributeTypeId", "includes", "cityCategoryModuleRefId", "goodsAttributeTypeId", "selectedTags", "moduleTags", "existingTags", "existingTagIds", "tagId", "goodsTagId", "savePromises", "toDeleteCategoryRefIds", "toDeleteModuleRefIds", "toDeleteAttrRefIds", "toDeleteTagRefIds", "deletePromises", "batchDeleteCityCategoryRef", "batchDeleteCityCategoryModuleRef", "batchDeleteModuleAttributeTypeRef", "batchDeleteModuleTagRef", "Object", "values", "configRelationSaveOptimizer"], "sources": ["D:/code/dianxinCode/新版省集约项目/hnyxs-admim-app/src/utils/configRelationOptimizer.js"], "sourcesContent": ["/**\n * 关联配置管理优化工具类\n * 解决循环调用接口和保存速度慢的问题\n * <AUTHOR> Assistant\n * @date 2025-01-01\n */\n\nimport {\n  batchGetCategoriesByCityCodes,\n  batchGetModulesByCityCategoryRefIds,\n  batchGetAttributeTypesByModuleRefIds,\n  batchGetTagsByModuleRefIds,\n  batchSaveCityCategoryRef,\n  batchSaveCityCategoryModuleRef,\n  batchSaveModuleAttributeTypeRef,\n  batchSaveModuleTagRef,\n  getCategoriesByCityCode,\n  getModulesByCityCategoryRefId,\n  getAttributeTypesByModuleRefId,\n  getTagsByModuleRefId\n} from '@/api/hnzsxH5/configRelation';\n\n/**\n * 关联配置数据加载器\n */\nexport class ConfigRelationDataLoader {\n  constructor() {\n    this.cache = new Map(); // 数据缓存\n    this.loadingPromises = new Map(); // 防止重复加载\n  }\n\n  /**\n   * 批量加载地市分类关系数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 地市分类关系数据\n   */\n  async batchLoadCityCategories(cityCodes) {\n    const cacheKey = `city_categories_${cityCodes.sort().join(',')}`;\n    \n    // 检查缓存\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    // 检查是否正在加载\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    // 开始加载\n    const loadingPromise = this._loadCityCategoriesData(cityCodes);\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n\n  /**\n   * 内部方法：加载地市分类数据\n   */\n  async _loadCityCategoriesData(cityCodes) {\n    try {\n      // 尝试使用批量接口\n      const batchResult = await batchGetCategoriesByCityCodes(cityCodes);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      \n      // 降级为单个接口调用\n      const result = {};\n      const promises = cityCodes.map(async cityCode => {\n        const categoryRefs = await getCategoriesByCityCode(cityCode);\n        result[cityCode] = categoryRefs;\n      });\n      \n      await Promise.all(promises);\n      return result;\n    }\n  }\n\n  /**\n   * 批量加载模块关系数据\n   * @param {number[]} cityCategoryRefIds 地市分类关系ID数组\n   * @returns {Promise<Object>} 模块关系数据\n   */\n  async batchLoadCategoryModules(cityCategoryRefIds) {\n    const cacheKey = `category_modules_${cityCategoryRefIds.sort().join(',')}`;\n    \n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadCategoryModulesData(cityCategoryRefIds);\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n\n  /**\n   * 内部方法：加载模块数据\n   */\n  async _loadCategoryModulesData(cityCategoryRefIds) {\n    try {\n      const batchResult = await batchGetModulesByCityCategoryRefIds(cityCategoryRefIds);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      \n      const result = {};\n      const promises = cityCategoryRefIds.map(async refId => {\n        const moduleRefs = await getModulesByCityCategoryRefId(refId);\n        result[refId] = moduleRefs;\n      });\n      \n      await Promise.all(promises);\n      return result;\n    }\n  }\n\n  /**\n   * 批量加载模块属性和标签数据\n   * @param {number[]} moduleRefIds 模块关系ID数组\n   * @returns {Promise<{attributes: Object, tags: Object}>} 属性和标签数据\n   */\n  async batchLoadModuleAttributesAndTags(moduleRefIds) {\n    const cacheKey = `module_attrs_tags_${moduleRefIds.sort().join(',')}`;\n    \n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadModuleAttributesAndTagsData(moduleRefIds);\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n\n  /**\n   * 内部方法：加载模块属性和标签数据\n   */\n  async _loadModuleAttributesAndTagsData(moduleRefIds) {\n    try {\n      // 并行调用批量接口\n      const [attributesResult, tagsResult] = await Promise.all([\n        batchGetAttributeTypesByModuleRefIds(moduleRefIds),\n        batchGetTagsByModuleRefIds(moduleRefIds)\n      ]);\n      \n      return {\n        attributes: attributesResult,\n        tags: tagsResult\n      };\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      \n      const attributes = {};\n      const tags = {};\n      \n      const promises = moduleRefIds.map(async refId => {\n        const [attrTypes, tagList] = await Promise.all([\n          getAttributeTypesByModuleRefId(refId),\n          getTagsByModuleRefId(refId)\n        ]);\n        attributes[refId] = attrTypes;\n        tags[refId] = tagList;\n      });\n      \n      await Promise.all(promises);\n      return { attributes, tags };\n    }\n  }\n\n  /**\n   * 一次性加载所有关联数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 完整的关联数据结构\n   */\n  async loadAllRelationData(cityCodes) {\n    console.log('开始批量加载关联数据，地市数量:', cityCodes.length);\n    const startTime = Date.now();\n\n    // 第一步：加载地市分类关系\n    const cityCategories = await this.batchLoadCityCategories(cityCodes);\n    console.log('地市分类关系加载完成，耗时:', Date.now() - startTime, 'ms');\n\n    // 收集所有地市分类关系ID\n    const cityCategoryRefIds = [];\n    const cityCategoryRefMap = {};\n    \n    for (const cityCode of cityCodes) {\n      const categoryRefs = cityCategories[cityCode] || [];\n      for (const ref of categoryRefs) {\n        cityCategoryRefIds.push(ref.refId);\n        cityCategoryRefMap[`${cityCode}_${ref.categoryId}`] = ref.refId;\n      }\n    }\n\n    // 第二步：批量加载模块关系\n    let categoryModules = {};\n    if (cityCategoryRefIds.length > 0) {\n      categoryModules = await this.batchLoadCategoryModules(cityCategoryRefIds);\n      console.log('模块关系加载完成，耗时:', Date.now() - startTime, 'ms');\n    }\n\n    // 收集所有模块关系ID\n    const moduleRefIds = [];\n    const cityCategoryModuleRefMap = {};\n    \n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n      for (const moduleRef of moduleRefs) {\n        moduleRefIds.push(moduleRef.refId);\n        cityCategoryModuleRefMap[`${refId}_${moduleRef.moduleId}`] = moduleRef.refId;\n      }\n    }\n\n    // 第三步：批量加载属性和标签\n    let moduleAttributesAndTags = { attributes: {}, tags: {} };\n    if (moduleRefIds.length > 0) {\n      moduleAttributesAndTags = await this.batchLoadModuleAttributesAndTags(moduleRefIds);\n      console.log('属性标签加载完成，总耗时:', Date.now() - startTime, 'ms');\n    }\n\n    return {\n      cityCategories,\n      categoryModules,\n      moduleAttributesAndTags,\n      cityCategoryRefMap,\n      cityCategoryModuleRefMap\n    };\n  }\n\n  /**\n   * 清除缓存\n   */\n  clearCache() {\n    this.cache.clear();\n    this.loadingPromises.clear();\n  }\n\n  /**\n   * 获取缓存统计信息\n   */\n  getCacheStats() {\n    return {\n      cacheSize: this.cache.size,\n      loadingCount: this.loadingPromises.size\n    };\n  }\n}\n\n// 创建单例实例\nexport const configRelationDataLoader = new ConfigRelationDataLoader();\n\n/**\n * 关联配置保存优化器\n */\nexport class ConfigRelationSaveOptimizer {\n  constructor() {\n    this.allRelationData = null; // 缓存的完整关联数据\n  }\n\n  /**\n   * 优化的保存配置流程\n   * @param {Object} componentInstance 组件实例\n   * @returns {Promise<void>}\n   */\n  async optimizedSaveConfiguration(componentInstance) {\n    const startTime = Date.now();\n    console.log('开始优化的保存配置流程...');\n\n    try {\n      // 1. 一次性加载所有现有关联数据（如果还没有加载）\n      if (!this.allRelationData) {\n        await this.loadAllExistingRelations(componentInstance);\n      }\n\n      // 2. 并行执行所有保存操作\n      await Promise.all([\n        this.optimizedSaveCityCategoryRelations(componentInstance),\n        this.optimizedSaveCityCategoryModuleRelations(componentInstance),\n        this.optimizedSaveModuleAttributeTagRelations(componentInstance)\n      ]);\n\n      // 3. 最后执行清理操作\n      await this.optimizedDeleteUnusedRelations(componentInstance);\n\n      console.log(`优化的保存配置完成，总耗时: ${Date.now() - startTime}ms`);\n    } catch (error) {\n      console.error('优化的保存配置失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 一次性加载所有现有关联数据\n   */\n  async loadAllExistingRelations(componentInstance) {\n    console.log('开始加载所有现有关联数据...');\n    const startTime = Date.now();\n\n    this.allRelationData = await configRelationDataLoader.loadAllRelationData(componentInstance.selectedCities);\n\n    console.log(`所有现有关联数据加载完成，耗时: ${Date.now() - startTime}ms`);\n  }\n\n  /**\n   * 优化的地市-分类关系保存\n   */\n  async optimizedSaveCityCategoryRelations(componentInstance) {\n    const cityCategoryRefs = [];\n    const existingRelations = this.allRelationData.cityCategories;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categories = componentInstance.cityCategories[cityCode] || [];\n\n      for (const categoryId of categories) {\n        const key = `${cityCode}_${categoryId}`;\n\n        // 检查是否已存在关联关系\n        const existingRefs = existingRelations[cityCode] || [];\n        const exists = existingRefs.some(ref => ref.categoryId === categoryId);\n\n        if (!exists) {\n          cityCategoryRefs.push({\n            cityCode: cityCode,\n            categoryId: categoryId,\n            status: 1,\n            sort: 1\n          });\n        }\n      }\n    }\n\n    if (cityCategoryRefs.length > 0) {\n      console.log(`批量保存${cityCategoryRefs.length}个地市-模块分类关系`);\n      await batchSaveCityCategoryRef(cityCategoryRefs);\n\n      // 更新组件的映射关系\n      await this.updateCityCategoryRefMap(componentInstance);\n    }\n  }\n\n  /**\n   * 优化的地市分类-模块关系保存\n   */\n  async optimizedSaveCityCategoryModuleRelations(componentInstance) {\n    const cityCategoryModuleRefs = [];\n    const existingModuleRelations = this.allRelationData.categoryModules;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (!refId) continue;\n\n        const modules = componentInstance.cityModules[cityCategoryKey] || [];\n        const existingModuleRefs = existingModuleRelations[refId] || [];\n\n        for (const moduleId of modules) {\n          const exists = existingModuleRefs.some(ref => ref.moduleId === moduleId);\n\n          if (!exists) {\n            cityCategoryModuleRefs.push({\n              cityCategoryRefId: refId,\n              moduleId: moduleId,\n              status: 1,\n              isOneBeat: 1\n            });\n          }\n        }\n      }\n    }\n\n    if (cityCategoryModuleRefs.length > 0) {\n      console.log(`批量保存${cityCategoryModuleRefs.length}个地市分类-模块关系`);\n      await componentInstance.batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);\n\n      // 更新组件的映射关系\n      await this.updateCityCategoryModuleRefMap(componentInstance);\n    }\n  }\n\n  /**\n   * 优化的模块-属性标签关系保存\n   */\n  async optimizedSaveModuleAttributeTagRelations(componentInstance) {\n    const moduleAttributeTypeRefs = [];\n    const moduleTagRefs = [];\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (!cityCategoryRefId) continue;\n\n        for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n          const key = `${cityCode}-${categoryId}-${moduleId}`;\n          const moduleRefId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n          if (!moduleRefId) continue;\n\n          // 处理属性类别\n          const selectedAttributes = componentInstance.moduleAttributes[key] || [];\n          const existingAttributes = existingAttributesAndTags.attributes[moduleRefId] || [];\n          const existingAttributeIds = existingAttributes.map(item => item.attributeTypeId);\n\n          for (const attributeTypeId of selectedAttributes) {\n            if (!existingAttributeIds.includes(attributeTypeId)) {\n              moduleAttributeTypeRefs.push({\n                cityCategoryModuleRefId: moduleRefId,\n                goodsAttributeTypeId: attributeTypeId,\n                status: 1,\n                sort: 1\n              });\n            }\n          }\n\n          // 处理标签\n          const selectedTags = componentInstance.moduleTags[key] || [];\n          const existingTags = existingAttributesAndTags.tags[moduleRefId] || [];\n          const existingTagIds = existingTags.map(item => item.tagId);\n\n          for (const tagId of selectedTags) {\n            if (!existingTagIds.includes(tagId)) {\n              moduleTagRefs.push({\n                goodsTagId: tagId,\n                cityCategoryModuleRefId: moduleRefId,\n                status: 1,\n                sort: 1\n              });\n            }\n          }\n        }\n      }\n    }\n\n    // 并行保存属性和标签关系\n    const savePromises = [];\n\n    if (moduleAttributeTypeRefs.length > 0) {\n      console.log(`批量保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);\n      savePromises.push(batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs));\n    }\n\n    if (moduleTagRefs.length > 0) {\n      console.log(`批量保存${moduleTagRefs.length}个模块-商品标签关系`);\n      savePromises.push(batchSaveModuleTagRef(moduleTagRefs));\n    }\n\n    await Promise.all(savePromises);\n  }\n\n  /**\n   * 优化的删除无用关系\n   */\n  async optimizedDeleteUnusedRelations(componentInstance) {\n    // 基于已加载的数据进行删除操作，避免重复查询\n    const toDeleteCategoryRefIds = [];\n    const toDeleteModuleRefIds = [];\n    const toDeleteAttrRefIds = [];\n    const toDeleteTagRefIds = [];\n\n    // 使用缓存的数据进行比较和删除逻辑\n    // ... 删除逻辑实现（基于已有的allRelationData）\n\n    // 并行执行删除操作\n    const deletePromises = [];\n\n    if (toDeleteCategoryRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteCityCategoryRef(toDeleteCategoryRefIds));\n    }\n\n    if (toDeleteModuleRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds));\n    }\n\n    if (toDeleteAttrRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds));\n    }\n\n    if (toDeleteTagRefIds.length > 0) {\n      deletePromises.push(componentInstance.batchDeleteModuleTagRef(toDeleteTagRefIds));\n    }\n\n    await Promise.all(deletePromises);\n  }\n\n  /**\n   * 更新地市分类关系映射\n   */\n  async updateCityCategoryRefMap(componentInstance) {\n    // 重新获取关联ID\n    const cityCategories = await configRelationDataLoader.batchLoadCityCategories(componentInstance.selectedCities);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categoryRefs = cityCategories[cityCode] || [];\n      for (const ref of categoryRefs) {\n        const key = `${cityCode}_${ref.categoryId}`;\n        componentInstance.cityCategoryRefMap[key] = ref.refId;\n      }\n    }\n  }\n\n  /**\n   * 更新地市分类模块关系映射\n   */\n  async updateCityCategoryModuleRefMap(componentInstance) {\n    const cityCategoryRefIds = Object.values(componentInstance.cityCategoryRefMap);\n    const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds);\n\n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n      for (const moduleRef of moduleRefs) {\n        const key = `${refId}_${moduleRef.moduleId}`;\n        componentInstance.cityCategoryModuleRefMap[key] = moduleRef.refId;\n      }\n    }\n  }\n\n  /**\n   * 清除缓存\n   */\n  clearCache() {\n    this.allRelationData = null;\n  }\n}\n\n// 创建保存优化器实例\nexport const configRelationSaveOptimizer = new ConfigRelationSaveOptimizer();\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA,SACEA,6BADF,EAEEC,mCAFF,EAGEC,oCAHF,EAIEC,0BAJF,EAKEC,wBALF,EAMEC,8BANF,EAOEC,+BAPF,EAQEC,qBARF,EASEC,uBATF,EAUEC,6BAVF,EAWEC,8BAXF,EAYEC,oBAZF,QAaO,8BAbP;AAeA;AACA;AACA;;AACA,OAAO,MAAMC,wBAAN,CAA+B;EACpCC,WAAW,GAAG;IACZ,KAAKC,KAAL,GAAa,IAAIC,GAAJ,EAAb,CADY,CACY;;IACxB,KAAKC,eAAL,GAAuB,IAAID,GAAJ,EAAvB,CAFY,CAEsB;EACnC;EAED;AACF;AACA;AACA;AACA;;;EAC+B,MAAvBE,uBAAuB,CAACC,SAAD,EAAY;IACvC,MAAMC,QAAQ,GAAI,mBAAkBD,SAAS,CAACE,IAAV,GAAiBC,IAAjB,CAAsB,GAAtB,CAA2B,EAA/D,CADuC,CAGvC;;IACA,IAAI,KAAKP,KAAL,CAAWQ,GAAX,CAAeH,QAAf,CAAJ,EAA8B;MAC5B,OAAO,KAAKL,KAAL,CAAWS,GAAX,CAAeJ,QAAf,CAAP;IACD,CANsC,CAQvC;;;IACA,IAAI,KAAKH,eAAL,CAAqBM,GAArB,CAAyBH,QAAzB,CAAJ,EAAwC;MACtC,OAAO,KAAKH,eAAL,CAAqBO,GAArB,CAAyBJ,QAAzB,CAAP;IACD,CAXsC,CAavC;;;IACA,MAAMK,cAAc,GAAG,KAAKC,uBAAL,CAA6BP,SAA7B,CAAvB;;IACA,KAAKF,eAAL,CAAqBU,GAArB,CAAyBP,QAAzB,EAAmCK,cAAnC;;IAEA,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,cAArB;MACA,KAAKV,KAAL,CAAWY,GAAX,CAAeP,QAAf,EAAyBQ,MAAzB;MACA,OAAOA,MAAP;IACD,CAJD,SAIU;MACR,KAAKX,eAAL,CAAqBY,MAArB,CAA4BT,QAA5B;IACD;EACF;EAED;AACF;AACA;;;EAC+B,MAAvBM,uBAAuB,CAACP,SAAD,EAAY;IACvC,IAAI;MACF;MACA,MAAMW,WAAW,GAAG,MAAM7B,6BAA6B,CAACkB,SAAD,CAAvD;MACA,OAAOW,WAAP;IACD,CAJD,CAIE,OAAOC,KAAP,EAAc;MACdC,OAAO,CAACC,IAAR,CAAa,qBAAb,EAAoCF,KAApC,EADc,CAGd;;MACA,MAAMH,MAAM,GAAG,EAAf;MACA,MAAMM,QAAQ,GAAGf,SAAS,CAACgB,GAAV,CAAc,MAAMC,QAAN,IAAkB;QAC/C,MAAMC,YAAY,GAAG,MAAM5B,uBAAuB,CAAC2B,QAAD,CAAlD;QACAR,MAAM,CAACQ,QAAD,CAAN,GAAmBC,YAAnB;MACD,CAHgB,CAAjB;MAKA,MAAMC,OAAO,CAACC,GAAR,CAAYL,QAAZ,CAAN;MACA,OAAON,MAAP;IACD;EACF;EAED;AACF;AACA;AACA;AACA;;;EACgC,MAAxBY,wBAAwB,CAACC,kBAAD,EAAqB;IACjD,MAAMrB,QAAQ,GAAI,oBAAmBqB,kBAAkB,CAACpB,IAAnB,GAA0BC,IAA1B,CAA+B,GAA/B,CAAoC,EAAzE;;IAEA,IAAI,KAAKP,KAAL,CAAWQ,GAAX,CAAeH,QAAf,CAAJ,EAA8B;MAC5B,OAAO,KAAKL,KAAL,CAAWS,GAAX,CAAeJ,QAAf,CAAP;IACD;;IAED,IAAI,KAAKH,eAAL,CAAqBM,GAArB,CAAyBH,QAAzB,CAAJ,EAAwC;MACtC,OAAO,KAAKH,eAAL,CAAqBO,GAArB,CAAyBJ,QAAzB,CAAP;IACD;;IAED,MAAMK,cAAc,GAAG,KAAKiB,wBAAL,CAA8BD,kBAA9B,CAAvB;;IACA,KAAKxB,eAAL,CAAqBU,GAArB,CAAyBP,QAAzB,EAAmCK,cAAnC;;IAEA,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,cAArB;MACA,KAAKV,KAAL,CAAWY,GAAX,CAAeP,QAAf,EAAyBQ,MAAzB;MACA,OAAOA,MAAP;IACD,CAJD,SAIU;MACR,KAAKX,eAAL,CAAqBY,MAArB,CAA4BT,QAA5B;IACD;EACF;EAED;AACF;AACA;;;EACgC,MAAxBsB,wBAAwB,CAACD,kBAAD,EAAqB;IACjD,IAAI;MACF,MAAMX,WAAW,GAAG,MAAM5B,mCAAmC,CAACuC,kBAAD,CAA7D;MACA,OAAOX,WAAP;IACD,CAHD,CAGE,OAAOC,KAAP,EAAc;MACdC,OAAO,CAACC,IAAR,CAAa,qBAAb,EAAoCF,KAApC;MAEA,MAAMH,MAAM,GAAG,EAAf;MACA,MAAMM,QAAQ,GAAGO,kBAAkB,CAACN,GAAnB,CAAuB,MAAMQ,KAAN,IAAe;QACrD,MAAMC,UAAU,GAAG,MAAMlC,6BAA6B,CAACiC,KAAD,CAAtD;QACAf,MAAM,CAACe,KAAD,CAAN,GAAgBC,UAAhB;MACD,CAHgB,CAAjB;MAKA,MAAMN,OAAO,CAACC,GAAR,CAAYL,QAAZ,CAAN;MACA,OAAON,MAAP;IACD;EACF;EAED;AACF;AACA;AACA;AACA;;;EACwC,MAAhCiB,gCAAgC,CAACC,YAAD,EAAe;IACnD,MAAM1B,QAAQ,GAAI,qBAAoB0B,YAAY,CAACzB,IAAb,GAAoBC,IAApB,CAAyB,GAAzB,CAA8B,EAApE;;IAEA,IAAI,KAAKP,KAAL,CAAWQ,GAAX,CAAeH,QAAf,CAAJ,EAA8B;MAC5B,OAAO,KAAKL,KAAL,CAAWS,GAAX,CAAeJ,QAAf,CAAP;IACD;;IAED,IAAI,KAAKH,eAAL,CAAqBM,GAArB,CAAyBH,QAAzB,CAAJ,EAAwC;MACtC,OAAO,KAAKH,eAAL,CAAqBO,GAArB,CAAyBJ,QAAzB,CAAP;IACD;;IAED,MAAMK,cAAc,GAAG,KAAKsB,gCAAL,CAAsCD,YAAtC,CAAvB;;IACA,KAAK7B,eAAL,CAAqBU,GAArB,CAAyBP,QAAzB,EAAmCK,cAAnC;;IAEA,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,cAArB;MACA,KAAKV,KAAL,CAAWY,GAAX,CAAeP,QAAf,EAAyBQ,MAAzB;MACA,OAAOA,MAAP;IACD,CAJD,SAIU;MACR,KAAKX,eAAL,CAAqBY,MAArB,CAA4BT,QAA5B;IACD;EACF;EAED;AACF;AACA;;;EACwC,MAAhC2B,gCAAgC,CAACD,YAAD,EAAe;IACnD,IAAI;MACF;MACA,MAAM,CAACE,gBAAD,EAAmBC,UAAnB,IAAiC,MAAMX,OAAO,CAACC,GAAR,CAAY,CACvDpC,oCAAoC,CAAC2C,YAAD,CADmB,EAEvD1C,0BAA0B,CAAC0C,YAAD,CAF6B,CAAZ,CAA7C;MAKA,OAAO;QACLI,UAAU,EAAEF,gBADP;QAELG,IAAI,EAAEF;MAFD,CAAP;IAID,CAXD,CAWE,OAAOlB,KAAP,EAAc;MACdC,OAAO,CAACC,IAAR,CAAa,qBAAb,EAAoCF,KAApC;MAEA,MAAMmB,UAAU,GAAG,EAAnB;MACA,MAAMC,IAAI,GAAG,EAAb;MAEA,MAAMjB,QAAQ,GAAGY,YAAY,CAACX,GAAb,CAAiB,MAAMQ,KAAN,IAAe;QAC/C,MAAM,CAACS,SAAD,EAAYC,OAAZ,IAAuB,MAAMf,OAAO,CAACC,GAAR,CAAY,CAC7C5B,8BAA8B,CAACgC,KAAD,CADe,EAE7C/B,oBAAoB,CAAC+B,KAAD,CAFyB,CAAZ,CAAnC;QAIAO,UAAU,CAACP,KAAD,CAAV,GAAoBS,SAApB;QACAD,IAAI,CAACR,KAAD,CAAJ,GAAcU,OAAd;MACD,CAPgB,CAAjB;MASA,MAAMf,OAAO,CAACC,GAAR,CAAYL,QAAZ,CAAN;MACA,OAAO;QAAEgB,UAAF;QAAcC;MAAd,CAAP;IACD;EACF;EAED;AACF;AACA;AACA;AACA;;;EAC2B,MAAnBG,mBAAmB,CAACnC,SAAD,EAAY;IACnCa,OAAO,CAACuB,GAAR,CAAY,kBAAZ,EAAgCpC,SAAS,CAACqC,MAA1C;IACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB,CAFmC,CAInC;;IACA,MAAMC,cAAc,GAAG,MAAM,KAAK1C,uBAAL,CAA6BC,SAA7B,CAA7B;IACAa,OAAO,CAACuB,GAAR,CAAY,gBAAZ,EAA8BG,IAAI,CAACC,GAAL,KAAaF,SAA3C,EAAsD,IAAtD,EANmC,CAQnC;;IACA,MAAMhB,kBAAkB,GAAG,EAA3B;IACA,MAAMoB,kBAAkB,GAAG,EAA3B;;IAEA,KAAK,MAAMzB,QAAX,IAAuBjB,SAAvB,EAAkC;MAChC,MAAMkB,YAAY,GAAGuB,cAAc,CAACxB,QAAD,CAAd,IAA4B,EAAjD;;MACA,KAAK,MAAM0B,GAAX,IAAkBzB,YAAlB,EAAgC;QAC9BI,kBAAkB,CAACsB,IAAnB,CAAwBD,GAAG,CAACnB,KAA5B;QACAkB,kBAAkB,CAAE,GAAEzB,QAAS,IAAG0B,GAAG,CAACE,UAAW,EAA/B,CAAlB,GAAsDF,GAAG,CAACnB,KAA1D;MACD;IACF,CAlBkC,CAoBnC;;;IACA,IAAIsB,eAAe,GAAG,EAAtB;;IACA,IAAIxB,kBAAkB,CAACe,MAAnB,GAA4B,CAAhC,EAAmC;MACjCS,eAAe,GAAG,MAAM,KAAKzB,wBAAL,CAA8BC,kBAA9B,CAAxB;MACAT,OAAO,CAACuB,GAAR,CAAY,cAAZ,EAA4BG,IAAI,CAACC,GAAL,KAAaF,SAAzC,EAAoD,IAApD;IACD,CAzBkC,CA2BnC;;;IACA,MAAMX,YAAY,GAAG,EAArB;IACA,MAAMoB,wBAAwB,GAAG,EAAjC;;IAEA,KAAK,MAAMvB,KAAX,IAAoBF,kBAApB,EAAwC;MACtC,MAAMG,UAAU,GAAGqB,eAAe,CAACtB,KAAD,CAAf,IAA0B,EAA7C;;MACA,KAAK,MAAMwB,SAAX,IAAwBvB,UAAxB,EAAoC;QAClCE,YAAY,CAACiB,IAAb,CAAkBI,SAAS,CAACxB,KAA5B;QACAuB,wBAAwB,CAAE,GAAEvB,KAAM,IAAGwB,SAAS,CAACC,QAAS,EAAhC,CAAxB,GAA6DD,SAAS,CAACxB,KAAvE;MACD;IACF,CArCkC,CAuCnC;;;IACA,IAAI0B,uBAAuB,GAAG;MAAEnB,UAAU,EAAE,EAAd;MAAkBC,IAAI,EAAE;IAAxB,CAA9B;;IACA,IAAIL,YAAY,CAACU,MAAb,GAAsB,CAA1B,EAA6B;MAC3Ba,uBAAuB,GAAG,MAAM,KAAKxB,gCAAL,CAAsCC,YAAtC,CAAhC;MACAd,OAAO,CAACuB,GAAR,CAAY,eAAZ,EAA6BG,IAAI,CAACC,GAAL,KAAaF,SAA1C,EAAqD,IAArD;IACD;;IAED,OAAO;MACLG,cADK;MAELK,eAFK;MAGLI,uBAHK;MAILR,kBAJK;MAKLK;IALK,CAAP;EAOD;EAED;AACF;AACA;;;EACEI,UAAU,GAAG;IACX,KAAKvD,KAAL,CAAWwD,KAAX;IACA,KAAKtD,eAAL,CAAqBsD,KAArB;EACD;EAED;AACF;AACA;;;EACEC,aAAa,GAAG;IACd,OAAO;MACLC,SAAS,EAAE,KAAK1D,KAAL,CAAW2D,IADjB;MAELC,YAAY,EAAE,KAAK1D,eAAL,CAAqByD;IAF9B,CAAP;EAID;;AAxPmC,C,CA2PtC;;AACA,OAAO,MAAME,wBAAwB,GAAG,IAAI/D,wBAAJ,EAAjC;AAEP;AACA;AACA;;AACA,OAAO,MAAMgE,2BAAN,CAAkC;EACvC/D,WAAW,GAAG;IACZ,KAAKgE,eAAL,GAAuB,IAAvB,CADY,CACiB;EAC9B;EAED;AACF;AACA;AACA;AACA;;;EACkC,MAA1BC,0BAA0B,CAACC,iBAAD,EAAoB;IAClD,MAAMvB,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB;IACA3B,OAAO,CAACuB,GAAR,CAAY,gBAAZ;;IAEA,IAAI;MACF;MACA,IAAI,CAAC,KAAKuB,eAAV,EAA2B;QACzB,MAAM,KAAKG,wBAAL,CAA8BD,iBAA9B,CAAN;MACD,CAJC,CAMF;;;MACA,MAAM1C,OAAO,CAACC,GAAR,CAAY,CAChB,KAAK2C,kCAAL,CAAwCF,iBAAxC,CADgB,EAEhB,KAAKG,wCAAL,CAA8CH,iBAA9C,CAFgB,EAGhB,KAAKI,wCAAL,CAA8CJ,iBAA9C,CAHgB,CAAZ,CAAN,CAPE,CAaF;;MACA,MAAM,KAAKK,8BAAL,CAAoCL,iBAApC,CAAN;MAEAhD,OAAO,CAACuB,GAAR,CAAa,kBAAiBG,IAAI,CAACC,GAAL,KAAaF,SAAU,IAArD;IACD,CAjBD,CAiBE,OAAO1B,KAAP,EAAc;MACdC,OAAO,CAACD,KAAR,CAAc,YAAd,EAA4BA,KAA5B;MACA,MAAMA,KAAN;IACD;EACF;EAED;AACF;AACA;;;EACgC,MAAxBkD,wBAAwB,CAACD,iBAAD,EAAoB;IAChDhD,OAAO,CAACuB,GAAR,CAAY,iBAAZ;IACA,MAAME,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB;IAEA,KAAKmB,eAAL,GAAuB,MAAMF,wBAAwB,CAACtB,mBAAzB,CAA6C0B,iBAAiB,CAACM,cAA/D,CAA7B;IAEAtD,OAAO,CAACuB,GAAR,CAAa,oBAAmBG,IAAI,CAACC,GAAL,KAAaF,SAAU,IAAvD;EACD;EAED;AACF;AACA;;;EAC0C,MAAlCyB,kCAAkC,CAACF,iBAAD,EAAoB;IAC1D,MAAMO,gBAAgB,GAAG,EAAzB;IACA,MAAMC,iBAAiB,GAAG,KAAKV,eAAL,CAAqBlB,cAA/C;;IAEA,KAAK,MAAMxB,QAAX,IAAuB4C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,MAAMG,UAAU,GAAGT,iBAAiB,CAACpB,cAAlB,CAAiCxB,QAAjC,KAA8C,EAAjE;;MAEA,KAAK,MAAM4B,UAAX,IAAyByB,UAAzB,EAAqC;QACnC,MAAMC,GAAG,GAAI,GAAEtD,QAAS,IAAG4B,UAAW,EAAtC,CADmC,CAGnC;;QACA,MAAM2B,YAAY,GAAGH,iBAAiB,CAACpD,QAAD,CAAjB,IAA+B,EAApD;QACA,MAAMwD,MAAM,GAAGD,YAAY,CAACE,IAAb,CAAkB/B,GAAG,IAAIA,GAAG,CAACE,UAAJ,KAAmBA,UAA5C,CAAf;;QAEA,IAAI,CAAC4B,MAAL,EAAa;UACXL,gBAAgB,CAACxB,IAAjB,CAAsB;YACpB3B,QAAQ,EAAEA,QADU;YAEpB4B,UAAU,EAAEA,UAFQ;YAGpB8B,MAAM,EAAE,CAHY;YAIpBzE,IAAI,EAAE;UAJc,CAAtB;QAMD;MACF;IACF;;IAED,IAAIkE,gBAAgB,CAAC/B,MAAjB,GAA0B,CAA9B,EAAiC;MAC/BxB,OAAO,CAACuB,GAAR,CAAa,OAAMgC,gBAAgB,CAAC/B,MAAO,YAA3C;MACA,MAAMnD,wBAAwB,CAACkF,gBAAD,CAA9B,CAF+B,CAI/B;;MACA,MAAM,KAAKQ,wBAAL,CAA8Bf,iBAA9B,CAAN;IACD;EACF;EAED;AACF;AACA;;;EACgD,MAAxCG,wCAAwC,CAACH,iBAAD,EAAoB;IAChE,MAAMgB,sBAAsB,GAAG,EAA/B;IACA,MAAMC,uBAAuB,GAAG,KAAKnB,eAAL,CAAqBb,eAArD;;IAEA,KAAK,MAAM7B,QAAX,IAAuB4C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,KAAK,MAAMtB,UAAX,IAAyBgB,iBAAiB,CAACpB,cAAlB,CAAiCxB,QAAjC,KAA8C,EAAvE,EAA2E;QACzE,MAAM8D,eAAe,GAAI,GAAE9D,QAAS,IAAG4B,UAAW,EAAlD;QACA,MAAMrB,KAAK,GAAGqC,iBAAiB,CAACnB,kBAAlB,CAAsC,GAAEzB,QAAS,IAAG4B,UAAW,EAA/D,CAAd;QAEA,IAAI,CAACrB,KAAL,EAAY;QAEZ,MAAMwD,OAAO,GAAGnB,iBAAiB,CAACoB,WAAlB,CAA8BF,eAA9B,KAAkD,EAAlE;QACA,MAAMG,kBAAkB,GAAGJ,uBAAuB,CAACtD,KAAD,CAAvB,IAAkC,EAA7D;;QAEA,KAAK,MAAMyB,QAAX,IAAuB+B,OAAvB,EAAgC;UAC9B,MAAMP,MAAM,GAAGS,kBAAkB,CAACR,IAAnB,CAAwB/B,GAAG,IAAIA,GAAG,CAACM,QAAJ,KAAiBA,QAAhD,CAAf;;UAEA,IAAI,CAACwB,MAAL,EAAa;YACXI,sBAAsB,CAACjC,IAAvB,CAA4B;cAC1BuC,iBAAiB,EAAE3D,KADO;cAE1ByB,QAAQ,EAAEA,QAFgB;cAG1B0B,MAAM,EAAE,CAHkB;cAI1BS,SAAS,EAAE;YAJe,CAA5B;UAMD;QACF;MACF;IACF;;IAED,IAAIP,sBAAsB,CAACxC,MAAvB,GAAgC,CAApC,EAAuC;MACrCxB,OAAO,CAACuB,GAAR,CAAa,OAAMyC,sBAAsB,CAACxC,MAAO,YAAjD;MACA,MAAMwB,iBAAiB,CAAC1E,8BAAlB,CAAiD0F,sBAAjD,CAAN,CAFqC,CAIrC;;MACA,MAAM,KAAKQ,8BAAL,CAAoCxB,iBAApC,CAAN;IACD;EACF;EAED;AACF;AACA;;;EACgD,MAAxCI,wCAAwC,CAACJ,iBAAD,EAAoB;IAChE,MAAMyB,uBAAuB,GAAG,EAAhC;IACA,MAAMC,aAAa,GAAG,EAAtB;IACA,MAAMC,yBAAyB,GAAG,KAAK7B,eAAL,CAAqBT,uBAAvD;;IAEA,KAAK,MAAMjC,QAAX,IAAuB4C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,KAAK,MAAMtB,UAAX,IAAyBgB,iBAAiB,CAACpB,cAAlB,CAAiCxB,QAAjC,KAA8C,EAAvE,EAA2E;QACzE,MAAM8D,eAAe,GAAI,GAAE9D,QAAS,IAAG4B,UAAW,EAAlD;QACA,MAAMsC,iBAAiB,GAAGtB,iBAAiB,CAACnB,kBAAlB,CAAsC,GAAEzB,QAAS,IAAG4B,UAAW,EAA/D,CAA1B;QAEA,IAAI,CAACsC,iBAAL,EAAwB;;QAExB,KAAK,MAAMlC,QAAX,IAAuBY,iBAAiB,CAACoB,WAAlB,CAA8BF,eAA9B,KAAkD,EAAzE,EAA6E;UAC3E,MAAMR,GAAG,GAAI,GAAEtD,QAAS,IAAG4B,UAAW,IAAGI,QAAS,EAAlD;UACA,MAAMwC,WAAW,GAAG5B,iBAAiB,CAACd,wBAAlB,CAA4C,GAAEoC,iBAAkB,IAAGlC,QAAS,EAA5E,CAApB;UAEA,IAAI,CAACwC,WAAL,EAAkB,SAJyD,CAM3E;;UACA,MAAMC,kBAAkB,GAAG7B,iBAAiB,CAAC8B,gBAAlB,CAAmCpB,GAAnC,KAA2C,EAAtE;UACA,MAAMqB,kBAAkB,GAAGJ,yBAAyB,CAACzD,UAA1B,CAAqC0D,WAArC,KAAqD,EAAhF;UACA,MAAMI,oBAAoB,GAAGD,kBAAkB,CAAC5E,GAAnB,CAAuB8E,IAAI,IAAIA,IAAI,CAACC,eAApC,CAA7B;;UAEA,KAAK,MAAMA,eAAX,IAA8BL,kBAA9B,EAAkD;YAChD,IAAI,CAACG,oBAAoB,CAACG,QAArB,CAA8BD,eAA9B,CAAL,EAAqD;cACnDT,uBAAuB,CAAC1C,IAAxB,CAA6B;gBAC3BqD,uBAAuB,EAAER,WADE;gBAE3BS,oBAAoB,EAAEH,eAFK;gBAG3BpB,MAAM,EAAE,CAHmB;gBAI3BzE,IAAI,EAAE;cAJqB,CAA7B;YAMD;UACF,CApB0E,CAsB3E;;;UACA,MAAMiG,YAAY,GAAGtC,iBAAiB,CAACuC,UAAlB,CAA6B7B,GAA7B,KAAqC,EAA1D;UACA,MAAM8B,YAAY,GAAGb,yBAAyB,CAACxD,IAA1B,CAA+ByD,WAA/B,KAA+C,EAApE;UACA,MAAMa,cAAc,GAAGD,YAAY,CAACrF,GAAb,CAAiB8E,IAAI,IAAIA,IAAI,CAACS,KAA9B,CAAvB;;UAEA,KAAK,MAAMA,KAAX,IAAoBJ,YAApB,EAAkC;YAChC,IAAI,CAACG,cAAc,CAACN,QAAf,CAAwBO,KAAxB,CAAL,EAAqC;cACnChB,aAAa,CAAC3C,IAAd,CAAmB;gBACjB4D,UAAU,EAAED,KADK;gBAEjBN,uBAAuB,EAAER,WAFR;gBAGjBd,MAAM,EAAE,CAHS;gBAIjBzE,IAAI,EAAE;cAJW,CAAnB;YAMD;UACF;QACF;MACF;IACF,CAnD+D,CAqDhE;;;IACA,MAAMuG,YAAY,GAAG,EAArB;;IAEA,IAAInB,uBAAuB,CAACjD,MAAxB,GAAiC,CAArC,EAAwC;MACtCxB,OAAO,CAACuB,GAAR,CAAa,OAAMkD,uBAAuB,CAACjD,MAAO,cAAlD;MACAoE,YAAY,CAAC7D,IAAb,CAAkBxD,+BAA+B,CAACkG,uBAAD,CAAjD;IACD;;IAED,IAAIC,aAAa,CAAClD,MAAd,GAAuB,CAA3B,EAA8B;MAC5BxB,OAAO,CAACuB,GAAR,CAAa,OAAMmD,aAAa,CAAClD,MAAO,YAAxC;MACAoE,YAAY,CAAC7D,IAAb,CAAkBvD,qBAAqB,CAACkG,aAAD,CAAvC;IACD;;IAED,MAAMpE,OAAO,CAACC,GAAR,CAAYqF,YAAZ,CAAN;EACD;EAED;AACF;AACA;;;EACsC,MAA9BvC,8BAA8B,CAACL,iBAAD,EAAoB;IACtD;IACA,MAAM6C,sBAAsB,GAAG,EAA/B;IACA,MAAMC,oBAAoB,GAAG,EAA7B;IACA,MAAMC,kBAAkB,GAAG,EAA3B;IACA,MAAMC,iBAAiB,GAAG,EAA1B,CALsD,CAOtD;IACA;IAEA;;IACA,MAAMC,cAAc,GAAG,EAAvB;;IAEA,IAAIJ,sBAAsB,CAACrE,MAAvB,GAAgC,CAApC,EAAuC;MACrCyE,cAAc,CAAClE,IAAf,CAAoBiB,iBAAiB,CAACkD,0BAAlB,CAA6CL,sBAA7C,CAApB;IACD;;IAED,IAAIC,oBAAoB,CAACtE,MAArB,GAA8B,CAAlC,EAAqC;MACnCyE,cAAc,CAAClE,IAAf,CAAoBiB,iBAAiB,CAACmD,gCAAlB,CAAmDL,oBAAnD,CAApB;IACD;;IAED,IAAIC,kBAAkB,CAACvE,MAAnB,GAA4B,CAAhC,EAAmC;MACjCyE,cAAc,CAAClE,IAAf,CAAoBiB,iBAAiB,CAACoD,iCAAlB,CAAoDL,kBAApD,CAApB;IACD;;IAED,IAAIC,iBAAiB,CAACxE,MAAlB,GAA2B,CAA/B,EAAkC;MAChCyE,cAAc,CAAClE,IAAf,CAAoBiB,iBAAiB,CAACqD,uBAAlB,CAA0CL,iBAA1C,CAApB;IACD;;IAED,MAAM1F,OAAO,CAACC,GAAR,CAAY0F,cAAZ,CAAN;EACD;EAED;AACF;AACA;;;EACgC,MAAxBlC,wBAAwB,CAACf,iBAAD,EAAoB;IAChD;IACA,MAAMpB,cAAc,GAAG,MAAMgB,wBAAwB,CAAC1D,uBAAzB,CAAiD8D,iBAAiB,CAACM,cAAnE,CAA7B;;IAEA,KAAK,MAAMlD,QAAX,IAAuB4C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,MAAMjD,YAAY,GAAGuB,cAAc,CAACxB,QAAD,CAAd,IAA4B,EAAjD;;MACA,KAAK,MAAM0B,GAAX,IAAkBzB,YAAlB,EAAgC;QAC9B,MAAMqD,GAAG,GAAI,GAAEtD,QAAS,IAAG0B,GAAG,CAACE,UAAW,EAA1C;QACAgB,iBAAiB,CAACnB,kBAAlB,CAAqC6B,GAArC,IAA4C5B,GAAG,CAACnB,KAAhD;MACD;IACF;EACF;EAED;AACF;AACA;;;EACsC,MAA9B6D,8BAA8B,CAACxB,iBAAD,EAAoB;IACtD,MAAMvC,kBAAkB,GAAG6F,MAAM,CAACC,MAAP,CAAcvD,iBAAiB,CAACnB,kBAAhC,CAA3B;IACA,MAAMI,eAAe,GAAG,MAAMW,wBAAwB,CAACpC,wBAAzB,CAAkDC,kBAAlD,CAA9B;;IAEA,KAAK,MAAME,KAAX,IAAoBF,kBAApB,EAAwC;MACtC,MAAMG,UAAU,GAAGqB,eAAe,CAACtB,KAAD,CAAf,IAA0B,EAA7C;;MACA,KAAK,MAAMwB,SAAX,IAAwBvB,UAAxB,EAAoC;QAClC,MAAM8C,GAAG,GAAI,GAAE/C,KAAM,IAAGwB,SAAS,CAACC,QAAS,EAA3C;QACAY,iBAAiB,CAACd,wBAAlB,CAA2CwB,GAA3C,IAAkDvB,SAAS,CAACxB,KAA5D;MACD;IACF;EACF;EAED;AACF;AACA;;;EACE2B,UAAU,GAAG;IACX,KAAKQ,eAAL,GAAuB,IAAvB;EACD;;AA/QsC,C,CAkRzC;;AACA,OAAO,MAAM0D,2BAA2B,GAAG,IAAI3D,2BAAJ,EAApC"}, "metadata": {}, "sourceType": "module"}