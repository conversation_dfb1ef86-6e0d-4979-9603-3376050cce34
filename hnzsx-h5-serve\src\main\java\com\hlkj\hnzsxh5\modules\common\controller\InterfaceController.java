package com.hlkj.hnzsxh5.modules.common.controller;

import java.io.IOException;
import java.util.*;
import java.util.stream.IntStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hlkj.hnzsxh5.modules.h5BusinessAcceptance.strategy.TemplateOrderStrategyContext;
import com.hlkj.hnzsxh5.modules.hnzsxh5_log.entity.OrderProcessLogEntity;
import com.hlkj.hnzsxh5.modules.hnzsxh5_log.service.OrderProcessLogService;
import com.hlkj.hnzsxh5.modules.hnzsxh5_order.enums.PaymentState;
import com.hlkj.hnzsxh5.modules.hnzsxh5_order.enums.PrepayStatus;
import com.hlkj.hnzsxh5.modules.hnzsxh5_homepage.entity.HnzsxH5GoodsInfo;
import com.hlkj.hnzsxh5.modules.hnzsxh5_order.entity.Hnzsxh5PaymentInfoEntity;
import com.hlkj.hnzsxh5.modules.hnzsxh5_order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hlkj.hnzsxh5.common.config.awzS3.AwzS3Service;
import com.hlkj.hnzsxh5.common.personal.StaticFinalUtil;
import com.hlkj.hnzsxh5.common.userFile.UserFileUtil;
import com.hlkj.hnzsxh5.common.userFile.model.UserModel;
import com.hlkj.hnzsxh5.exception.R;
import com.hlkj.hnzsxh5.modules.common.service.HnzsxUserService;
import com.hlkj.hnzsxh5.modules.common.service.UploadFileService;
import com.hlkj.hnzsxh5.modules.hnzsxh5_order.entity.Hnzsxh5OrderInfoEntity;
import com.hlkj.hnzsxh5.util.CityUtil;
import com.hlkj.hnzsxh5.util.Constant;
import com.hlkj.hnzsxh5.util.DateUtils;
import com.hlkj.hnzsxh5.util.InterfaceUtil;
import com.hlkj.hnzsxh5.util.RedisUtil;
import com.hlkj.hnzsxh5.util.SmsYanZhengMa;
import com.hlkj.hnzsxh5.util.StringUtil;
import com.hlkj.hnzsxh5.modules.hnzsxh5_goods.service.GoodsService;
import com.hlkj.hnzsxh5.modules.hnzsxh5_order.entity.Hnzsxh5OrderLivingBodyCertiInfoEntity;
import com.hlkj.hnzsxh5.modules.hnzsxh5_number_preoccupation_record.entity.NumberPreoccupationRecordEntity;
import com.hlkj.hnzsxh5.modules.hnzsxh5_number_preoccupation_record.service.NumberPreoccupationRecordService;

/**
 * 公共接口控制器
 * <p>
 * Title: InterfaceController.java
 * </p>
 * 
 * @<NAME_EMAIL>
 * @date 2025-03-07 05:15:56
 * @version 1.0
 */
@RestController
@RequestMapping("/api/interface")
@Slf4j
public class InterfaceController {

	private static final Logger logger = LoggerFactory.getLogger(InterfaceController.class);

	@Resource
	RedisUtil redisService;
	@Resource
	UserFileUtil userFile;
	@Resource
	HnzsxUserService hnzsxAppUserService;
	@Resource
	AwzS3Service awzS3Service;
	@Resource
	StaticFinalUtil staticFinal;
	@Resource
	SmsYanZhengMa smsYanZhengMa;
	@Resource
	Hnzsxh5OrderInfoService hnzsxh5OrderInfoService;
	@Resource
	UploadFileService uploadFileService;
	@Resource
	Hnzsxh5OrderLivingBodyCertiInfoService hnzsxh5OrderLivingBodyCertiInfoService;
	@Resource
	GoodsService goodsService;
	@Resource
	Hnzsxh5PaymentInfoService paymentInfoService;
	@Resource
	TemplateOrderStrategyContext strategyContext;
	@Resource
	OrderValidationService orderValidationService;
	@Resource
	private NumberPreoccupationRecordService phonePreRecService;
	@Resource(name = "taskExecutor")
	private ThreadPoolTaskExecutor taskExecutor;
	@Resource
	private OrderProcessLogService orderProcessLogService;

	/**
	 * 测试接口加解密
	 * 
	 * @MethodName: ceshi
	 * @<NAME_EMAIL>
	 * @param reqData
	 * @return R
	 * @date 2025-03-11 03:42:00
	 */
	@RequestMapping("/ceshi")
	public R ceshi(@RequestBody JSONObject reqData) {
		logger.info("ceshi：" + reqData);
		return R.ok("测试成功");
	}

	/**
	 * 掌上销私有号池查询 2019-07-23 V1.0
	 *
	 * @param phone
	 * @return
	 */
	@RequestMapping("/qryJTNumberSelectPoolInfo")
	public R qryJTNumberSelectPoolInfo() {
		logger.info("私有号池查询");
		JSONObject resultData = new JSONObject();
		UserModel userModel = userFile.getUserModel();
		String keys = staticFinal.getSessiongToke() + ":" + Constant.HNZSX_SELSET_PHONE_SWCH + userModel.getCitycode();
		String object2 = redisService.getString(keys);
		if (redisService.hasKey(keys) && "1".equals(object2)) {
			resultData = InterfaceUtil.qryJTNumberSelectPoolInfo(userModel.getSaleBoxCode(), userModel.getCitycode());

			JSONArray data = resultData.getJSONArray("data");
			// 将 JSONArray 中的所有键转换为小写
			JSONArray modifiedJsonArray = new JSONArray();
			IntStream.range(0, data.size()).forEach(i -> {
				JSONObject jsonObject = data.getJSONObject(i);
				JSONObject modifiedJsonObject = new JSONObject();
				jsonObject.keySet().forEach(key -> {
					modifiedJsonObject.put(key.toLowerCase(), jsonObject.get(key));
				});
				modifiedJsonArray.add(modifiedJsonObject);
			});
			resultData.put("data", modifiedJsonArray);

			logger.info("老" + object2);
		} else {
			logger.info("新" + object2);
			resultData = InterfaceUtil.qryJTNumberSelectPoolInfoNew(userModel.getSaleBoxCode(),
					userModel.getCitycode());
		}

		String defaultPoolId = CityUtil.lan_codes.get(userModel.getCitycode());
		JSONArray privatePoolsList = resultData.getJSONArray("data");
		for (int i = 0; i < privatePoolsList.size(); i++) {
			JSONObject privatePoolsObject = privatePoolsList.getJSONObject(i);
			if (defaultPoolId.equals(privatePoolsObject.getString("pool_id"))) {
				privatePoolsObject.put("checked", true);
			} else {
				privatePoolsObject.put("checked", false);
			}

			privatePoolsObject.put("text", privatePoolsObject.getString("pool_name"));
			privatePoolsObject.put("value", i);
		}

		return R.ok().put("defaultPoolId", CityUtil.lan_codes.get(userModel.getCitycode())).put("privatePoolsList",
				privatePoolsList);
	}

	/**
	 * 加载号码信息
	 *
	 * @param selContent 填写的查询内容
	 * @param cardnum    全部号段
	 * @param prefee     预存金额
	 * @param basefee    保底金额
	 * @param mantissa   尾数规则
	 * @param userpool   号池选择
	 * @param matchNo    需要查询的号码（靓号查询才有）
	 * @param searchType 查询的类型（靓号查询才有）
	 * @return
	 */
	@RequestMapping("/loadMobile")
	public Map<String, Object> loadMobileRe(@RequestBody JSONObject reqData) {
		UserModel userModel = userFile.getUserModel();
		String key = staticFinal.getSessiongToke() + ":" + Constant.HNZSX_SELSET_PHONE_SWCH + userModel.getCitycode();
		String object2 = redisService.getString(key);
		if (redisService.hasKey(key) && "1".equals(object2)) {
			logger.info("老" + object2);
			return loadMobile(reqData);// 旧版
		} else {
			logger.info("新" + object2);
			return loadMobileNew(reqData);// 新版
		}
	}

	public R loadMobile(JSONObject reqData) {
		List<Map<String, Object>> phoneList = null;
		Map<String, Object> param = new HashMap<>();

		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();
		String selContent = reqData.getString("selContent");
		String cardnum = reqData.getString("cardnum");
		String prefee = reqData.getString("prefee");
		String basefee = reqData.getString("basefee");
		String mantissa = reqData.getString("mantissa");
		String userpool = reqData.getString("userpool");
		String matchNo = reqData.getString("matchNo");
		String searchType = reqData.getString("searchType");

		param.put("saleBoxCode", userModel.getSaleBoxCode());
		// 当前用户地区
		if ("736".equals(managerCityCode)) {
			param.put("acc_nbr_pool_id", "10110721");
		}
		// 使用私人号池
		if (StringUtils.isNotBlank(userpool)) {
			param.put("acc_nbr_pool_id", userpool);
		}
		// 尾号内容查询
		if (StringUtils.isNotBlank(selContent)) {
			param.put("accNbrEnd", selContent);// 号码尾
		}
		// 号段头
		if (StringUtils.isNotBlank(cardnum)) {
			param.put("accNbrStart", cardnum);// 号码头
		}
		// 预付款
		if (StringUtils.isNotBlank(prefee)) {
			if ("0".equals(prefee) || "1".equals(prefee)) {
				param.put("codeLevel", prefee);
			} else if ("10000".equals(prefee)) {
				param.put("lt_prestore", "10000");
			} else if ("150000".equals(prefee)) {
				param.put("gt_prestore", "150000");
			} else if (prefee.indexOf("-") > -1) {
				String prefeeArray[] = prefee.split("-");
				param.put("gt_prestore", prefeeArray[0].replace(" ", ""));
				param.put("lt_prestore", prefeeArray[1].replace(" ", ""));
			}
		} else {
			if ("4".equals(mantissa)) {
				param.put("codeLevel", "");// 选择不含四的时候要为空
			} else if ((StringUtils.isBlank(userpool)) && (StringUtils.isBlank(prefee))) {
				// wm 2021-04-09 修改，集团选好新规则
				// if (managerCityCode.equals("731")) {
				param.put("codeLevel", "0");
				/*
				 * } else { param.put("codeLevel", "1"); }
				 */
			}
		}
		// 保底消费
		if (StringUtils.isNotBlank(basefee)) {
			param.put("codeLevel", basefee);
		}
		// 尾号规则
		if (StringUtils.isNotBlank(mantissa)) {
			if ("4".equals(mantissa)) {
				// 不含4
				param.put("acc_nbr_last_four_num", "4");
			} else {
				param.put("acc_nbr_features", mantissa);// 号码特征
			}
		}
		// 每页显示数量
		String pageSize = "40";
		// 条件
		String currentPage = "1";
		param.put("lan_id", managerCityCode);// 本地网
		param.put("pageIndex", currentPage);// 页数下标
		param.put("pageSize", pageSize);
		param.put("matchNo", matchNo);
		param.put("searchType", searchType);
		phoneList = InterfaceUtil.queryPhoneNbrs(param);
		return R.ok().put("phoneList", phoneList);
	}

	public R loadMobileNew(JSONObject reqData) {
		Map<String, Object> param = new HashMap<>();

		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();
		String selContent = reqData.getString("selContent");
		String cardnum = reqData.getString("cardnum");
		String prefee = reqData.getString("prefee");
		String basefee = reqData.getString("basefee");
		String mantissa = reqData.getString("mantissa");
		String userpool = reqData.getString("userpool");
		String matchNo = reqData.getString("matchNo");
		String searchType = reqData.getString("searchType");
		logger.info("掌上销工具号码加载接口入参{selContent :" + selContent + "}{cardnum：" + cardnum + "}{prefee：" + prefee
				+ "}{basefee：" + basefee + "}{mantissa：" + mantissa + "}{userpool=" + userpool + "}");

		// 渠道编码
		param.put("saleBoxCode", userModel.getSaleBoxCode());

		// 当前用户地区
		if ("736".equals(managerCityCode)) {
			param.put("acc_nbr_pool_id", "10110721");
		}
		// 使用私人号池
		if (!StringUtils.isEmpty(userpool)) {
			param.put("acc_nbr_pool_id", userpool);
		}

		// 号段头
		if (!StringUtils.isEmpty(cardnum)) {
			param.put("accNbrStart", cardnum);// 号码头
		}

		// 预付款
		if (!StringUtils.isEmpty(prefee)) {
			if ("0".equals(prefee) || "1".equals(prefee)) {
				param.put("codeLevel", prefee);
			} else if ("10000".equals(prefee)) {
				param.put("lt_prestore", "10000");
			} else if ("150000".equals(prefee)) {
				param.put("gt_prestore", "150000");
			} else if (prefee.indexOf("-") > -1) {
				String prefeeArray[] = prefee.split("-");
				param.put("gt_prestore", prefeeArray[0].replace(" ", ""));
				param.put("lt_prestore", prefeeArray[1].replace(" ", ""));
			}
		} else {
			if ("4".equals(mantissa)) {
				param.put("codeLevel", "");// 选择不含四的时候要为空
			} else if ((StringUtils.isEmpty(userpool)) && (StringUtils.isEmpty(prefee))) {
				param.put("codeLevel", "0");
			}
		}
		// 保底消费
		if (!StringUtils.isEmpty(basefee)) {
			param.put("codeLevel", basefee);
		}
		// 尾号规则
		if (!StringUtils.isEmpty(mantissa)) {
			if ("4".equals(mantissa)) {
				// 不含4
				param.put("acc_nbr_features", "4");
			} else {
				param.put("acc_nbr_features", mantissa);// 号码特征
			}
		}
		param.put("lan_id", managerCityCode);// 本地网

		if (StringUtils.isNotBlank(matchNo)) {
			// 靓号查询参数
			param.put("matchNo", matchNo);
			param.put("searchType", searchType);
		} else {
			// 尾号内容查询
			if (!StringUtils.isEmpty(selContent) && selContent.length() <= 4) {
				param.put("accNbrEnd", selContent);// 号码尾
			} else {
				param.put("match_nbr", selContent); // 包含数字
			}
		}

		JSONObject queryPhoneNbrsNewRes = InterfaceUtil.queryPhoneNbrsNew(param);
		if ("1".equals(queryPhoneNbrsNewRes.getString("code"))) {
			return R.error(1, queryPhoneNbrsNewRes.getString("message"));
		}
		return R.ok().put("phoneList", queryPhoneNbrsNewRes.getJSONArray("data"));
	}

	/**
	 * 手机号码预占
	 * 
	 * @param phone
	 * @param customerCard
	 * @param token
	 * @param phoneApprovePassword_靓号降档密码（wm 2020-12-07增加，靓号降档才有此参数）
	 * @return
	 */
	@RequestMapping("/prePhone")
	public R prePhone(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();
			String phone = reqData.getString("phone");

			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String customerCard = customerInfo.getString("customerCard");

			if (StringUtils.isBlank(phone) || StringUtils.isBlank(customerCard)) {
				return R.error(1, "参数为空");
			}

			Map<String, Object> param = new HashMap<>();
			param.put("acc_nbr", phone);
			param.put("cert_number", customerCard);
			param.put("lan_id", managerCityCode);
			param.put("staffCode", userModel.getStaffCode());
			param.put("saleBoxCode", userModel.getSaleBoxCode());
			param.put("phoneApprovePassword", reqData.getString("phoneApprovePassword"));
			JSONObject preSelectNbr = InterfaceUtil.preSelectNbr(param);
			if ("0".equals(preSelectNbr.get("code"))) {
				// 号码占用成功
				logger.info("用户" + customerCard + "成功占用号码" + phone);
				return R.ok("预占号码成功");
			} else {
				// 预占失败
				String resultMessage = preSelectNbr.get("message").toString();

				if (resultMessage.contains("状态为[1003]")) {// 1.预占"已预占状态"的号码
					logger.info("号码" + phone + "状态为预占状态,不可第二次预占！");
					return R.error(1, "号码" + phone + "状态为预占状态,不可第二次预占！");
				} else if (resultMessage.contains("已经预约超过五个号码")) {// 2.名下预占已经超过五个
					logger.info("身份证号[" + customerCard + "]已经预约超过五个号码，不允许再预约！");
					return R.error(1, "身份证号[" + customerCard + "]已经预占超过五个号码，不允许再预约，请点击右侧\"预占号码\"选择预约的号码。");
				} else {
					logger.info("用户" + customerCard + "占用号码" + phone + "失败");
					return R.error(1, resultMessage);
				}
			}
		} catch (Exception e) {
			logger.error("手机号码预占异常" + e);
			return R.error(1, "手机号码预占异常" + e);
		}
	}

	/**
	 * 手机号码预占-省内下沉
	 * 
	 * @param phone
	 * @param customerCard
	 * @param phoneApprovePassword_靓号降档密码（wm 2020-12-07增加，靓号降档才有此参数）
	 * @return
	 */
	@RequestMapping("/supPreselectPhoneNbr")
	public R supPreselectPhoneNbr(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();
			String phone = reqData.getString("phone");

			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String customerCard = customerInfo.getString("customerCard");

			if (StringUtils.isBlank(phone) || StringUtils.isBlank(customerCard)) {
				return R.error(1, "参数为空");
			}

			Map<String, Object> param = new HashMap<>();
			param.put("acc_nbr", phone);
			param.put("cert_number", customerCard);
			param.put("lan_id", managerCityCode);
			param.put("staffCode", userModel.getStaffCode());
			param.put("saleBoxCode", userModel.getSaleBoxCode());
			param.put("phoneApprovePassword", reqData.getString("phoneApprovePassword"));
			R preSelectNbr = InterfaceUtil.supPreselectPhoneNbr(param);
			if (0 == preSelectNbr.getInteger("code")) {
				// 号码占用成功
				logger.info("用户{},成功占用号码 {}", customerCard, phone);
				return R.ok("预占号码成功");
			} else {
				// 预占失败
				String resultMessage = preSelectNbr.getString("message");

				if (resultMessage.contains("状态为[1003]")) {// 1.预占"已预占状态"的号码
					logger.info("用户{},{}状态为预占状态,不可第二次预占！", customerCard, phone);
					return R.error(1, "号码" + phone + "状态为预占状态,不可第二次预占！");
				} else if (resultMessage.contains("已经预约超过五个号码")) {// 2.名下预占已经超过五个
					logger.info("身份证号{}已经预约超过五个号码，不允许再预约！", StringUtil.desensitizedIdNumber(customerCard));
					return R.error(1, "身份证号[" + customerCard + "]已经预占超过五个号码，不允许再预约，请点击右侧\"预占号码\"选择预约的号码。");
				} else {
					logger.info("用户{},占用号码 {} 失败", customerCard, phone);
					return R.error(1, resultMessage);
				}
			}
		} catch (Exception e) {
			logger.error("手机号码预占异常" + e);
			return R.error(1, "手机号码预占异常" + e);
		}
	}

	/**
	 * 手机号码释放-集团
	 *
	 * @param phone
	 * @param id
	 * @return
	 */
	@RequestMapping("/releasePhone")
	public R releasePhone(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();
			String phone = reqData.getString("phone");

			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String customerCard = customerInfo.getString("customerCard");

			if (StringUtils.isBlank(phone) || StringUtils.isBlank(customerCard)) {
				return R.error(1, "参数为空");
			}

			Map<String, Object> param = new HashMap<>();
			param.put("phoneNbr", phone);
			param.put("certNumber", customerCard);
			param.put("lan_id", managerCityCode);
			param.put("staffCode", userModel.getStaffCode());
			param.put("saleBoxCode", userModel.getSaleBoxCode());
			JSONObject restData = InterfaceUtil.accepcardreleaseFor4G(param);
			if ("0".equals(restData.get("code"))) {
				logger.info("用户" + customerCard + "释放手机号码" + phone + "成功");
				return R.ok("释放号码成功");
			} else {
				logger.info("用户" + customerCard + "释放手机号码" + phone + "失败");
				return R.error(2, "释放失败");
			}
		} catch (Exception e) {
			logger.error("手机号码释放异常" + e);
			return R.error(1, "手机号码释放异常" + e);
		}
	}

	/**
	 * 手机号码释放-省内下沉
	 *
	 * @param phone
	 * @param id
	 * @return
	 */
	@RequestMapping("/supRelasePhoneNbr")
	public R supRelasePhoneNbr(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();
			String phone = reqData.getString("phone");

			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String customerCard = customerInfo.getString("customerCard");
			String custId = customerInfo.getString("custId");

			if (StringUtils.isBlank(phone) || StringUtils.isBlank(customerCard)) {
				return R.error(1, "参数为空");
			}

			/**
			 * 参数封装
			 */
			JSONObject param = new JSONObject();
			param.put("phoneNbr", phone);
			param.put("certNumber", customerCard);
			param.put("lan_id", managerCityCode);
			param.put("staffCode", userModel.getStaffCode());
			param.put("saleBoxCode", userModel.getSaleBoxCode());
			param.put("custId", custId);// 此参数不用做释放接口
			
			/**
			 * 查询是否为历史号码
			 */
			if (phonePreRecService.updatePhoneInfo(param)) {
				return R.ok();
			}
			
			R restData = InterfaceUtil.supRelasePhoneNbr(param);
			if (0 == restData.getInteger("code")) {
				logger.info("用户{},释放手机号码 {} 成功", customerCard, phone);
				return R.ok("释放号码成功");
			} else {
				String resultMessage = restData.getString("message");
				logger.info("用户{},释放手机号码 {} 失败", customerCard, phone);
				return R.error(1, resultMessage);
			}
		} catch (Exception e) {
			logger.error("手机号码释放异常{}", reqData.getString("phone"), e);
			return R.error(1, "手机号码释放异常" + e);
		}
	}

	/**
	 * 根据身份证查询预约信息
	 * 
	 * @param model
	 * @param certNumber
	 * @param cityCode
	 * @return
	 */
	@RequestMapping("/custPreInfo")
	public R custPreInfo(@RequestBody JSONObject reqData) {
		UserModel userModel = userFile.getUserModel();
		JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);

		return InterfaceUtil.custPreInfo(customerInfo.getString("customerCard"), userModel.getCitycode());
	}

	/**
	 * 根据身份证查询预占号码-省内
	 * 
	 * @param model
	 * @param certNumber
	 * @param cityCode
	 * @return
	 */
	@RequestMapping("/supQueryPrePhoneNbrs")
	public R supQueryPrePhoneNbrs(@RequestBody JSONObject reqData) {
		UserModel userModel = userFile.getUserModel();
		JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);

		return InterfaceUtil.supQueryPrePhoneNbrs(customerInfo.getString("customerCard"), userModel.getCitycode());
	}

	/**
	 * 校验是否需要活体
	 * 
	 * @MethodName: getPersonCheckImgInfo
	 * @<NAME_EMAIL>
	 * @param reqData
	 * @return R
	 * @date 2025-03-29 10:29:36
	 */
	@RequestMapping("/getPersonCheckImgInfo")
	public R getPersonCheckImgInfo(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);

			if (StringUtils.isBlank(customerInfo.getString("custId"))) {
				return R.error(1, "客户ID参数为空");
			}

			reqData.put("custId", customerInfo.getString("custId"));
			reqData.put("lanId", userModel.getCitycode());
			reqData.put("orgId", userModel.getOrgId());
			reqData.put("staffId", userModel.getStaffId());
			reqData.put("token", "");
			return InterfaceUtil.getPersonCheckImgInfo(reqData);
		} catch (Exception e) {
			logger.error("校验是否需要活体异常" + e);
			return R.error(1, "校验是否需要活体异常" + e.getMessage());
		}
	}

	/**
	 * 是否需要订单预览视图接口
	 * 
	 * @MethodName: PREORDERCHECK
	 * @<NAME_EMAIL>
	 * @param reqData
	 * @return R
	 * @date 2025-03-29 10:48:42
	 */
	@RequestMapping("/PREORDERCHECK")
	public R PREORDERCHECK(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);

			if (StringUtils.isBlank(customerInfo.getString("custId"))) {
				return R.error(1, "客户ID参数为空");
			}

			reqData.put("custId", customerInfo.getString("custId"));
			reqData.put("lanId", userModel.getCitycode());
			reqData.put("orgId", userModel.getOrgId());
			reqData.put("staffId", userModel.getStaffId());
			reqData.put("token", reqData.getString("orderNo"));
			return InterfaceUtil.PREORDERCHECK(reqData);
		} catch (Exception e) {
			logger.error("是否需要订单预览视图异常" + e);
			return R.error(1, "是否需要订单预览视图异常" + e.getMessage());
		}
	}

	/**
	 * H5查询当前客户是否存在审批单
	 * 
	 * @MethodName: H5CANAPPROVEAPP
	 * @<NAME_EMAIL>
	 * @param reqData
	 * @return R
	 * @date 2025-03-29 11:00:44
	 */
	@RequestMapping("/H5CANAPPROVEAPP")
	public R H5CANAPPROVEAPP(@RequestBody JSONObject reqData) {
		// 记录处理开始时间
		long startTime = System.currentTimeMillis();
		String processNode = "查询当前客户是否存在审批单";
		String className = this.getClass().getName();
		String methodName = "H5CANAPPROVEAPP";
		String orderNo = reqData.getString("orderNo");
		
		// 初始化日志实体
		OrderProcessLogEntity logEntity = new OrderProcessLogEntity();
		// 设置基本信息
		logEntity.setOrderNo(orderNo);
		if (StringUtils.isNotBlank(orderNo)){
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			//根据订单号查询暂存id和87单号
			if (!Objects.isNull(orderInfo)){
				logEntity.setSceneInstId(orderInfo.getSceneInstIdList());
				logEntity.setCustOrderId(orderInfo.getCustOrderIdList());
			}
		}
		logEntity.setProcessNode(processNode);
		logEntity.setClassName(className);
		logEntity.setMethodName(methodName);
		logEntity.setRequestParams(reqData != null ? reqData.toString() : null);
		logEntity.setCreateTime(new Date());
		
		try {
			UserModel userModel = userFile.getUserModel();
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);

			if (StringUtils.isBlank(customerInfo.getString("custId"))) {
				// 设置错误信息
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("客户ID参数为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "客户ID参数为空");
			}
			
			// 设置操作人信息
			if (userModel != null) {
				logEntity.setOperatorId(userModel.getId());
				logEntity.setOperatorStaffId(userModel.getStaffId());
				logEntity.setOperatorName(userModel.getUserName());
			}
			
			reqData.put("regionId", userModel.getRegionId());
			reqData.put("custId", customerInfo.getString("custId"));
			reqData.put("lanId", userModel.getCitycode());
			reqData.put("staffId", userModel.getStaffId());
			reqData.put("token", "");
			
			// 设置接口请求参数
			logEntity.setInterfaceRequestParams(reqData.toString());
			
			// 调用接口
			R result = InterfaceUtil.H5CANAPPROVEAPP(reqData);
			
			// 设置响应结果
			if (result != null) {
				logEntity.setResponseResult(result.toString());
				
				// 根据结果设置状态和错误信息
				if (result.getInteger("code") == 0) {
					// 设置成功状态
					logEntity.setStatus(0); // 0-成功
				} else {
					// 设置失败状态和错误信息
					logEntity.setStatus(1); // 1-失败
					logEntity.setErrorMsg(result.getString("message"));
				}
			} else {
				// 接口返回为空
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("接口返回结果为空");
			}
			
			// 记录执行时间
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("日志记录异常", logEx);
			}
			
			return result;
		} catch (Exception e) {
			// 设置异常信息
			logEntity.setStatus(1); // 1-失败
			logEntity.setErrorMsg("H5查询当前客户是否存在审批单异常: " + e.getMessage());
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			logger.error("H5查询当前客户是否存在审批单异常" + e);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("异常日志记录失败", logEx);
			}
			
			return R.error(1, "H5查询当前客户是否存在审批单异常" + e);
		}
	}

	/**
	 * 获取固话列表
	 *
	 * @param token
	 * @param regionId   区域编码
	 * @param codeLevel  号码级别
	 * @param phoneNoKey 号段
	 * @return
	 */
	@RequestMapping("/loadTelephone")
	public R loadTelephone(@RequestBody JSONObject reqData) {

		if (StringUtils.isBlank(reqData.getString("regionId")) || StringUtils.isBlank(reqData.getString("codeLevel"))) {
			logger.info("甩单获取固话列表接口入参为空");
			return R.error(1, "参数为空");
		}
		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();

		JSONArray telePhoneList = new JSONArray();
		String phoneNoKey = reqData.getString("phoneNoKey");
		String regionId = reqData.getString("regionId");
		String codeLevel = reqData.getString("codeLevel");
		String orgId = userModel.getOrgId();

		String returnCount = "20";
		if (phoneNoKey == "" || phoneNoKey == null) {
			phoneNoKey = "%";
		}
		telePhoneList = InterfaceUtil.queryGuHDcoos(managerCityCode, regionId, returnCount, phoneNoKey, codeLevel,
				orgId);
		for (int i = 0; i < telePhoneList.size(); i++) {
			JSONObject telePhoneObject = telePhoneList.getJSONObject(i);
			telePhoneObject.put("checked", false);
			telePhoneObject.put("id", i);
		}
		return R.ok().put("telePhoneList", telePhoneList);
	}

	/**
	 * 预占固话
	 *
	 * @param token
	 * @param regionId     区域编码
	 * @param phoneNo      固话号码
	 * @param customerCard 客户身份证号
	 * @return
	 */
	@RequestMapping("/preTelePhone")
	public R preTelePhone(@RequestBody JSONObject reqData) {
		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();
		String regionId = reqData.getString("regionId");
		String phoneNo = reqData.getString("phoneNo");
		String orgId = userModel.getOrgId();
		String staffCode = userModel.getStaffCode();

		/**
		 * 根据读证类型获取客户信息//读证类型 1: 客户证件 2: 使用人证件 3: 经办人证件
		 */
		JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
		String customerCard = customerInfo.getString("customerCard");

		if (StringUtils.isBlank(regionId) || StringUtils.isBlank(phoneNo) || StringUtils.isBlank(customerCard)) {
			logger.info("甩单工具预占固话接口入参为空");
			return R.error(1, "参数为空");
		}

		JSONObject resultData = InterfaceUtil.preGuHDcoos(managerCityCode, regionId, phoneNo, customerCard, orgId,
				staffCode);
		if ("0".equals(resultData.getString("code"))) {
			return R.ok();
		} else {
			return R.error(1, resultData.getString("message"));
		}
	}

	/**
	 * 查询标准地址按地址名称（模糊查询）
	 * 
	 * @param token
	 * @param addressName  地址名称
	 * @param addrCount    查询条数
	 * @param regionCode   区域id，具体到市县
	 * @param addressLevel 地址等级
	 * @return
	 */
	@RequestMapping("/qryResInfoAddrSearch")
	public R qryResInfoAddrSearchDcoos(@RequestBody JSONObject reqData) {
		try {
			JSONObject restData = InterfaceUtil.qryResInfoAddrSearchDcoos(reqData.getString("addressName"),
					reqData.getString("addrCount"), reqData.getString("regionCode"), reqData.getString("addressLevel"));
			if ("0".equals(restData.getString("code"))) {
				if ("0".equals(restData.getJSONObject("data").getString("cou"))) {
					return R.error(1, "未查询到地址信息");
				}
				JSONArray addressList = restData.getJSONObject("data").getJSONArray("addrs");
				for (int i = 0; i < addressList.size(); i++) {
					JSONObject addressObject = addressList.getJSONObject(i);
					addressObject.put("checked", false);
					addressObject.put("index", i);
				}
				return R.ok().put("addressList", addressList);
			} else {
				return R.error(1, "查询标准地址按地址名称（模糊查询）");
			}
		} catch (Exception e) {
			logger.error("查询标准地址按地址名称（模糊查询）异常:", e);
			return R.error(1, "查询标准地址按地址名称（模糊查询）");
		}
	}

	/**
	 * 判断地址是否具备宽带安装资格
	 * 
	 * @param request
	 * @param addr_id
	 * @param model
	 * @return
	 */
	@RequestMapping("/qryResources")
	public R qryResources(@RequestBody JSONObject reqData) {
		if (StringUtils.isBlank(reqData.getString("addrId"))) {
			logger.info("资源预判参数为空");
			return R.error(1, "九级地址id为空");
		}
		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();

		JSONObject resultMap = InterfaceUtil.qryResForNetHallDcoos(reqData.getString("addrId"), managerCityCode);
		if (!"0".equals(resultMap.getString("code"))) {
			return R.error(1, resultMap.getString("message"));
		} else {
			return R.ok().put("resourceArray", resultMap.getJSONArray("data"));
		}
	}

	/**
	 * 资源返回是否支持500M
	 * 
	 * @param addrId
	 * @return
	 */
	@RequestMapping("/isSupport500M")
	public R isSupport500M(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();
			JSONObject restData = InterfaceUtil.isSupport500MDcoos(reqData.getString("addrId"),
					reqData.getString("isCity"), managerCityCode);
			if ("0".equals(restData.getString("code"))) {
				return R.ok().put("data", restData.getString("data"));
			} else {
				return R.error(1, restData.getString("message"));
			}
		} catch (Exception e) {
			logger.error("HNBSS掌上销查询查验结果(携号转网结果查询)接口异常：", e);
			return R.error(1, "资源返回是否支持500M接口异常1");
		}
	}

	/**
	 * BSS30-根据缆机工号模糊查询缆机信息供预受理使用[V1.0,2019-08-02]
	 *
	 * @param token
	 * @param staffCodeOrName 工号或者姓名
	 * @return
	 */
	@RequestMapping("/qrytPartyInfoByPartyCode")
	public R qrytPartyInfoByPartyCode(@RequestBody JSONObject reqData) {
		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();
		String staffCodeOrName = reqData.getString("staffCodeOrName");
		try {
			boolean isChinese = StringUtil.isChinese(staffCodeOrName);
			if (isChinese) {// 姓名
				JSONObject qrytPartyInfoByPartyName = InterfaceUtil.qrytPartyInfoByPartyName(staffCodeOrName,
						managerCityCode);
				if ("0".equals(qrytPartyInfoByPartyName.getString("code"))) {
					JSONArray dataArray = qrytPartyInfoByPartyName.getJSONArray("data");
					if (dataArray != null) {
						for (int i = 0; i < dataArray.size(); i++) {
							JSONObject obj = dataArray.getJSONObject(i);
							obj.put("checked", false);
							obj.put("id", i);
						}
					}
					return R.ok().put("data", dataArray);
				} else {
					return R.error(1, qrytPartyInfoByPartyName.getString("message"));
				}
			} else {// 工号
				JSONObject qrytPartyInfoByPartyCode = InterfaceUtil.qrytPartyInfoByPartyCode(staffCodeOrName,
						managerCityCode);
				if ("0".equals(qrytPartyInfoByPartyCode.getString("code"))) {
					JSONArray dataArray = qrytPartyInfoByPartyCode.getJSONArray("data");
					if (dataArray != null) {
						for (int i = 0; i < dataArray.size(); i++) {
							JSONObject obj = dataArray.getJSONObject(i);
							obj.put("checked", false);
							obj.put("id", i);
						}
					}
					return R.ok().put("data", dataArray);
				} else {
					return R.error(1, qrytPartyInfoByPartyCode.getString("message"));
				}
			}
		} catch (Exception e) {
			logger.error("========根据缆机工号模糊查询缆机信息异常========", e);
			return R.error(1, "查询信息失败");
		}
	}

	/**
	 * 获取支付信息
	 *
	 * @param token
	 * @param payType
	 * @return
	 */
	@RequestMapping("/getPayData")
	public R getPayData(@RequestBody JSONObject reqData) {
		// 记录处理开始时间
		long startTime = System.currentTimeMillis();
		String processNode = "获取支付信息";
		String className = this.getClass().getName();
		String methodName = "getPayData";
		
		// 初始化日志实体
		OrderProcessLogEntity logEntity = new OrderProcessLogEntity();
		// 设置基本信息
		logEntity.setOrderNo(reqData.getString("orderNo"));
		if (StringUtils.isNotBlank(logEntity.getOrderNo())){
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(logEntity.getOrderNo());
			//根据订单号查询暂存id和87单号
			if (!Objects.isNull(orderInfo)){
				logEntity.setSceneInstId(orderInfo.getSceneInstIdList());
				logEntity.setCustOrderId(orderInfo.getCustOrderIdList());
			}
		}
		logEntity.setProcessNode(processNode);
		logEntity.setClassName(className);
		logEntity.setMethodName(methodName);
		logEntity.setRequestParams(reqData != null ? reqData.toString() : null);
		logEntity.setCreateTime(new Date());
		
		try {
			logger.info("获取支付信息入参》》》" + reqData.toJSONString());

			/**
			 * 1、获取登录工号信息
			 */
			UserModel hnzsxUserEntity = userFile.getUserModel();
			
			// 设置操作人信息
			if (hnzsxUserEntity != null) {
				logEntity.setOperatorId(hnzsxUserEntity.getId());
				logEntity.setOperatorStaffId(hnzsxUserEntity.getStaffId());
				logEntity.setOperatorName(hnzsxUserEntity.getUserName());
			}

			/**
			 * 2、根据读证类型获取客户信息，读证类型 1: 客户证件 2: 使用人证件 3: 经办人证件
			 */
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String custId = customerInfo.getString("custId");
			reqData.put("custId", custId);

			/**
			 * 3、提取请求参数
			 */
			String orderPrice = reqData.getString("orderPrice");// 订单金额
			String customerContactPhone = reqData.getString("customerContactPhone");// 客户联系号码
			String payType = reqData.getString("payType");// 支付方式
			String crmOrderId = reqData.getString("crmOrderId");// CRM订单号
			String coupon_flag = reqData.getString("coupon_flag");//
			String orderNo = reqData.getString("orderNo");// 即时受理系统订单号
			String pushInvoiceWay = reqData.getString("pushInvoiceWay");// 发票类型 0 ：不开票 1：普票 2：收据
			String receptInvoiceEmail = reqData.getString("receptInvoiceEmail");// 发票推送邮箱

			/**
			 * 4、请求参数缺失订单号拦截
			 */
			if (StringUtils.isBlank(orderNo)) {
				// 设置错误信息
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("参数校验失败：订单号为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "参数校验失败");
			}

			/**
			 * 获取订单信息，同步订单信息到掌上销
			 */
			LambdaQueryWrapper<Hnzsxh5OrderInfoEntity> objectQueryWrapper = new LambdaQueryWrapper<>();
			objectQueryWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo);
			Hnzsxh5OrderInfoEntity hnzsxh5OrderInfoEntity = hnzsxh5OrderInfoService.getOne(objectQueryWrapper);
			if (Objects.isNull(hnzsxh5OrderInfoEntity)) {
				return R.error(1, "订单不存在");
			}

			/**
			 * 订单已支付成功拦截
			 *
			 */
			if (null != hnzsxh5OrderInfoEntity.getPaymentStatus()
					&& (PaymentState.SUCCESS.getCode() == hnzsxh5OrderInfoEntity.getPaymentStatus()
							|| PaymentState.NO_PAY.getCode() == hnzsxh5OrderInfoEntity.getPaymentStatus())) {
				return R.error(1, "该笔订单已完成支付，请勿重复提交");
			}

			/**
			 * 同步订单信息到掌上销
			 */
			if (hnzsxh5OrderInfoEntity.getZsxSyncStatus() == null || 1 != hnzsxh5OrderInfoEntity.getZsxSyncStatus()) {
				hnzsxh5OrderInfoService.synAddOrderData(hnzsxh5OrderInfoEntity, customerInfo);
			}
			/**
			 * 订单金额为0，直接过费
			 */
			BigDecimal totalAmount = new BigDecimal("0");// 所有暂存单总金额
			List<String> sceneInstIdList = StringUtil.stringToList(hnzsxh5OrderInfoEntity.getSceneInstIdList());
			if (!CollectionUtils.isEmpty(sceneInstIdList)) {
				// 查询每个暂存单的详细信息
				for (String sceneInstId : sceneInstIdList) {
					// 构建查询参数
					JSONObject params = new JSONObject();
					params.put("serviceAction", "loadScene");
					JSONArray acceptInfo = new JSONArray();
					JSONObject sceneInfo = new JSONObject();
					sceneInfo.put("sceneInstId", sceneInstId);
					acceptInfo.add(sceneInfo);
					params.put("acceptInfo", acceptInfo);
					params.put("userModel", JSON.toJSONString(hnzsxUserEntity));
					// 调用接口获取暂存单详情
					R dataJson = strategyContext.handleStrategy("loadScene", params);
					if (dataJson.getInteger("code") != 0) {
						log.error("查询暂存单详情失败，sceneInstId: {}, 错误信息: {}", sceneInstId, dataJson.getString("message"));
					}
					// 解析json
					JSONObject data = dataJson.getJSONObject("data");
					if (Objects.isNull(data)) {
						return R.error(1, "未查询到暂存单信息");
					}
					JSONObject h5CustOrder = data.getJSONObject("h5CustOrder");
					if (Objects.isNull(h5CustOrder)) {
						return R.error(1, "未获取到支付信息");
					}
					String total_Amount = h5CustOrder.getString("totalAmount");
					// 判断是否为0
					BigDecimal amount = new BigDecimal(total_Amount);
					totalAmount = totalAmount.add(amount);// 总金额累加
					if (amount.compareTo(BigDecimal.ZERO) == 0) {
						// 收费确认
						reqData.put("hnzsxh5OrderInfoEntity", hnzsxh5OrderInfoEntity);
						reqData.put("payType", "noPay");// 支付方式（tx:预存金 ewm：二维码 noPay：无需支付）
						R result = strategyContext.handleStrategy("AdvanceDepositOr0YuanH5CommitFee", reqData);
						if (result != null && 0 == result.getInteger("code")) {
							hnzsxh5OrderInfoEntity.setTotalAmount("0");
							hnzsxh5OrderInfoEntity.setUpdatedDate(new Date());
							hnzsxh5OrderInfoService.updateById(hnzsxh5OrderInfoEntity);
							logger.info("更新订单金额信息，订单号为：{}", hnzsxh5OrderInfoEntity.getOrderNo());
							return R.ok(666, "该笔订单支付金额为0，直接确认收费");
						} else {
							return R.error(999, "该笔订单确认收费失败");
						}
					}
				}
			}

			/**
			 * 初始化支付流水单数据
			 */
			Hnzsxh5PaymentInfoEntity paymentInfoEntity = new Hnzsxh5PaymentInfoEntity();
			paymentInfoEntity.setOrderNo(orderNo);
			paymentInfoEntity.setCustomerContactPhone(customerContactPhone);
			paymentInfoEntity.setPayType(payType);
			paymentInfoEntity.setCrmorderId(crmOrderId);
			paymentInfoEntity.setCouponFlag(coupon_flag);
			paymentInfoEntity.setCustId(custId);
			paymentInfoEntity.setStaffId(hnzsxUserEntity.getStaffId());
			paymentInfoEntity.setStaffCode(hnzsxUserEntity.getStaffCode());
			paymentInfoEntity.setOrgId(hnzsxUserEntity.getOrgId());
			paymentInfoEntity.setLanId(hnzsxUserEntity.getCitycode());
			paymentInfoEntity.setOrgSubtype(hnzsxUserEntity.getOrgSubtype());
			paymentInfoService.savePaymentInfo(paymentInfoEntity);

			if (payType.equals("tx")) {
				// 查询风险金余额
				R queryAgentDepositAmount = InterfaceUtil.queryAgentDepositAmount(hnzsxUserEntity.getStaffId());
				if (0 == queryAgentDepositAmount.getInteger("code")) {
					// 暂存单总金额大于预存金时
					if (totalAmount.compareTo(queryAgentDepositAmount.getBigDecimal("balance")) > 0) {
						// 流水信息更新
						paymentInfoEntity.setPrepayStatus(PrepayStatus.AGENT_DEPOSIT_AMOUNT_FAILED.getCode());
						paymentInfoEntity.setPrepayDate(new Date());
						paymentInfoEntity.setTotalFee(totalAmount.toString());
						paymentInfoService.updatePaymentInfo(paymentInfoEntity);

						return R.error(1, "您的风险金余额不足" + totalAmount + "，请选择二维码支付");
					} else {
						// 流水信息更新
						paymentInfoEntity.setPrepayStatus(PrepayStatus.AGENT_DEPOSIT_AMOUNT_SUCCESS.getCode());
						paymentInfoEntity.setPrepayDate(new Date());
						paymentInfoEntity.setTotalFee(totalAmount.toString());
						paymentInfoService.updatePaymentInfo(paymentInfoEntity);

						// 订单状态修改
						LambdaUpdateWrapper<Hnzsxh5OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
						updateWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo)
								.set(Hnzsxh5OrderInfoEntity::getUpdatedDate, new Date()) // 更新修改时间
								.set(Hnzsxh5OrderInfoEntity::getState, 3) // 订单状态(0:完结，1：暂存单，2：订单确认，3：未支付，4：已支付，5：支付失败，6：CRM订单收费确认失败，-1:失败)
								.set(Hnzsxh5OrderInfoEntity::getPushInvoiceWay, pushInvoiceWay)
								.set(Hnzsxh5OrderInfoEntity::getReceptInvoiceEmail, receptInvoiceEmail)
								.set(Hnzsxh5OrderInfoEntity::getTotalAmount, totalAmount.toString());
						hnzsxh5OrderInfoService.update(updateWrapper);

						return queryAgentDepositAmount;
					}
				} else {
					return queryAgentDepositAmount;
				}
			} else {
				logger.info("获取支付信息入参2222");
				// 获取支付二维码
				Map<String, String> paramMap = new HashMap<>();
				paramMap.put("totalFee", orderPrice);// 订单金额
				paramMap.put("notifyUrl", "http://134.176.102.33:8081/api/openapi/zsxapp/appRouter");// 支付中心回调地址
				paramMap.put("staffId", hnzsxUserEntity.getStaffId());
				paramMap.put("orgId", hnzsxUserEntity.getOrgId());
				paramMap.put("lanId", hnzsxUserEntity.getCitycode());
				paramMap.put("crmOrderId", crmOrderId);
				paramMap.put("custId", custId);
				paramMap.put("customerContactPhone", customerContactPhone);
//				String payUserSerial = DateUtils.format(DateUtils.DATE_YANGIDN) + new Random().nextInt(1000000);
				String thisMonthDate = new SimpleDateFormat("yyMMdd").format(new Date());// 当月的日期
				String payUserSerial = new Random().nextInt(100) + DateUtils.format(thisMonthDate)
						+ new Random().nextInt(10000000);
				paramMap.put("payUserSerial", payUserSerial);
				paramMap.put("orgSubtype", hnzsxUserEntity.getOrgSubtype());
				paramMap.put("coupon_flag", coupon_flag);
				paramMap.put("orderNo", orderNo);
				paramMap.put("goodsDesc", "#CHANNEL#HNZSXH5JSSL#CHANNEL#;#DESC#" + orderNo + "#DESC#");

				// 设置支付流水信息数据
				paymentInfoEntity.setNotifyUrl(paramMap.get("notifyUrl"));
				paymentInfoEntity.setPayUserSerial(payUserSerial);
				R zsxPayUrl = InterfaceUtil.zsxPayUrl(paramMap);
				// 获取支付二维码成功
				if (0 == zsxPayUrl.getInteger("code")) {
					logger.info("更新预下单状态为获取二维码成功，调用方流水号为：{}", payUserSerial);
					paymentInfoEntity.setPrepayStatus(PrepayStatus.QR_CODE_SUCCESS.getCode());
					paymentInfoEntity.setPrepayDate(new Date());
					paymentInfoEntity.setTotalFee(totalAmount.toString());
					paymentInfoService.updatePaymentInfo(paymentInfoEntity);
					LambdaUpdateWrapper<Hnzsxh5OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
					updateWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo)
							.set(Hnzsxh5OrderInfoEntity::getUpdatedDate, new Date()) // 更新修改时间
							.set(Hnzsxh5OrderInfoEntity::getState, 3) // 订单状态(0:完结，1：暂存单，2：订单确认，3：未支付，4：已支付，5：支付失败，6：CRM订单收费确认失败，-1:失败)
							.set(Hnzsxh5OrderInfoEntity::getPushInvoiceWay, pushInvoiceWay)
							.set(Hnzsxh5OrderInfoEntity::getReceptInvoiceEmail, receptInvoiceEmail);
					hnzsxh5OrderInfoService.update(updateWrapper);

					return zsxPayUrl;
				} else {
					logger.info("更新预下单状态为获取二维码失败，调用方流水号为：{}", payUserSerial);
					paymentInfoEntity.setPrepayStatus(PrepayStatus.QR_CODE_FAILED.getCode());
					paymentInfoEntity.setPrepayDate(new Date());
					paymentInfoEntity.setTotalFee(totalAmount.toString());
					paymentInfoService.updatePaymentInfo(paymentInfoEntity);
					return zsxPayUrl;
				}
			}
		} catch (Exception e) {
			// 设置异常信息
			logEntity.setStatus(1); // 1-失败
			logEntity.setErrorMsg("获取支付信息异常: " + e.getMessage());
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			logger.error("获取支付信息异常", e);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("异常日志记录失败", logEx);
			}
			
			return R.error(1, "获取支付信息异常：" + e.getMessage());
		}
	}

	/**
	 * 预存金支付确认
	 *
	 * @param token
	 * @return
	 */
	@RequestMapping("/riskFundPay")
	public R riskFundPay(@RequestBody JSONObject reqData) {
		// 记录处理开始时间
		long startTime = System.currentTimeMillis();
		String processNode = "预存金支付确认";
		String className = this.getClass().getName();
		String methodName = "riskFundPay";
		String orderNo = reqData.getString("orderNo");
		
		// 初始化日志实体
		OrderProcessLogEntity logEntity = new OrderProcessLogEntity();
		// 设置基本信息
		logEntity.setOrderNo(orderNo);
		if (StringUtils.isNotBlank(orderNo)){
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			//根据订单号查询暂存id和87单号
			if (!Objects.isNull(orderInfo)){
				logEntity.setSceneInstId(orderInfo.getSceneInstIdList());
				logEntity.setCustOrderId(orderInfo.getCustOrderIdList());
			}
		}
		logEntity.setProcessNode(processNode);
		logEntity.setClassName(className);
		logEntity.setMethodName(methodName);
		logEntity.setRequestParams(reqData != null ? reqData.toString() : null);
		logEntity.setCreateTime(new Date());
		
		try {
			// 设置接口请求参数
			logEntity.setInterfaceRequestParams(reqData.toString());
			
			/**
			 * 根据读证类型获取客户信息，读证类型 1: 客户证件 2: 使用人证件 3: 经办人证件
			 */
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String custId = customerInfo.getString("custId");

			/**
			 * 获取订单信息，同步订单信息到掌上销
			 */
			LambdaQueryWrapper<Hnzsxh5OrderInfoEntity> objectQueryWrapper = new LambdaQueryWrapper<>();
			objectQueryWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo);
			Hnzsxh5OrderInfoEntity hnzsxh5OrderInfoEntity = hnzsxh5OrderInfoService.getOne(objectQueryWrapper);
			if (Objects.isNull(hnzsxh5OrderInfoEntity)) {
				return R.error(1, "订单不存在");
			}

			// 根据订单号查询支付流水
			Hnzsxh5PaymentInfoEntity paymentInfoEntity = paymentInfoService.getPaymentInfoByOrderNo(orderNo);

			/**
			 * 收费确认
			 */
			BigDecimal yuan = new BigDecimal(hnzsxh5OrderInfoEntity.getTotalAmount()); // 将字符串转换为 BigDecimal 对象
			BigDecimal fen = yuan.multiply(new BigDecimal("100")); // 乘以 100 进行分转换
			fen = fen.setScale(0, BigDecimal.ROUND_DOWN);
			reqData.put("custId", custId);
//			reqData.put("paytacheType", "100000");// 预存金支付方式
			reqData.put("payChannelType", "100000");// 预存金支付方式
			reqData.put("payType", "tx");// 支付方式（tx:预存金 ewm：二维码 noPay：无需支付）
			reqData.put("cashFee", fen.toString());// 费用
			reqData.put("hnzsxh5OrderInfoEntity", hnzsxh5OrderInfoEntity);
			R result = strategyContext.handleStrategy("AdvanceDepositOr0YuanH5CommitFee", reqData);
			if (result != null && 0 == result.getInteger("code")) {
				paymentInfoService.updatePaymentState(paymentInfoEntity.getId(), PaymentState.SUCCESS.getCode());
				logger.info("更新流水支付信息-支付状态为成功，订单号：{}", orderNo);

				// 记录日志
				logEntity.setStatus(0); // 0-成功
				logEntity.setResponseResult(result.toString());
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return result;
			} else {
				paymentInfoService.updatePaymentState(paymentInfoEntity.getId(), PaymentState.FAILURE.getCode());
				logger.info("更新流水支付信息-支付状态为失败，订单号：{}", orderNo);
				
				// 记录日志
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg(result != null ? result.getString("message") : "支付确认失败");
				logEntity.setResponseResult(result != null ? result.toString() : "");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return result;
			}
		} catch (Exception e) {
			// 设置异常信息
			logEntity.setStatus(1); // 1-失败
			logEntity.setErrorMsg("预存金支付异常: " + e.getMessage());
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			logger.error("预存金支付异常", e);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("异常日志记录失败", logEx);
			}
			
			return R.error(1, "预存金支付异常：" + e.getMessage());
		}
	}

	/**
	 * 人证比对
	 *
	 * @param phone
	 * @return
	 */
	@RequestMapping("/chkfaceVerify")
	public R chkfaceVerify(@RequestBody JSONObject reqData) {

		// 获取登录工号信息
		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();

		// 上传免冠照并保存免冠照
		String identityCardImageBase64 = reqData.getString("identityCardImageBase64");// 免冠照base64串
		String identityCardImagePath = "";// 免冠照ceph存储路径
		JSONObject cephUploadPrame = new JSONObject();
		cephUploadPrame.put("base64Image", identityCardImageBase64);
		cephUploadPrame.put("bucketPhoto", "hnzsxbusiness");
		cephUploadPrame.put("maxSizeKb", 300);
		cephUploadPrame.put("imageSorceName", "即时受理H5");
		R cephUploadBase64File = uploadFileService.cephUploadBase64File(cephUploadPrame);
		if (0 == cephUploadBase64File.getInteger("code")) {
			identityCardImagePath = cephUploadBase64File.getString("path");
		} else {
			R.error(1, "上传免冠照失败，请重新检测活体验证。");
		}

		/**
		 * 根据readType获取【客户、经办人、使用人】信息
		 */
		JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
		String customerName = customerInfo.getString("customerName");// 证件姓名
		String customerCard = customerInfo.getString("customerCard");// 证件号码
		String customerCardImgPath = customerInfo.getString("imageIdcard");// 证件二寸照ceph存储路径

		JSONObject params = new JSONObject();
		params.put("cityCode", managerCityCode);
		params.put("identityCardImage3", identityCardImagePath);// 免冠照
		params.put("image_idcard", customerCardImgPath);// 二寸照
		params.put("customerName", customerName);
		params.put("customerCard", customerCard);

		try {
			// 获取订单号
			String orderNo = reqData.getString("orderNo");
			String accountNumber = reqData.getString("accountNumber");
			R chkfaceVerify = InterfaceUtil.chkfaceVerify(awzS3Service.getRandomBean(), params);// {"code":0,"data":"95.53","message":"成功"}
			String similarity = chkfaceVerify.getString("data");
			if (0 == chkfaceVerify.getIntValue("code")) {
				// 1.比对成功，根据订单号和账号更新订单活体认证数据
				try {

					Double similarityS = Double.valueOf(chkfaceVerify.getString("data"));
					if (similarityS < 65) {
						// 查询活体认证数据
						Hnzsxh5OrderLivingBodyCertiInfoEntity entity = hnzsxh5OrderLivingBodyCertiInfoService
								.getByOrderNoAndAccount(orderNo, accountNumber);
						if (entity != null) {
							// 更新相似度和状态
							entity.setSimilarity(similarity);
							entity.setIsChkfaceVerify(2); // 人证比对不通过
							entity.setUpdateTime(new Date());
							hnzsxh5OrderLivingBodyCertiInfoService.updateById(entity);
							// 更新订单活体人证状态为失败
							LambdaUpdateWrapper<Hnzsxh5OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
							updateWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo)
									.set(Hnzsxh5OrderInfoEntity::getLiveStatus, 2) // 设置状态
									.set(Hnzsxh5OrderInfoEntity::getUpdatedDate, new Date()); // 更新修改时间
							hnzsxh5OrderInfoService.update(updateWrapper);
							logger.info("订单 {} 账号 {} 人证比对失败并更新活体认证相似度数据", orderNo, accountNumber);
						} else {
							logger.warn("未找到订单 {} 账号 {} 的活体认证数据", orderNo, accountNumber);
						}
						return R.error(1, "人证比对校验未通过，请重新上传！");
					} else {
						// 查询活体认证数据
						Hnzsxh5OrderLivingBodyCertiInfoEntity entity = hnzsxh5OrderLivingBodyCertiInfoService
								.getByOrderNoAndAccount(orderNo, accountNumber);
						if (entity != null) {
							// 更新相似度和状态
							entity.setSimilarity(similarity);
							entity.setIsChkfaceVerify(1); // 人证比对通过
							entity.setUpdateTime(new Date());
							hnzsxh5OrderLivingBodyCertiInfoService.updateById(entity);
							// 更新订单活体人证状态为成功
							LambdaUpdateWrapper<Hnzsxh5OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
							updateWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo)
									.set(Hnzsxh5OrderInfoEntity::getLiveStatus, 1) // 设置状态
									.set(Hnzsxh5OrderInfoEntity::getUpdatedDate, new Date()); // 更新修改时间
							hnzsxh5OrderInfoService.update(updateWrapper);
							logger.info("订单 {} 账号 {} 人证比对成功并更新活体认证相似度数据", orderNo, accountNumber);
						} else {
							logger.warn("未找到订单 {} 账号 {} 的活体认证数据", orderNo, accountNumber);
						}
					}
				} catch (Exception e) {
					logger.error("更新订单 {} 账号 {} 活体认证相似度数据异常", orderNo, accountNumber, e);
				}

				reqData.put("similarity", chkfaceVerify.getString("data"));// 人证比对相似度
				reqData.put("identityCardImagePath", identityCardImagePath);// 免冠照ceph存储路径
				reqData.put("customerCardImgPath", customerCardImgPath);// 个人客户二寸照ceph存储路径
				reqData.put("usersCardImgPath", "");// 使用人二寸照ceph存储路径
				reqData.put("agentCardImgPath", "");// 经办人二寸照ceph存储路径

				R synCustCertInfoToCrm = synCustCertInfoToCrm(reqData, userModel, customerInfo, orderNo);
				return synCustCertInfoToCrm;
			} else {
				// 1.比对失败，根据订单号和账号更新订单活体认证数据
				try {
					// 查询活体认证数据
					Hnzsxh5OrderLivingBodyCertiInfoEntity entity = hnzsxh5OrderLivingBodyCertiInfoService
							.getByOrderNoAndAccount(orderNo, accountNumber);
					if (entity != null) {
						// 更新相似度和状态
						entity.setSimilarity(similarity);
						entity.setIsChkfaceVerify(2); // 人证比对不通过
						entity.setUpdateTime(new Date());
						hnzsxh5OrderLivingBodyCertiInfoService.updateById(entity);
						// 更新订单活体人证状态为失败
						LambdaUpdateWrapper<Hnzsxh5OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
						updateWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo)
								.set(Hnzsxh5OrderInfoEntity::getLiveStatus, 2) // 设置状态
								.set(Hnzsxh5OrderInfoEntity::getUpdatedDate, new Date()); // 更新修改时间
						hnzsxh5OrderInfoService.update(updateWrapper);
						logger.info("订单 {} 账号 {} 人证比对失败并更新活体认证相似度数据", orderNo, accountNumber);
					} else {
						logger.warn("未找到订单 {} 账号 {} 的活体认证数据", orderNo, accountNumber);
					}
				} catch (Exception e) {
					logger.error("更新订单 {} 活体认证相似度数据异常", orderNo, e);
				}

				return chkfaceVerify;
			}
		} catch (IOException e) {
			logger.error("人证比对===" + e.getMessage());
			return R.error(1, "人证比对异常：" + e.getMessage());
		}
	}

	/**
	 * 同步人证比对照片给CRM
	 * 
	 * @param reqData
	 * @return JSONObject
	 * @date 2025-04-12 05:57:30
	 */
	public R synCustCertInfoToCrm(JSONObject reqData, UserModel userModel, JSONObject customerInfo, String orderNo) {
		try {

			JSONObject custCertData = new JSONObject();

			String managerCityCode = userModel.getCitycode();// 地市编码
			String customerType = reqData.getString("customerType");// 客户分群1:个人客户；2:企业客户
			String similarity = reqData.getString("similarity");// 人证比对相似度
			String crmOrderId = reqData.getString("crmOrderId");// crm订单号
			String custId = customerInfo.getString("custId");// 读证用户的custId

			// 同步给crm的照片信息
			String identityCardImage3 = reqData.getString("identityCardImagePath");// 免冠照ceph存储路径

			custCertData.put("image_best", "");// 人证比对免冠照
			custCertData.put("image_idcard", "");// 个人客户份证免冠二寸照
			custCertData.put("image_usersidcard", "");// 使用人身份证免冠二寸照
			custCertData.put("image_agentidcard", "");// 经办人身份证免冠二寸照

			if ("1".equals(customerType)) {// 1:个人客户
				custCertData.put("image_idcard", reqData.getString("customerCardImgPath"));// 个人客户二寸照ceph存储路径
			} else if ("2".equals(customerType)) {// 2:企业客户
				if (customerInfo != null) {
					custCertData.put("image_usersidcard", reqData.getString("usersCardImgPath"));// 使用人身份证二寸照ceph存储路径
				}
				if (customerInfo != null) {
					custCertData.put("image_agentidcard", reqData.getString("agentCardImgPath"));// 经办人身份证二寸照ceph存储路径
				}
			}

			custCertData.put("customerCard", customerInfo.getString("customerCard"));// 证件号码
			custCertData.put("lan_id", managerCityCode);// 地市编码
			custCertData.put("manual_staffcode", userModel.getStaffCode());// 工号
			custCertData.put("staffId", userModel.getStaffId());// 工号ID
			custCertData.put("manual_staffname", userModel.getUserName());// 工号姓名
			custCertData.put("transactionid", "");
			custCertData.put("custType", customerType);// 客户分群
			custCertData.put("similarity", similarity);// 人证比对相似度
			custCertData.put("image_best", identityCardImage3);// 人证比对免冠照

			// 一号一拍开关，要从商品中获取
			int isOneBeat = 2;// 默认为关闭，是否为一号一拍数据1、是 2、否
			Hnzsxh5OrderInfoEntity orderByOrderNo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			if (!Objects.isNull(orderByOrderNo)) {
				HnzsxH5GoodsInfo goodsInfoById = goodsService.getById(orderByOrderNo.getGoodsId());
				if (!Objects.isNull(goodsInfoById)) {
					isOneBeat = goodsInfoById.getIsOneBeat();
				}
			}

			custCertData.put("isOneBeat", isOneBeat);// 是否一号一拍
			custCertData.put("cust_order_id", orderByOrderNo.getCustOrderIdList());// CRM系统orderId
			String accountNumber = "";
			// 一号一拍
			if (1 == isOneBeat) {
				accountNumber = reqData.getString("accountNumber");// 一号一拍账号
				custCertData.put("acc_num", accountNumber);
			} else {
				custCertData.put("acc_num", "");
			}

			R rests = InterfaceUtil.synCustCertInfoToCrm(awzS3Service.getRandomBean(), custCertData);
			String idcardTwoinchPhotoUrl = reqData.getString("customerCardImgPath");
			String bareheadedPhotoUrl = reqData.getString("identityCardImagePath");

			JSONObject restData = rests.getJSONObject("data");
			logger.info("同步人证比对图像返回：" + rests);
			if (!restData.get("result_code").equals("00")) {
				R errorResult = R.error(1, restData.getString("result_msg")).put("data", "0");// 同步照片给CRM(1:成功 0：失败);
				// 1.比对失败，根据订单号和账号更新订单活体认证数据
				try {
					// 查询活体认证数据
					Hnzsxh5OrderLivingBodyCertiInfoEntity entity = hnzsxh5OrderLivingBodyCertiInfoService
							.getByOrderNoAndAccount(orderNo, accountNumber);
					if (entity != null) {
						// 更新照片路径、流水号和状态
						entity.setBareheadedPhotoUrl(bareheadedPhotoUrl); // 活体免冠照
						entity.setIdcardTwoinchPhotoUrl(idcardTwoinchPhotoUrl); // 身份证上二寸照
						entity.setCrmSynImgStatus(2); // 表示不通过
						entity.setUpdateTime(new Date());
						hnzsxh5OrderLivingBodyCertiInfoService.updateById(entity);
						logger.info("订单 {} 账号 {} 同步照片给CRM失败并更新活体认证照片数据", orderNo, accountNumber);
					} else {
						logger.warn("未找到订单 {} 账号 {} 的活体认证数据", orderNo, accountNumber);
					}
				} catch (Exception e) {
					logger.error("更新订单 {} 活体认证照片数据异常", orderNo, e);
				}
				// 最后返回结果
				return errorResult;
			} else {
				// 2.比对成功，根据订单号和账号更新订单活体认证数据
				try {
					// 查询活体认证数据
					Hnzsxh5OrderLivingBodyCertiInfoEntity entity = hnzsxh5OrderLivingBodyCertiInfoService
							.getByOrderNoAndAccount(orderNo, accountNumber);
					if (entity != null) {
						// 更新照片路径、流水号和状态
						entity.setBareheadedPhotoUrl(bareheadedPhotoUrl); // 活体免冠照
						entity.setIdcardTwoinchPhotoUrl(idcardTwoinchPhotoUrl); // 身份证上二寸照
						// entity.setTemporaryNumber(temporaryNumber); // 一号一拍流水号
						entity.setCrmSynImgStatus(1); // 表示通过
						entity.setUpdateTime(new Date());
						hnzsxh5OrderLivingBodyCertiInfoService.updateById(entity);
						logger.info("订单 {} 账号 {} 同步照片给CRM成功并更新活体认证照片数据", orderNo, accountNumber);
					} else {
						logger.warn("未找到订单 {} 账号 {} 的活体认证数据", orderNo, accountNumber);
					}
				} catch (Exception e) {
					logger.error("更新订单 {} 活体认证照片数据异常", orderNo, e);
				}
				return R.ok(restData.getString("result_msg")).put("data", "1");// 同步照片给CRM(1:成功 0：失败);
			}
		} catch (Exception e) {
			logger.error("========掌上销省集约同步人证比对照片给CRM异常========", e);
			return R.error(1, e.getMessage()).put("data", "0");// 同步照片给CRM(1:成功 0：失败);
		}
	}

	/**
	 * 人证比对 废弃
	 *
	 * @param phone
	 * @return
	 */
	@RequestMapping("/chkfaceVerify1")
	public R chkfaceVerify1(@RequestBody JSONObject reqData) {
		String identityCardImageBase64 = reqData.getString("identityCardImageBase64");// 免冠照base64串

		// // 上传免冠照
		// JSONObject cephUploadPrame = new JSONObject();
		// cephUploadPrame.put("base64Image", identityCardImageBase64);
		// cephUploadPrame.put("bucketPhoto", "hnzsxbusiness");
		// cephUploadPrame.put("maxSizeKb", 300);
		// cephUploadPrame.put("imageSorceName", "即时受理H5");
		// R cephUploadBase64File =
		// uploadFileService.cephUploadBase64File(cephUploadPrame);
		String identityCardImagePath = "";
		// // 保存免冠照
		// if (0 == cephUploadBase64File.getInteger("code")) {
		// identityCardImagePath = cephUploadBase64File.getString("path");
		// } else {
		// R.error(1, "上传免冠照失败，请重新检测活体验证。");
		// }

		/**
		 * 
		 * 根据readType获取【客户、经办人、使用人】信息
		 */
		JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
		String name = customerInfo.getString("customerName");
		String card = customerInfo.getString("customerCard");

		// 测试数据
		// 二寸照
		String customerCardImg = "202412/hnzsxbusiness/772f2c52841c4c3186fa86f0d8097156.jpeg";
		// 免冠照
		identityCardImagePath = "202412/hnzsxbusiness/712faf523c9f45f19605b16632b79535.jpeg";

		UserModel userModel = userFile.getUserModel();
		String managerCityCode = userModel.getCitycode();
		JSONObject params = new JSONObject();
		params.put("cityCode", managerCityCode);
		// params.put("identityCardImage3",
		// reqData.getString("identityCardImage3"));//免冠照
		// params.put("image_idcard", reqData.getString("customerCardImg"));
		params.put("identityCardImage3", identityCardImagePath);// 免冠照
		params.put("image_idcard", customerCardImg);// 二寸照
		params.put("customerName", name);
		params.put("customerCard", card);

		try {
			R chkfaceVerify = InterfaceUtil.chkfaceVerify(awzS3Service.getRandomBean(), params);// {"code":0,"data":"95.53","message":"成功"}

			reqData.put("identityCardImage3", identityCardImagePath);// 免冠照
			reqData.put("image_idcard", customerCardImg);// 二寸照

			if (0 == chkfaceVerify.getIntValue("code")) {
				reqData.put("similarity", chkfaceVerify.getString("data"));
				R synCustCertInfoToCrm = synCustCertInfoToCrm1(reqData);
				return synCustCertInfoToCrm;
			} else {
				return chkfaceVerify;
			}
		} catch (IOException e) {
			logger.error("人证比对===" + e.getMessage());
			return R.error(1, "人证比对异常：" + e.getMessage());
		}
	}

	/**
	 * 同步人证比对照片给CRM 废弃
	 * 
	 * @param reqData
	 * @return JSONObject
	 * @date 2025-04-12 05:57:30
	 */
	public R synCustCertInfoToCrm1(JSONObject reqData) {
		try {

			// "orderNo":"HNZSXH5JSSL20250411230021315809"

			JSONObject custCertData = new JSONObject();
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();

			String temporary_number = "9009" + StringUtil.generateSerialNumber();

			/**
			 * 
			 * 重新设置【客户、经办人、使用人】脱敏信息
			 */
			JSONObject customerInfo = userFile.getCustomerInfo(null, "CustomerInfoToken");
			JSONObject usersInfoInfo = userFile.getCustomerInfo(null, "UsersInfoToken");
			JSONObject agebtInfoInfo = userFile.getCustomerInfo(null, "AgebtInfoToken");

			String readType = reqData.getString("readType");
			String customerType = reqData.getString("customerType");//// 客户分群1:个人客户；2:企业客户
			String similarity = reqData.getString("similarity");// 人证比对相似度
			String crmOrderId = reqData.getString("crmOrderId");// crm订单号
			String accountNumber = reqData.getString("accountNumber");// 一号一拍账号

			String custId = "";// 读证用户的custId
			String identityCardImage3 = reqData.getString("identityCardImage3");// 免冠照

			String customerCardImg = "";
			String usersCardImg = "";
			String agentCardImg = "";
			if ("1".equals(customerType)) {// 1:个人客户
				// customerCardImg = reqData.getString("customerCardImg");// 客户身份证免冠二寸照
				customerCardImg = reqData.getString("image_idcard");// 客户身份证免冠二寸照 测试
				custId = customerInfo.getString("custId");
			} else if ("2".equals(customerType)) {// 2:企业客户
				if (customerInfo != null) {
					usersCardImg = reqData.getString("usersCardImg");// 使用人身份证免冠二寸照
				}
				if (customerInfo != null) {
					agentCardImg = reqData.getString("agentCardImg");// 经办人身份证免冠二寸照
				}
			}

			custCertData.put("image_idcard", "");
			custCertData.put("image_best", "");// 人证比对免冠照
			custCertData.put("image_usersidcard", "");// 使用人身份证免冠二寸照
			custCertData.put("image_agentidcard", "");// 经办人身份证免冠二寸照

			// 客户身份证免冠二寸照
			if (StringUtil.isNotNull(customerCardImg)) {
				custCertData.put("image_idcard", customerCardImg);
			}
			if (StringUtil.isNotNull(usersCardImg)) {
				custCertData.put("image_usersidcard", usersCardImg);// 使用人身份证免冠二寸照
			}
			if (StringUtil.isNotNull(agentCardImg)) {
				custCertData.put("image_agentidcard", agentCardImg);// 经办人身份证免冠二寸照
			}

			// wm 2020-09-16修改，增加政企客户时，传经办人证件号码
			if ("1".equals(readType)) {
				custCertData.put("customerCard", customerInfo.getString("customerCard"));
			} else {
				custCertData.put("customerCard", agebtInfoInfo.getString("agentCard"));
			}
			custCertData.put("lan_id", managerCityCode);
			custCertData.put("manual_staffcode", userModel.getStaffCode());
			custCertData.put("staffId", userModel.getStaffId());
			custCertData.put("manual_staffname", userModel.getUserName());
			custCertData.put("transactionid", "");
			custCertData.put("custType", customerType);

			// 一号一拍开关
			int isOneBeat = 1;// 是否为一号一拍数据1、是 2、否

			// 一号一拍新增 wm 2024-06-17
			if (1 == isOneBeat) {
				custCertData.put("staff_id", userModel.getStaffId());
				custCertData.put("cust_id", custId);
				custCertData.put("acc_num", accountNumber);
				custCertData.put("temporary_number", temporary_number);

				custCertData.put("similarity", similarity);// 人证比对相似度
				custCertData.put("cust_order_id", "");// CRM系统orderId
				custCertData.put("image_best", identityCardImage3);// 人证比对免冠照
			} else {
				custCertData.put("staff_id", "");
				custCertData.put("cust_id", "");
				custCertData.put("acc_num", "");
				custCertData.put("temporary_number", "");

				custCertData.put("similarity", similarity);// 人证比对相似度
				custCertData.put("cust_order_id", crmOrderId);// CRM系统orderId
				if (StringUtil.isNotNull(identityCardImage3)) {
					custCertData.put("image_best", identityCardImage3);// 人证比对免冠照
				}
			}

			R rests = InterfaceUtil.synCustCertInfoToCrm(awzS3Service.getRandomBean(), custCertData);
			JSONObject restData = rests.getJSONObject("data");
			logger.info("同步人证比对图像返回：" + rests);
			if (!restData.get("result_code").equals("00")) {
				return R.error(1, restData.getString("result_msg")).put("data", "0");// 同步照片给CRM(1:成功 0：失败);
			} else {
				return R.ok(restData.getString("result_msg")).put("data", "1");// 同步照片给CRM(1:成功 0：失败);
			}
		} catch (Exception e) {
			logger.error("========掌上销省集约同步人证比对照片给CRM异常========", e);
			return R.error(1, e.getMessage()).put("data", "0");// 同步照片给CRM(1:成功 0：失败);
		}
	}

	/**
	 * 同步识别仪读取的证件信息给CRM（掌上销用）
	 *
	 * @param response
	 * @param request
	 * @param reqData
	 * @return
	 */
	@RequestMapping("/syncIdardInfo")
	public R syncIdardInfo(HttpServletResponse response, HttpServletRequest request, @RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel(request);
			String managerCityCode = userModel.getCitycode();
			String customerCard = reqData.getString("certiNumber");// 客户身份证号
			String customerName = reqData.getString("custName");// 客户姓名
			String customerAddr = reqData.getString("address");// 身份证地址
			String activityLTo = reqData.getString("activityLTo");// 客户身份证有效期
			String customerCardImg = reqData.getString("customerCardImg");// 客户身份证有效期

			Map<String, Object> condition = new HashMap<String, Object>(16);
			// 本地网
			condition.put("lan_id", managerCityCode);
			// 身份证号码
			condition.put("certiNumber", customerCard);
			// 有效截止日期
			condition.put("activityLTo", activityLTo);
			// 员工id
			condition.put("staffId", userModel.getStaffId());
			// 客户姓名
			condition.put("custName", customerName);
			// 客户身份证地址
			condition.put("address", customerAddr);
			// 身份证免冠照
			condition.put("customerCardImg", customerCardImg);
			condition.put("type", "1");

			JSONObject syncIdardInfoResult = InterfaceUtil.syncIdardInfo(condition, awzS3Service.getRandomBean());
			if ("0".equals(syncIdardInfoResult.getString("code"))) {
				return R.ok("同步成功");
			} else {
				return R.error(1, syncIdardInfoResult.getString("message"));
			}
		} catch (Exception e) {
			logger.error("同步识别仪读取的证件信息给CRM异常:", e);
			return R.error(1, "同步识别仪读取的证件信息给CRM异常" + e.getMessage());
		}
	}

	/**
	 * BSS30_查询终端串码状态
	 * 
	 * @param token
	 * @param mktResCode
	 * @return
	 */
	@RequestMapping("/qryMktResCode")
	public R qryMktResCode(String token, String mktResCode) {
		logger.info("BSS30_查询终端串码状态入参：" + mktResCode);
		try {
			String managerCitycode = (String) redisService.get(token + "MANAGERCITYCODE");
			return InterfaceUtil.qryMktResStatus(mktResCode, managerCitycode);
		} catch (Exception e) {
			logger.error("BSS30_查询终端串码状态异常" + e);
			return R.error(1, "BSS30_查询终端串码状态异常");
		}
	}

	/**
	 * 查询联系人信息
	 * 
	 * @param token
	 * @param mktResCode
	 * @return
	 */
	@RequestMapping("/queryCustContactInfo")
	public R queryCustContactInfo(@RequestBody JSONObject reqData) {
		try {
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			if (customerInfo == null) {
				return R.error(1, "客户信息失效，请重新获取客户信息！");
			}

			return InterfaceUtil.queryCustContactInfo(customerInfo.getString("custId"));
		} catch (Exception e) {
			logger.error("查询联系人信息异常" + e);
			return R.error(1, "查询联系人信息异常");
		}
	}

	/**
	 * 新增/删除联系人
	 * 
	 * @param reqData
	 * @return R
	 * @date 2025-04-12 09:28:16
	 */
	@RequestMapping("/updateOrAddCustContactInfo")
	public R updateOrAddCustContactInfo(@RequestBody JSONObject reqData) {
		try {
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();
			if (customerInfo == null) {
				return R.error(1, "客户信息失效，请重新获取客户信息！");
			}
			reqData.put("cust_id", customerInfo.getString("custId"));
			reqData.put("lan_id", managerCityCode);
			return InterfaceUtil.updateCustContactInfo(reqData);
		} catch (Exception e) {
			logger.error("新增/删除联系人异常" + e);
			return R.error(1, "新增/删除联系人异常");
		}
	}

	/**
	 * 湖南CRM收费确认订单免填单生成与查询接口
	 * 
	 * @param reqData
	 * @return R
	 * @date 2025-04-12 09:43:58
	 */
	@RequestMapping("/crmGetPdf")
	public R crmGetPdf(@RequestBody JSONObject reqData) {
		// 记录处理开始时间
		long startTime = System.currentTimeMillis();
		String processNode = "免填单生成";
		String className = this.getClass().getName();
		String methodName = "crmGetPdf";
		String orderNo = reqData.getString("orderNo");
		// 初始化日志实体
		OrderProcessLogEntity logEntity = new OrderProcessLogEntity();
		// 设置基本信息
		logEntity.setOrderNo(orderNo);
		if (StringUtils.isNotBlank(orderNo)){
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			//根据订单号查询暂存id和87单号
			if (!Objects.isNull(orderInfo)){
				logEntity.setSceneInstId(orderInfo.getSceneInstIdList());
				logEntity.setCustOrderId(orderInfo.getCustOrderIdList());
			}
		}
		logEntity.setProcessNode(processNode);
		logEntity.setClassName(className);
		logEntity.setMethodName(methodName);
		logEntity.setRequestParams(reqData != null ? reqData.toString() : null);
		logEntity.setCreateTime(new Date());
		try {
			// 登录工号信息
			UserModel userModel = userFile.getUserModel();
			// 设置操作人信息
			if (userModel != null) {
				logEntity.setOperatorId(userModel.getId());
				logEntity.setOperatorStaffId(userModel.getStaffId());
				logEntity.setOperatorName(userModel.getUserName());
			}
			// 根据订单号查询custOrderId
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			String custOrderIdList = orderInfo.getCustOrderIdList();
			List<String> custIds = StringUtil.stringToList(custOrderIdList);
			String custOrderId = custIds.get(0);
			reqData.put("cust_order_id", custOrderId);
			reqData.put("busi_type", "2");
			reqData.put("sys_source", "CRM");
			reqData.put("add_mbk", "1");// "1" -- 要明白卡,如果要查询，不传次字段
			// 设置接口请求参数
			logEntity.setInterfaceRequestParams(reqData.toString());
			R result = InterfaceUtil.getPdf(reqData);
			int code = Integer.parseInt(result.getString("code"));
			// 设置响应结果
			if (result != null) {
				logEntity.setResponseResult(result.toString());
				if (code == 1) {
					// 更新订单签字状态为成功
					LambdaUpdateWrapper<Hnzsxh5OrderInfoEntity> updateWrapper = new LambdaUpdateWrapper<>();
					updateWrapper.eq(Hnzsxh5OrderInfoEntity::getOrderNo, orderNo)
							.set(Hnzsxh5OrderInfoEntity::getSignatureStatus, 1) // 设置签字状态
							.set(Hnzsxh5OrderInfoEntity::getUpdatedDate, new Date()); // 更新修改时间
					hnzsxh5OrderInfoService.update(updateWrapper);
					logEntity.setStatus(0); // 0-成功
				} else {
					logEntity.setStatus(1); // 1-失败
					logEntity.setErrorMsg(result.getString("message"));
				}
			} else {
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("接口返回结果为空");
			}
			// 记录执行时间
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("日志记录异常", logEx);
			}
			return R.ok("免填单接口成功");
		} catch (Exception e) {
			// 设置异常信息
			logEntity.setStatus(1); // 1-失败
			logEntity.setErrorMsg("免填单接口异常: " + e.getMessage());
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			logger.error("免填单接口异常", e);
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("异常日志记录失败", logEx);
			}
			return R.error(1, "免填单接口异常");
		}
	}

	@RequestMapping("/hncrmQryMktResInfo")
	public R hncrmQryMktResInfo(@RequestBody JSONObject reqData) {
		try {

			return InterfaceUtil.qryMktResInfo(reqData.getString("objId"));
		} catch (Exception e) {
			logger.error("免填单接口异常" + e);
			return R.error(1, "免填单接口异常");
		}
	}

	/**
	 * 创建用户资料 【V1.0,2019-10-17】
	 *
	 * @param token
	 * @param customerName         姓名
	 * @param customerCard         身份证
	 * @param customerContactPhone 联系电话
	 * @param customerAddr         地址
	 * @param cert_type            客户证件类型
	 * @return
	 */
	@RequestMapping("/createUserData")
	public R createUserData(@RequestBody JSONObject reqData) {
		try {
			UserModel userModel = userFile.getUserModel();
			String managerCityCode = userModel.getCitycode();

			String customerName = reqData.getString("customerName");
			String customerCard = reqData.getString("customerCard");
			String customerContactPhone = reqData.getString("customerContactPhone");
			String customerAddress = reqData.getString("customerAddress");
			String certType = reqData.getString("certType");

			// 用户id为空创建用户id
			Map<String, Object> custParam = new HashMap<>();
			String custId;
			custParam.put("lan_id", managerCityCode);
			custParam.put("cust_name", customerName);// 客户名字
			custParam.put("cert_type", certType);// 类型
			custParam.put("cert_num", customerCard); // 身份证号
			custParam.put("type", "N");
			custParam.put("contact_people", customerName);// 客户联系人
			custParam.put("contact_phone", customerContactPhone); // 客户联系人电话
			custParam.put("address", customerAddress);
			custParam.put("region_id", managerCityCode + "01");
			Map<String, String> insertCust = InterfaceUtil.genPlaCustInfo(custParam);
			if ("0".equals(insertCust.get("code"))) {
				logger.info("创建客户资料成功" + insertCust);
				// 创建用户成功
				custId = insertCust.get("custId");
				if (!StringUtil.isEmpty(custId)) {
					logger.info("用户" + custId + "创建成功");
					return R.ok("创建资料成功").put("data", custId);
				}
			}
			return R.error(1, "创建资料失败");
		} catch (Exception e) {
			logger.error("掌上销查询免费信息异常:", e);
			return R.error(1, "创建资料异常");
		}
	}

	/**
	 * 查询客户欠费信息 String custId, String token
	 * 
	 * @param token
	 * @return
	 * <AUTHOR>
	 * @Date 2020年4月1日下午14:55:27
	 */
	@RequestMapping("/queryArrearsList")
	public R queryArrearsList(@RequestBody JSONObject reqData) {
		logger.info("掌上销-查询客户欠费信息入参:" + reqData);
		try {
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String custId = customerInfo.getString("custId");

			R queryArrears = InterfaceUtil.queryArrearsList(custId);
			if (0 == queryArrears.getInteger("code")) {
				JSONObject data = queryArrears.getJSONObject("data");
				if (data.get("oweFlag").equals("1")) {// 欠费标志 0：不欠费 1：欠费
					return R.ok("掌上销-查询客户欠费信息成功").put("data", data.getJSONArray("detailDtoArr"));
				} else {
					return R.error(1, "未查询到客户欠费信息");
				}
			}
			return R.error(1, "查询客户欠费信息失败");
		} catch (Exception e) {
			logger.error("掌上销-查询客户欠费信息异常", e);
			return R.error(1, "掌上销-查询客户欠费信息异常" + e.getMessage());
		}
	}

	/**
	 * H5查询当前客户可使用的审批单详细信息
	 * 
	 * @param reqData
	 * @return R
	 * @date 2025-04-25 03:21:27
	 */
	@RequestMapping("/h5qryApproveApp")
	public R h5qryApproveApp(@RequestBody JSONObject reqData) {
		logger.info("H5查询当前客户可使用的审批单详细信息入参:" + reqData);
		
		// 记录处理开始时间
		long startTime = System.currentTimeMillis();
		String processNode = "查询当前客户可使用的审批单详细信息";
		String className = this.getClass().getName();
		String methodName = "h5qryApproveApp";
		String orderNo = reqData.getString("orderNo");
		
		// 初始化日志实体
		OrderProcessLogEntity logEntity = new OrderProcessLogEntity();
		// 设置基本信息
		logEntity.setOrderNo(orderNo);
		if (StringUtils.isNotBlank(orderNo)){
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			//根据订单号查询暂存id和87单号
			if (!Objects.isNull(orderInfo)){
				logEntity.setSceneInstId(orderInfo.getSceneInstIdList());
				logEntity.setCustOrderId(orderInfo.getCustOrderIdList());
			}
		}
		logEntity.setProcessNode(processNode);
		logEntity.setClassName(className);
		logEntity.setMethodName(methodName);
		logEntity.setRequestParams(reqData != null ? reqData.toString() : null);
		logEntity.setCreateTime(new Date());
		
		try {
			// 登录工号信息
			UserModel userModel = userFile.getUserModel();

			// 设置操作人信息
			if (userModel != null) {
				logEntity.setOperatorId(userModel.getId());
				logEntity.setOperatorStaffId(userModel.getStaffId());
				logEntity.setOperatorName(userModel.getUserName());
			}
			
			// 客户资料信息
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			if (customerInfo == null || StringUtils.isBlank(customerInfo.getString("custId"))) {
				// 设置错误信息
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("客户ID参数为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "客户ID参数为空");
			}
			
			String custId = customerInfo.getString("custId");

			reqData.put("regionId", userModel.getRegionId());
			reqData.put("lanId", userModel.getCitycode());
			reqData.put("staffId", userModel.getStaffId());
			reqData.put("custId", custId);
			
			// 设置接口请求参数
			logEntity.setInterfaceRequestParams(reqData.toString());
			
			// 调用接口
			R result = InterfaceUtil.h5qryApproveApp(reqData);
			
			// 设置响应结果
			if (result != null) {
				logEntity.setResponseResult(result.toString());
				
				// 根据结果设置状态和错误信息
				if (result.getInteger("code") == 0) {
					// 设置成功状态
					logEntity.setStatus(0); // 0-成功
				} else {
					// 设置失败状态和错误信息
					logEntity.setStatus(1); // 1-失败
					logEntity.setErrorMsg(result.getString("message"));
				}
			} else {
				// 接口返回为空
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("接口返回结果为空");
			}
			
			// 记录执行时间
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("日志记录异常", logEx);
			}
			
			return result;
		} catch (Exception e) {
			// 设置异常信息
			logEntity.setStatus(1); // 1-失败
			logEntity.setErrorMsg("H5查询当前客户可使用的审批单详细信息异常: " + e.getMessage());
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			logger.error("H5查询当前客户可使用的审批单详细信息异常", e);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("异常日志记录失败", logEx);
			}
			
			return R.error(1, "H5查询当前客户可使用的审批单详细信息异常" + e.getMessage());
		}
	}

	/**
	 * 查询当前客户下退订的套餐促销包实例列表包年
	 * 
	 * @param reqData
	 * @return R
	 * @date 2025-04-25 03:35:50
	 */
	@RequestMapping("/LISTCUSTOFFINSTS")
	public R LISTCUSTOFFINSTS(@RequestBody JSONObject reqData) {
		try {
			// 客户资料信息
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String custId = customerInfo.getString("custId");

			reqData.put("custId", custId);

			return InterfaceUtil.LISTCUSTOFFINSTS(reqData);
		} catch (Exception e) {
			logger.error("查询当前客户下退订的套餐促销包实例列表包年异常", e);
			return R.error(1, "查询当前客户下退订的套餐促销包实例列表包年异常" + e.getMessage());
		}
	}

	/**
	 * 批量退订销售品实例
	 * 
	 * @param reqData
	 * @return R
	 * @date 2025-04-25 03:35:50
	 */
	@RequestMapping("/BATCHREMOVEOFFINSTS")
	public R BATCHREMOVEOFFINSTS(@RequestBody JSONObject reqData) {
		try {

			// 登录工号信息
			UserModel userModel = userFile.getUserModel();

			// 客户资料信息
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String custId = customerInfo.getString("custId");

			reqData.put("lanId", userModel.getCitycode());
			reqData.put("staffId", userModel.getStaffId());
			reqData.put("orgId", userModel.getOrgId());
			reqData.put("custId", custId);

			return InterfaceUtil.BATCHREMOVEOFFINSTS(reqData);
		} catch (Exception e) {
			logger.error("批量退订销售品实例异常", e);
			return R.error(1, "批量退订销售品实例异常" + e.getMessage());
		}
	}

	/**
	 * 查询用户微信订单
	 * 
	 * @param reqData
	 * @return R
	 * @date 2025-05-13 08:37:08
	 */
	@RequestMapping("/queryCancelExamTosafe")
	public R queryCancelExamTosafe(@RequestBody JSONObject reqData) {
		try {
			// 客户资料信息
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			reqData.put("customerCard", customerInfo.getString("customerCard"));
			R queryCancelExamTosafe = InterfaceUtil.queryCancelExamTosafe(reqData);
			if (0 == queryCancelExamTosafe.getInteger("code")) {
				JSONArray dataArray = queryCancelExamTosafe.getJSONArray("data");
				JSONArray resultList = new JSONArray();
				for (int i = 0; i < dataArray.size(); i++) {
					JSONObject data = dataArray.getJSONObject(i);
					JSONObject restData = new JSONObject();
					if ("已授信".equals(data.getString("orderStatus"))) {
						restData.put("besttoneOrderItemNo", data.getString("besttoneOrderItemNo"));
						resultList.add(restData);
					}
				}
				return R.ok().put("data", resultList);
			} else {
				return queryCancelExamTosafe;
			}
		} catch (Exception e) {
			logger.error("查询用户微信订单异常", e);
			return R.error(1, "查询用户微信订单异常" + e.getMessage());
		}
	}

	/**
	 * 微信信用授权二维码获取接口（线上渠道)
	 *
	 * @return
	 */
	@RequestMapping("/createOrder")
	public R createOrder(@RequestBody JSONObject reqData) {
		if (StringUtil.isEmpty(reqData.getString("businessType"))
				|| StringUtil.isEmpty(reqData.getString("tatalBonus"))) {
			return R.error("入参异常");
		}
		try {
			// 登录工号信息
			UserModel userModel = userFile.getUserModel();

			// 客户资料信息
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);

			return orderValidationService.hbwxCreateOrder(reqData, userModel, customerInfo);
		} catch (Exception e) {
			logger.error("获取冻结二维码异常:{}", e);
			return R.error("获取冻结二维码异常" + e.getMessage());
		}
	}

	/**
	 * HNBSS掌上销查询查验结果(携号转网结果查询)
	 * 
	 * @param accNbr
	 * @param lanId
	 * @return
	 */
	@RequestMapping("/zsxGetAuthRsp")
	public R zsxGetAuthRsp(@RequestBody JSONObject reqData) {
		try {
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			String phone = reqData.getString("phone");
			String lanId = reqData.getString("lanId");

			JSONObject restJson = InterfaceUtil.zsxGetAuthRsp(phone, lanId);
			if ("0".equals(restJson.get("code"))) {
				String status = restJson.getJSONObject("data").getJSONObject("npInfo").getString("status");
				JSONObject npInfo = restJson.getJSONObject("data").getJSONObject("npInfo");
				if (!npInfo.getString("custName").equals(customerInfo.getString("customerName"))) {
					return R.error(1, "该号码为携入号码与当前读证客户姓名不一致，请重新选号");
				} else if ("002".equals(status)) {
					return R.ok("查验成功").put("data", npInfo).put("codes", "2");
				} else {
					return R.ok(StringUtil.zsxGetAuthRspList.get(status)).put("data", npInfo).put("codes", "3");
				}
			} else if ("2".equals(restJson.get("code"))) {
				return R.ok(restJson.getString("message")).put("codes", "4");
			} else {
				return R.error(1, restJson.getString("message"));
			}
		} catch (Exception e) {
			logger.error("HNBSS掌上销查询查验结果(携号转网结果查询)接口异常：", e);
			return R.error(1, "HNBSS掌上销查询查验结果(携号转网结果查询)接口异常");
		}
	}

	public static void main(String[] args) {
		BigDecimal yuan = new BigDecimal("100"); // 将字符串转换为 BigDecimal 对象
		BigDecimal fen = yuan.multiply(new BigDecimal("100")); // 乘以 100 进行分转换
		fen = fen.setScale(0, BigDecimal.ROUND_DOWN);
//		BigDecimal fen = new BigDecimal("10000");
//		BigDecimal totalAmount = fen.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
		System.out.println(fen);
//		String thisMonthDate = new SimpleDateFormat("yyMMdd").format(new Date());// 当月的日期
//		System.out
//				.println(new Random().nextInt(100) + DateUtils.format(thisMonthDate) + new Random().nextInt(10000000));
	}

	/**
	 * 查询客户群号关系
	 * 
	 * @param reqData 请求参数
	 * @return R
	 * @date 2025-06-20
	 * <AUTHOR>
	 */
	@RequestMapping("/qryCustomerGroupRelation")
	public R qryCustomerGroupRelation(@RequestBody JSONObject reqData) {
		try {
			logger.info("查询客户群号关系入参: {}", reqData);

			// 参数校验
			String custId = reqData.getString("custId");
			String lanId = reqData.getString("lanId");

			if (StringUtils.isBlank(custId)) {
				return R.error(1, "客户ID不能为空");
			}

			if (StringUtils.isBlank(lanId)) {
				return R.error(1, "本地网ID不能为空");
			}

			// 将前端传入的lanId（如730）转换为接口需要的格式（如8430600）
			String formatLanId = CityUtil.lan_codes.get(lanId);
			if (StringUtils.isBlank(formatLanId)) {
				return R.error(1, "无法识别的本地网ID: " + lanId);
			}
			
			logger.info("查询客户群号关系，转换前lanId: {}, 转换后formatLanId: {}", lanId, formatLanId);

			// 调用接口查询客户群号关系
			return InterfaceUtil.qryCustomerGroupRelation(custId, formatLanId);
		} catch (Exception e) {
			logger.error("查询客户群号关系异常", e);
			return R.error(1, "查询客户群号关系异常: " + e.getMessage());
		}
	}

	/**
	 * 终端回收信息查询接口
	 * 
	 * @param reqData 请求参数
	 * @return R
	 * @date 2025-01-27
	 */
	@RequestMapping("/qryTerminalRecycleInfo")
	public R qryTerminalRecycleInfo(@RequestBody JSONObject reqData) {
		logger.info("终端回收信息查询接口入参:" + reqData);
		
		// 记录处理开始时间
		long startTime = System.currentTimeMillis();
		String processNode = "终端回收信息查询";
		String className = this.getClass().getName();
		String methodName = "qryTerminalRecycleInfo";
		String orderNo = reqData.getString("orderNo");
		
		// 初始化日志实体
		OrderProcessLogEntity logEntity = new OrderProcessLogEntity();
		// 设置基本信息
		logEntity.setOrderNo(orderNo);
		if (StringUtils.isNotBlank(orderNo)){
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			//根据订单号查询暂存id和87单号
			if (!Objects.isNull(orderInfo)){
				logEntity.setSceneInstId(orderInfo.getSceneInstIdList());
				logEntity.setCustOrderId(orderInfo.getCustOrderIdList());
			}
		}
		logEntity.setProcessNode(processNode);
		logEntity.setClassName(className);
		logEntity.setMethodName(methodName);
		logEntity.setRequestParams(reqData != null ? reqData.toString() : null);
		logEntity.setCreateTime(new Date());
		
		try {
			// 登录工号信息
			UserModel userModel = userFile.getUserModel();

			// 设置操作人信息
			if (userModel != null) {
				logEntity.setOperatorId(userModel.getId());
				logEntity.setOperatorStaffId(userModel.getStaffId());
				logEntity.setOperatorName(userModel.getUserName());
			}
			
			// 客户资料信息
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			if (customerInfo == null || StringUtils.isBlank(customerInfo.getString("custId"))) {
				// 设置错误信息
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("客户ID参数为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "客户ID参数为空");
			}
			
			String custId = customerInfo.getString("custId");

			// 构建请求参数
			JSONObject requestParams = new JSONObject();
			
			// request部分
			JSONObject request = new JSONObject();
			String sceneInstId = reqData.getString("sceneInstId");
			if (StringUtils.isBlank(sceneInstId)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("场景实例ID不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "场景实例ID不能为空");
			}
			request.put("sceneInstId", sceneInstId);
			requestParams.put("request", request);
			
			// otherInfo部分
			JSONObject otherInfo = new JSONObject();
			otherInfo.put("lanId", userModel.getCitycode());
			otherInfo.put("orgId", userModel.getOrgId());
			StringUtil.putJsonValue(otherInfo,"promotionAStaffId",null);
			StringUtil.putJsonValue(otherInfo,"promotionBStaffId",null);
			StringUtil.putJsonValue(otherInfo,"promotionCStaffId",null);
			StringUtil.putJsonValue(otherInfo,"pushInvoiceWay",null);
			StringUtil.putJsonValue(otherInfo,"receptInvoiceEmail",null);
			StringUtil.putJsonValue(otherInfo,"staffName",null);
			otherInfo.put("regionId", userModel.getRegionId());
			otherInfo.put("staffCode", userModel.getStaffCode());
			otherInfo.put("staffId", userModel.getStaffId());
			requestParams.put("otherInfo", otherInfo);
			
			// custInfo部分
			JSONObject custInfo = new JSONObject();
			StringUtil.putJsonValue(custInfo,"handleCustId",null);
			custInfo.put("ownerCustId", custId);
			custInfo.put("useCustId", custId);
			requestParams.put("custInfo", custInfo);
			
			// token部分
			requestParams.put("token", "");
			
			// 设置接口请求参数
			logEntity.setInterfaceRequestParams(requestParams.toString());
			
			// 调用接口
			R result = InterfaceUtil.qryTerminalRecycleInfo(requestParams);
			
			// 设置响应结果
			if (result != null) {
				logEntity.setResponseResult(result.toString());
				
				// 根据结果设置状态和错误信息
				if (result.getInteger("code") == 0) {
					// 设置成功状态
					logEntity.setStatus(0); // 0-成功
				} else {
					// 设置失败状态和错误信息
					logEntity.setStatus(1); // 1-失败
					logEntity.setErrorMsg(result.getString("message"));
				}
			} else {
				// 接口返回为空
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("接口返回结果为空");
			}
			
			// 记录执行时间
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("日志记录异常", logEx);
			}
			
			return result;
		} catch (Exception e) {
			// 设置异常信息
			logEntity.setStatus(1); // 1-失败
			logEntity.setErrorMsg("终端回收信息查询接口异常: " + e.getMessage());
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			logger.error("终端回收信息查询接口异常", e);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("异常日志记录失败", logEx);
			}
			
			return R.error(1, "终端回收信息查询接口异常" + e.getMessage());
		}
	}

	/**
	 * 新增终端回收接口
	 * 
	 * @param reqData 请求参数
	 * @return R
	 * @date 2025-01-27
	 */
	@RequestMapping("/dealTerminalRecycleInfo")
	public R dealTerminalRecycleInfo(@RequestBody JSONObject reqData) {
		logger.info("新增终端回收接口入参:" + reqData);
		
		// 记录处理开始时间
		long startTime = System.currentTimeMillis();
		String processNode = "新增终端回收";
		String className = this.getClass().getName();
		String methodName = "dealTerminalRecycleInfo";
		String orderNo = reqData.getString("orderNo");
		
		// 初始化日志实体
		OrderProcessLogEntity logEntity = new OrderProcessLogEntity();
		// 设置基本信息
		logEntity.setOrderNo(orderNo);
		if (StringUtils.isNotBlank(orderNo)){
			Hnzsxh5OrderInfoEntity orderInfo = hnzsxh5OrderInfoService.getOrderByOrderNo(orderNo);
			//根据订单号查询暂存id和87单号
			if (!Objects.isNull(orderInfo)){
				logEntity.setSceneInstId(orderInfo.getSceneInstIdList());
				logEntity.setCustOrderId(orderInfo.getCustOrderIdList());
			}
		}
		logEntity.setProcessNode(processNode);
		logEntity.setClassName(className);
		logEntity.setMethodName(methodName);
		logEntity.setRequestParams(reqData != null ? reqData.toString() : null);
		logEntity.setCreateTime(new Date());
		
		try {
			// 登录工号信息
			UserModel userModel = userFile.getUserModel();

			// 设置操作人信息
			if (userModel != null) {
				logEntity.setOperatorId(userModel.getId());
				logEntity.setOperatorStaffId(userModel.getStaffId());
				logEntity.setOperatorName(userModel.getUserName());
			}
			
			// 客户资料信息
			JSONObject customerInfo = userFile.getCustomerInfo(reqData, null);
			if (customerInfo == null || StringUtils.isBlank(customerInfo.getString("custId"))) {
				// 设置错误信息
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("客户ID参数为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "客户ID参数为空");
			}
			
			String custId = customerInfo.getString("custId");

			// 构建请求参数
			JSONObject requestParams = new JSONObject();
			
			// request部分
			JSONObject request = new JSONObject();
			String terminalCode = reqData.getString("terminalCode");
			String ygwModel = reqData.getString("ygwModel");
			String terminalType = reqData.getString("terminalType");
			String recoveryLink = reqData.getString("recoveryLink");
			String recycleWay = reqData.getString("recycle_way");
			String sceneInstId = reqData.getString("sceneInstId");
			String offerId = reqData.getString("offerId");
			String offerInstId = reqData.getString("offerInstId");
			
			// 参数校验
			if (StringUtils.isBlank(terminalCode)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("终端编码不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "终端编码不能为空");
			}
			if (StringUtils.isBlank(ygwModel)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("终端型号不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "终端型号不能为空");
			}
			if (StringUtils.isBlank(terminalType)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("终端类型不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "终端类型不能为空");
			}
			if (StringUtils.isBlank(recoveryLink)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("回收链路不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "回收链路不能为空");
			}
			if (StringUtils.isBlank(recycleWay)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("回收方式不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "回收方式不能为空");
			}
			if (StringUtils.isBlank(sceneInstId)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("暂存单ID不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "场景实例ID不能为空");
			}
			if (StringUtils.isBlank(offerId)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("产品ID不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "产品ID不能为空");
			}
			if (StringUtils.isBlank(offerInstId)) {
				logEntity.setStatus(1);
				logEntity.setErrorMsg("产品实例ID不能为空");
				logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
				try {
					orderProcessLogService.saveLog(logEntity);
				} catch (Exception logEx) {
					logger.error("日志记录异常", logEx);
				}
				return R.error(1, "产品实例ID不能为空");
			}
			
			request.put("terminalCode", terminalCode);
			request.put("ygwModel", ygwModel);
			request.put("terminalType", terminalType);
			request.put("recoveryLink", recoveryLink);
			request.put("recycle_way", recycleWay);
			request.put("sceneInstId", sceneInstId);
			request.put("offerId", offerId);
			request.put("offerInstId", offerInstId);
			requestParams.put("request", request);
			
			// otherInfo部分
			JSONObject otherInfo = new JSONObject();
			otherInfo.put("lanId", userModel.getCitycode());
			otherInfo.put("orgId", userModel.getOrgId());
			otherInfo.put("promotionAStaffId", null);
			otherInfo.put("promotionBStaffId", null);
			otherInfo.put("promotionCStaffId", null);
			otherInfo.put("pushInvoiceWay", null);
			otherInfo.put("receptInvoiceEmail", null);
			otherInfo.put("regionId", userModel.getRegionId());
			otherInfo.put("staffCode", userModel.getStaffCode());
			otherInfo.put("staffId", userModel.getStaffId());
			otherInfo.put("staffName", null);
			requestParams.put("otherInfo", otherInfo);
			
			// custInfo部分
			JSONObject custInfo = new JSONObject();
			custInfo.put("handleCustId", null);
			custInfo.put("ownerCustId", custId);
			custInfo.put("useCustId", custId);
			requestParams.put("custInfo", custInfo);
			
			// token部分 - 生成token
			String token = "ZSX" + userModel.getCitycode() + userModel.getStaffId() + 
						  new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
			requestParams.put("token", token);
			
			// 设置接口请求参数
			logEntity.setInterfaceRequestParams(requestParams.toString());
			
			// 调用接口
			R result = InterfaceUtil.dealTerminalRecycleInfo(requestParams);
			
			// 设置响应结果
			if (result != null) {
				logEntity.setResponseResult(result.toString());
				
				// 根据结果设置状态和错误信息
				if (result.getInteger("code") == 0) {
					// 设置成功状态
					logEntity.setStatus(0); // 0-成功
				} else {
					// 设置失败状态和错误信息
					logEntity.setStatus(1); // 1-失败
					logEntity.setErrorMsg(result.getString("message"));
				}
			} else {
				// 接口返回为空
				logEntity.setStatus(1); // 1-失败
				logEntity.setErrorMsg("接口返回结果为空");
			}
			
			// 记录执行时间
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("日志记录异常", logEx);
			}
			
			return result;
		} catch (Exception e) {
			// 设置异常信息
			logEntity.setStatus(1); // 1-失败
			logEntity.setErrorMsg("新增终端回收接口异常: " + e.getMessage());
			logEntity.setExecutionTime(System.currentTimeMillis() - startTime);
			logger.error("新增终端回收接口异常", e);
			
			// 保存日志
			try {
				orderProcessLogService.saveLog(logEntity);
			} catch (Exception logEx) {
				logger.error("异常日志记录失败", logEx);
			}
			
			return R.error(1, "新增终端回收接口异常" + e.getMessage());
		}
	}
}
