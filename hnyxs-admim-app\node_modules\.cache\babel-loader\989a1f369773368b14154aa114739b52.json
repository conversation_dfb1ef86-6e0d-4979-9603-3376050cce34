{"ast": null, "code": "import \"core-js/modules/es.error.cause.js\";\nimport request from '@/utils/request';\nimport { hasPermission } from '@/utils/permission'; // 权限前缀\n\nconst permPrefix = 'hnzsxH5:configRelation:';\n/**\r\n * 获取所有地市信息（固定地市列表）\r\n */\n\nexport function getAllCities() {\n  // 返回固定的地市列表\n  return Promise.resolve([{\n    cityCode: '730',\n    cityName: '岳阳'\n  }, {\n    cityCode: '731',\n    cityName: '长沙'\n  }, {\n    cityCode: '732',\n    cityName: '湘潭'\n  }, {\n    cityCode: '733',\n    cityName: '株洲'\n  }, {\n    cityCode: '734',\n    cityName: '衡阳'\n  }, {\n    cityCode: '735',\n    cityName: '郴州'\n  }, {\n    cityCode: '736',\n    cityName: '常德'\n  }, {\n    cityCode: '737',\n    cityName: '益阳'\n  }, {\n    cityCode: '738',\n    cityName: '娄底'\n  }, {\n    cityCode: '739',\n    cityName: '邵阳'\n  }, {\n    cityCode: '743',\n    cityName: '湘西'\n  }, {\n    cityCode: '744',\n    cityName: '张家界'\n  }, {\n    cityCode: '745',\n    cityName: '怀化'\n  }, {\n    cityCode: '746',\n    cityName: '永州'\n  }]);\n}\n/**\r\n * 获取所有模块分类（全量查询）\r\n */\n\nexport async function getCategories(params) {\n  try {\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\n    const res = await request.post('/hnzsxH5/hnzsxh5-category/getAllList', params || {\n      status: 1 // 有效状态\n\n    });\n\n    if (res.data.code === 0) {\n      return {\n        rows: res.data.data,\n        count: res.data.data.length\n      };\n    } // 兼容老接口返回格式\n\n\n    if (res.data && Array.isArray(res.data)) {\n      return {\n        rows: res.data,\n        count: res.data.length\n      };\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取分类数据失败'));\n  } catch (error) {\n    console.error('获取分类数据异常:', error);\n    return {\n      rows: [],\n      count: 0\n    };\n  }\n}\n/**\r\n * 获取所有模块（全量查询）\r\n */\n\nexport async function getModules(params) {\n  try {\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\n    const res = await request.post('/hnzsxH5/hnzsxh5-module/getListByParam', params || {\n      status: 1 // 有效状态\n\n    });\n\n    if (res.data.code === 0) {\n      return {\n        rows: res.data.data,\n        count: res.data.data.length\n      };\n    } // 兼容老接口返回格式\n\n\n    if (res.data && Array.isArray(res.data)) {\n      return {\n        rows: res.data,\n        count: res.data.length\n      };\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取模块数据失败'));\n  } catch (error) {\n    console.error('获取模块数据异常:', error);\n    return {\n      rows: [],\n      count: 0\n    };\n  }\n}\n/**\r\n * 获取所有商品属性类别（全量查询）\r\n */\n\nexport async function getAttributeTypes(params) {\n  try {\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\n    const res = await request.post('/hnzsxH5/hnzsxh5-goods-attribute-type/getListByParam', params || {\n      status: 1 // 有效状态\n\n    });\n\n    if (res.data.code === 0) {\n      return {\n        rows: res.data.data,\n        count: res.data.data.length\n      };\n    } // 兼容老接口返回格式\n\n\n    if (res.data && Array.isArray(res.data)) {\n      return {\n        rows: res.data,\n        count: res.data.length\n      };\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取商品属性类别数据失败'));\n  } catch (error) {\n    console.error('获取商品属性类别数据异常:', error);\n    return {\n      rows: [],\n      count: 0\n    };\n  }\n}\n/**\r\n * 获取所有商品标签（全量查询）\r\n */\n\nexport async function getTags(params) {\n  try {\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\n    const res = await request.post('/hnzsxH5/hnzsxh5-goods-tag/getListByParam', params || {\n      status: 1,\n      // 有效状态\n      pId: 0 // 只查询父级标签\n\n    });\n\n    if (res.data.code === 0) {\n      return {\n        rows: res.data.data,\n        count: res.data.data.length\n      };\n    } // 兼容老接口返回格式\n\n\n    if (res.data && Array.isArray(res.data)) {\n      return {\n        rows: res.data,\n        count: res.data.length\n      };\n    }\n\n    return Promise.reject(new Error(res.data.message || '获取商品标签数据失败'));\n  } catch (error) {\n    console.error('获取商品标签数据异常:', error);\n    return {\n      rows: [],\n      count: 0\n    };\n  }\n}\n/**\r\n * 根据地市编码获取关联的模块分类\r\n */\n\nexport async function getCategoriesByCityCode(cityCode) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/getCategoriesByCityCode', {\n    cityCode: cityCode\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 根据地市-分类关系ID查询关联的模块信息\r\n */\n\nexport async function getModulesByCityCategoryRefId(cityCategoryRefId) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/getModulesByCityCategoryRefId', {\n    cityCategoryRefId: cityCategoryRefId\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 保存地市-模块分类关系\r\n */\n\nexport async function saveCityCategoryRef(data) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/avoid/insert', data);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量保存地市-模块分类关系\r\n */\n\nexport async function batchSaveCityCategoryRef(dataList) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/avoid/batchInsert', dataList);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 保存地市分类-模块关系\r\n */\n\nexport async function saveCityCategoryModuleRef(data) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/avoid/insert', data);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量保存地市分类-模块关系\r\n */\n\nexport async function batchSaveCityCategoryModuleRef(dataList) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/avoid/batchInsert', dataList);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 保存模块-商品属性类别关系\r\n */\n\nexport async function saveModuleAttributeTypeRef(data) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/avoid/insert', data);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量保存模块-商品属性类别关系\r\n */\n\nexport async function batchSaveModuleAttributeTypeRef(dataList) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/avoid/batchInsert', dataList);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 保存模块-商品标签关系\r\n */\n\nexport async function saveModuleTagRef(data) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/avoid/insert', data);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量保存模块-商品标签关系\r\n */\n\nexport async function batchSaveModuleTagRef(dataList) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/avoid/batchInsert', dataList);\n\n  if (res.data.code === 0) {\n    return res.data.message || '添加成功';\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 获取模块关联的商品属性类别信息\r\n * @param {number} cityCategoryModuleRefId 模块关系ID\r\n * @returns {Promise<Array>} 属性类型列表\r\n * <AUTHOR>\r\n * @date 2025-05-07\r\n */\n\nexport async function getAttributeTypesByModuleRefId(cityCategoryModuleRefId) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/getAttributeTypesByModuleRefId', {\n    cityCategoryModuleRefId\n  }, {\n    headers: {\n      'Content-Type': 'application/json'\n    }\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 获取模块关联的商品标签信息\r\n */\n\nexport async function getTagsByModuleRefId(cityCategoryModuleRefId) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/getTagsByModuleRefId', {\n    cityCategoryModuleRefId: cityCategoryModuleRefId\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 删除地市-模块分类关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function deleteCityCategoryRef(id) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/delete', {\n    id\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '删除失败'));\n}\n/**\r\n * 删除地市分类-模块关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function deleteCityCategoryModuleRef(id) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/delete', {\n    id\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '删除失败'));\n}\n/**\r\n * 删除模块-商品属性类别关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function deleteModuleAttributeTypeRef(id) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/delete', {\n    id\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '删除失败'));\n}\n/**\r\n * 删除模块-商品标签关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function deleteModuleTagRef(id) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/delete', {\n    id\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '删除失败'));\n}\n/**\r\n * 批量删除地市-模块分类关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function batchDeleteCityCategoryRef(ids) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/batchDelete', {\n    ids\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '批量删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\n}\n/**\r\n * 批量删除地市分类-模块关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function batchDeleteCityCategoryModuleRef(ids) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/batchDelete', {\n    ids\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '批量删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\n}\n/**\r\n * 批量删除模块-商品属性类别关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function batchDeleteModuleAttributeTypeRef(ids) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchDelete', {\n    ids\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '批量删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\n}\n/**\r\n * 批量删除模块-商品标签关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\n\nexport async function batchDeleteModuleTagRef(ids) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchDelete', {\n    ids\n  });\n\n  if (res.data.code === 0) {\n    return res.data.message || '批量删除成功';\n  }\n\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\n}\nexport async function batchUpdateRank(updateList) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchUpdateRank', updateList);\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n} // 批量更新属性排序\n\nexport async function batchUpdateRankSx(updateList) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchUpdateRank', updateList);\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量获取多个地市的分类关系\r\n * @param {string[]} cityCodes 地市编码数组\r\n * @returns {Promise<Object>} 地市编码为key，分类关系数组为value的对象\r\n */\n\nexport async function batchGetCategoriesByCityCodes(cityCodes) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/batchGetCategoriesByCityCodes', {\n    cityCodes: cityCodes\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量获取多个地市分类关系的模块信息\r\n * @param {number[]} cityCategoryRefIds 地市分类关系ID数组\r\n * @returns {Promise<Object>} 关系ID为key，模块关系数组为value的对象\r\n */\n\nexport async function batchGetModulesByCityCategoryRefIds(cityCategoryRefIds) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/batchGetModulesByCityCategoryRefIds', {\n    cityCategoryRefIds: cityCategoryRefIds\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量获取多个模块关系的属性类型信息\r\n * @param {number[]} moduleRefIds 模块关系ID数组\r\n * @returns {Promise<Object>} 模块关系ID为key，属性类型数组为value的对象\r\n */\n\nexport async function batchGetAttributeTypesByModuleRefIds(moduleRefIds) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchGetAttributeTypesByModuleRefIds', {\n    moduleRefIds: moduleRefIds\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}\n/**\r\n * 批量获取多个模块关系的标签信息\r\n * @param {number[]} moduleRefIds 模块关系ID数组\r\n * @returns {Promise<Object>} 模块关系ID为key，标签数组为value的对象\r\n */\n\nexport async function batchGetTagsByModuleRefIds(moduleRefIds) {\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchGetTagsByModuleRefIds', {\n    moduleRefIds: moduleRefIds\n  });\n\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n\n  return Promise.reject(new Error(res.data.message));\n}", "map": {"version": 3, "names": ["request", "hasPermission", "permPrefix", "getAllCities", "Promise", "resolve", "cityCode", "cityName", "getCategories", "params", "res", "post", "status", "data", "code", "rows", "count", "length", "Array", "isArray", "reject", "Error", "message", "error", "console", "getModules", "getAttributeTypes", "getTags", "pId", "getCategoriesByCityCode", "getModulesByCityCategoryRefId", "cityCategoryRefId", "saveCityCategoryRef", "batchSaveCityCategoryRef", "dataList", "saveCityCategoryModuleRef", "batchSaveCityCategoryModuleRef", "saveModuleAttributeTypeRef", "batchSaveModuleAttributeTypeRef", "saveModuleTagRef", "batchSaveModuleTagRef", "getAttributeTypesByModuleRefId", "cityCategoryModuleRefId", "headers", "getTagsByModuleRefId", "deleteCityCategoryRef", "id", "deleteCityCategoryModuleRef", "deleteModuleAttributeTypeRef", "deleteModuleTagRef", "batchDeleteCityCategoryRef", "ids", "batchDeleteCityCategoryModuleRef", "batchDeleteModuleAttributeTypeRef", "batchDeleteModuleTagRef", "batchUpdateRank", "updateList", "batchUpdateRankSx", "batchGetCategoriesByCityCodes", "cityCodes", "batchGetModulesByCityCategoryRefIds", "cityCategoryRefIds", "batchGetAttributeTypesByModuleRefIds", "moduleRefIds", "batchGetTagsByModuleRefIds"], "sources": ["D:/code/dianxinCode/新版省集约项目/hnyxs-admim-app/src/api/hnzsxH5/configRelation.js"], "sourcesContent": ["import request from '@/utils/request';\r\nimport { hasPermission } from '@/utils/permission';\r\n\r\n// 权限前缀\r\nconst permPrefix = 'hnzsxH5:configRelation:';\r\n\r\n/**\r\n * 获取所有地市信息（固定地市列表）\r\n */\r\nexport function getAllCities() {\r\n  // 返回固定的地市列表\r\n  return Promise.resolve([\r\n    { cityCode: '730', cityName: '岳阳' },\r\n    { cityCode: '731', cityName: '长沙' },\r\n    { cityCode: '732', cityName: '湘潭' },\r\n    { cityCode: '733', cityName: '株洲' },\r\n    { cityCode: '734', cityName: '衡阳' },\r\n    { cityCode: '735', cityName: '郴州' },\r\n    { cityCode: '736', cityName: '常德' },\r\n    { cityCode: '737', cityName: '益阳' },\r\n    { cityCode: '738', cityName: '娄底' },\r\n    { cityCode: '739', cityName: '邵阳' },\r\n    { cityCode: '743', cityName: '湘西' },\r\n    { cityCode: '744', cityName: '张家界' },\r\n    { cityCode: '745', cityName: '怀化' },\r\n    { cityCode: '746', cityName: '永州' }\r\n  ]);\r\n}\r\n\r\n/**\r\n * 获取所有模块分类（全量查询）\r\n */\r\nexport async function getCategories(params) {\r\n  try {\r\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-category/getAllList', params || {\r\n      status: 1 // 有效状态\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return {\r\n        rows: res.data.data,\r\n        count: res.data.data.length\r\n      };\r\n    }\r\n\r\n    // 兼容老接口返回格式\r\n    if (res.data && Array.isArray(res.data)) {\r\n      return {\r\n        rows: res.data,\r\n        count: res.data.length\r\n      };\r\n    }\r\n\r\n    return Promise.reject(new Error(res.data.message || '获取分类数据失败'));\r\n  } catch (error) {\r\n    console.error('获取分类数据异常:', error);\r\n    return { rows: [], count: 0 };\r\n  }\r\n}\r\n\r\n/**\r\n * 获取所有模块（全量查询）\r\n */\r\nexport async function getModules(params) {\r\n  try {\r\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-module/getListByParam', params || {\r\n      status: 1 // 有效状态\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return {\r\n        rows: res.data.data,\r\n        count: res.data.data.length\r\n      };\r\n    }\r\n\r\n    // 兼容老接口返回格式\r\n    if (res.data && Array.isArray(res.data)) {\r\n      return {\r\n        rows: res.data,\r\n        count: res.data.length\r\n      };\r\n    }\r\n\r\n    return Promise.reject(new Error(res.data.message || '获取模块数据失败'));\r\n  } catch (error) {\r\n    console.error('获取模块数据异常:', error);\r\n    return { rows: [], count: 0 };\r\n  }\r\n}\r\n\r\n/**\r\n * 获取所有商品属性类别（全量查询）\r\n */\r\nexport async function getAttributeTypes(params) {\r\n  try {\r\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-goods-attribute-type/getListByParam', params || {\r\n      status: 1 // 有效状态\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return {\r\n        rows: res.data.data,\r\n        count: res.data.data.length\r\n      };\r\n    }\r\n\r\n    // 兼容老接口返回格式\r\n    if (res.data && Array.isArray(res.data)) {\r\n      return {\r\n        rows: res.data,\r\n        count: res.data.length\r\n      };\r\n    }\r\n\r\n    return Promise.reject(new Error(res.data.message || '获取商品属性类别数据失败'));\r\n  } catch (error) {\r\n    console.error('获取商品属性类别数据异常:', error);\r\n    return { rows: [], count: 0 };\r\n  }\r\n}\r\n\r\n/**\r\n * 获取所有商品标签（全量查询）\r\n */\r\nexport async function getTags(params) {\r\n  try {\r\n    // 直接调用获取所有列表的接口，避免分页导致数据不完整\r\n    const res = await request.post('/hnzsxH5/hnzsxh5-goods-tag/getListByParam', params || {\r\n      status: 1, // 有效状态\r\n      pId: 0 // 只查询父级标签\r\n    });\r\n\r\n    if (res.data.code === 0) {\r\n      return {\r\n        rows: res.data.data,\r\n        count: res.data.data.length\r\n      };\r\n    }\r\n\r\n    // 兼容老接口返回格式\r\n    if (res.data && Array.isArray(res.data)) {\r\n      return {\r\n        rows: res.data,\r\n        count: res.data.length\r\n      };\r\n    }\r\n\r\n    return Promise.reject(new Error(res.data.message || '获取商品标签数据失败'));\r\n  } catch (error) {\r\n    console.error('获取商品标签数据异常:', error);\r\n    return { rows: [], count: 0 };\r\n  }\r\n}\r\n\r\n/**\r\n * 根据地市编码获取关联的模块分类\r\n */\r\nexport async function getCategoriesByCityCode(cityCode) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/getCategoriesByCityCode', {\r\n    cityCode: cityCode\r\n  });\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 根据地市-分类关系ID查询关联的模块信息\r\n */\r\nexport async function getModulesByCityCategoryRefId(cityCategoryRefId) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/getModulesByCityCategoryRefId', {\r\n    cityCategoryRefId: cityCategoryRefId\r\n  });\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 保存地市-模块分类关系\r\n */\r\nexport async function saveCityCategoryRef(data) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/avoid/insert', data);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量保存地市-模块分类关系\r\n */\r\nexport async function batchSaveCityCategoryRef(dataList) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/avoid/batchInsert', dataList);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 保存地市分类-模块关系\r\n */\r\nexport async function saveCityCategoryModuleRef(data) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/avoid/insert', data);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量保存地市分类-模块关系\r\n */\r\nexport async function batchSaveCityCategoryModuleRef(dataList) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/avoid/batchInsert', dataList);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 保存模块-商品属性类别关系\r\n */\r\nexport async function saveModuleAttributeTypeRef(data) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/avoid/insert', data);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量保存模块-商品属性类别关系\r\n */\r\nexport async function batchSaveModuleAttributeTypeRef(dataList) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/avoid/batchInsert', dataList);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 保存模块-商品标签关系\r\n */\r\nexport async function saveModuleTagRef(data) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/avoid/insert', data);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量保存模块-商品标签关系\r\n */\r\nexport async function batchSaveModuleTagRef(dataList) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/avoid/batchInsert', dataList);\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '添加成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 获取模块关联的商品属性类别信息\r\n * @param {number} cityCategoryModuleRefId 模块关系ID\r\n * @returns {Promise<Array>} 属性类型列表\r\n * <AUTHOR>\r\n * @date 2025-05-07\r\n */\r\nexport async function getAttributeTypesByModuleRefId(cityCategoryModuleRefId) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/getAttributeTypesByModuleRefId',\r\n    { cityCategoryModuleRefId },\r\n    { headers: { 'Content-Type': 'application/json' } }\r\n  );\r\n\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 获取模块关联的商品标签信息\r\n */\r\nexport async function getTagsByModuleRefId(cityCategoryModuleRefId) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/getTagsByModuleRefId', {\r\n    cityCategoryModuleRefId: cityCategoryModuleRefId\r\n  });\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 删除地市-模块分类关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function deleteCityCategoryRef(id) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/delete', { id });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '删除失败'));\r\n}\r\n\r\n/**\r\n * 删除地市分类-模块关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function deleteCityCategoryModuleRef(id) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/delete', { id });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '删除失败'));\r\n}\r\n\r\n/**\r\n * 删除模块-商品属性类别关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function deleteModuleAttributeTypeRef(id) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/delete', { id });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '删除失败'));\r\n}\r\n\r\n/**\r\n * 删除模块-商品标签关系\r\n * @param {number} id 关系ID\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function deleteModuleTagRef(id) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/delete', { id });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '删除失败'));\r\n}\r\n\r\n/**\r\n * 批量删除地市-模块分类关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function batchDeleteCityCategoryRef(ids) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/batchDelete', { ids });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '批量删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\r\n}\r\n\r\n/**\r\n * 批量删除地市分类-模块关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function batchDeleteCityCategoryModuleRef(ids) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/batchDelete', { ids });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '批量删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\r\n}\r\n\r\n/**\r\n * 批量删除模块-商品属性类别关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function batchDeleteModuleAttributeTypeRef(ids) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchDelete', { ids });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '批量删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\r\n}\r\n\r\n/**\r\n * 批量删除模块-商品标签关系\r\n * @param {number[]} ids 关系ID数组\r\n * @returns {Promise<string>} 删除结果消息\r\n * <AUTHOR>\r\n * @date 2025-05-08\r\n */\r\nexport async function batchDeleteModuleTagRef(ids) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchDelete', { ids });\r\n  if (res.data.code === 0) {\r\n    return res.data.message || '批量删除成功';\r\n  }\r\n  return Promise.reject(new Error(res.data.message || '批量删除失败'));\r\n}\r\n\r\nexport async function batchUpdateRank(updateList) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchUpdateRank', updateList);\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n// 批量更新属性排序\r\nexport async function batchUpdateRankSx(updateList) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchUpdateRank', updateList);\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量获取多个地市的分类关系\r\n * @param {string[]} cityCodes 地市编码数组\r\n * @returns {Promise<Object>} 地市编码为key，分类关系数组为value的对象\r\n */\r\nexport async function batchGetCategoriesByCityCodes(cityCodes) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-ref/batchGetCategoriesByCityCodes', {\r\n    cityCodes: cityCodes\r\n  });\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量获取多个地市分类关系的模块信息\r\n * @param {number[]} cityCategoryRefIds 地市分类关系ID数组\r\n * @returns {Promise<Object>} 关系ID为key，模块关系数组为value的对象\r\n */\r\nexport async function batchGetModulesByCityCategoryRefIds(cityCategoryRefIds) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-ref/batchGetModulesByCityCategoryRefIds', {\r\n    cityCategoryRefIds: cityCategoryRefIds\r\n  });\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量获取多个模块关系的属性类型信息\r\n * @param {number[]} moduleRefIds 模块关系ID数组\r\n * @returns {Promise<Object>} 模块关系ID为key，属性类型数组为value的对象\r\n */\r\nexport async function batchGetAttributeTypesByModuleRefIds(moduleRefIds) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-attribute-type-ref/batchGetAttributeTypesByModuleRefIds', {\r\n    moduleRefIds: moduleRefIds\r\n  });\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n/**\r\n * 批量获取多个模块关系的标签信息\r\n * @param {number[]} moduleRefIds 模块关系ID数组\r\n * @returns {Promise<Object>} 模块关系ID为key，标签数组为value的对象\r\n */\r\nexport async function batchGetTagsByModuleRefIds(moduleRefIds) {\r\n  const res = await request.post('/hnzsxH5/hnzsxh5-city-category-module-goods-tag-ref/batchGetTagsByModuleRefIds', {\r\n    moduleRefIds: moduleRefIds\r\n  });\r\n  if (res.data.code === 0) {\r\n    return res.data.data;\r\n  }\r\n  return Promise.reject(new Error(res.data.message));\r\n}\r\n\r\n"], "mappings": ";AAAA,OAAOA,OAAP,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,oBAA9B,C,CAEA;;AACA,MAAMC,UAAU,GAAG,yBAAnB;AAEA;AACA;AACA;;AACA,OAAO,SAASC,YAAT,GAAwB;EAC7B;EACA,OAAOC,OAAO,CAACC,OAAR,CAAgB,CACrB;IAAEC,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CADqB,EAErB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAFqB,EAGrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAHqB,EAIrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAJqB,EAKrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CALqB,EAMrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CANqB,EAOrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAPqB,EAQrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CARqB,EASrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CATqB,EAUrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAVqB,EAWrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAXqB,EAYrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAZqB,EAarB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAbqB,EAcrB;IAAED,QAAQ,EAAE,KAAZ;IAAmBC,QAAQ,EAAE;EAA7B,CAdqB,CAAhB,CAAP;AAgBD;AAED;AACA;AACA;;AACA,OAAO,eAAeC,aAAf,CAA6BC,MAA7B,EAAqC;EAC1C,IAAI;IACF;IACA,MAAMC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,sCAAb,EAAqDF,MAAM,IAAI;MAC/EG,MAAM,EAAE,CADuE,CACrE;;IADqE,CAA/D,CAAlB;;IAIA,IAAIF,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAO;QACLC,IAAI,EAAEL,GAAG,CAACG,IAAJ,CAASA,IADV;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASA,IAAT,CAAcI;MAFhB,CAAP;IAID,CAXC,CAaF;;;IACA,IAAIP,GAAG,CAACG,IAAJ,IAAYK,KAAK,CAACC,OAAN,CAAcT,GAAG,CAACG,IAAlB,CAAhB,EAAyC;MACvC,OAAO;QACLE,IAAI,EAAEL,GAAG,CAACG,IADL;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASI;MAFX,CAAP;IAID;;IAED,OAAOb,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,UAA9B,CAAf,CAAP;EACD,CAtBD,CAsBE,OAAOC,KAAP,EAAc;IACdC,OAAO,CAACD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;IACA,OAAO;MAAER,IAAI,EAAE,EAAR;MAAYC,KAAK,EAAE;IAAnB,CAAP;EACD;AACF;AAED;AACA;AACA;;AACA,OAAO,eAAeS,UAAf,CAA0BhB,MAA1B,EAAkC;EACvC,IAAI;IACF;IACA,MAAMC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,wCAAb,EAAuDF,MAAM,IAAI;MACjFG,MAAM,EAAE,CADyE,CACvE;;IADuE,CAAjE,CAAlB;;IAIA,IAAIF,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAO;QACLC,IAAI,EAAEL,GAAG,CAACG,IAAJ,CAASA,IADV;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASA,IAAT,CAAcI;MAFhB,CAAP;IAID,CAXC,CAaF;;;IACA,IAAIP,GAAG,CAACG,IAAJ,IAAYK,KAAK,CAACC,OAAN,CAAcT,GAAG,CAACG,IAAlB,CAAhB,EAAyC;MACvC,OAAO;QACLE,IAAI,EAAEL,GAAG,CAACG,IADL;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASI;MAFX,CAAP;IAID;;IAED,OAAOb,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,UAA9B,CAAf,CAAP;EACD,CAtBD,CAsBE,OAAOC,KAAP,EAAc;IACdC,OAAO,CAACD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;IACA,OAAO;MAAER,IAAI,EAAE,EAAR;MAAYC,KAAK,EAAE;IAAnB,CAAP;EACD;AACF;AAED;AACA;AACA;;AACA,OAAO,eAAeU,iBAAf,CAAiCjB,MAAjC,EAAyC;EAC9C,IAAI;IACF;IACA,MAAMC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,sDAAb,EAAqEF,MAAM,IAAI;MAC/FG,MAAM,EAAE,CADuF,CACrF;;IADqF,CAA/E,CAAlB;;IAIA,IAAIF,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAO;QACLC,IAAI,EAAEL,GAAG,CAACG,IAAJ,CAASA,IADV;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASA,IAAT,CAAcI;MAFhB,CAAP;IAID,CAXC,CAaF;;;IACA,IAAIP,GAAG,CAACG,IAAJ,IAAYK,KAAK,CAACC,OAAN,CAAcT,GAAG,CAACG,IAAlB,CAAhB,EAAyC;MACvC,OAAO;QACLE,IAAI,EAAEL,GAAG,CAACG,IADL;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASI;MAFX,CAAP;IAID;;IAED,OAAOb,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,cAA9B,CAAf,CAAP;EACD,CAtBD,CAsBE,OAAOC,KAAP,EAAc;IACdC,OAAO,CAACD,KAAR,CAAc,eAAd,EAA+BA,KAA/B;IACA,OAAO;MAAER,IAAI,EAAE,EAAR;MAAYC,KAAK,EAAE;IAAnB,CAAP;EACD;AACF;AAED;AACA;AACA;;AACA,OAAO,eAAeW,OAAf,CAAuBlB,MAAvB,EAA+B;EACpC,IAAI;IACF;IACA,MAAMC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,2CAAb,EAA0DF,MAAM,IAAI;MACpFG,MAAM,EAAE,CAD4E;MACzE;MACXgB,GAAG,EAAE,CAF+E,CAE7E;;IAF6E,CAApE,CAAlB;;IAKA,IAAIlB,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;MACvB,OAAO;QACLC,IAAI,EAAEL,GAAG,CAACG,IAAJ,CAASA,IADV;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASA,IAAT,CAAcI;MAFhB,CAAP;IAID,CAZC,CAcF;;;IACA,IAAIP,GAAG,CAACG,IAAJ,IAAYK,KAAK,CAACC,OAAN,CAAcT,GAAG,CAACG,IAAlB,CAAhB,EAAyC;MACvC,OAAO;QACLE,IAAI,EAAEL,GAAG,CAACG,IADL;QAELG,KAAK,EAAEN,GAAG,CAACG,IAAJ,CAASI;MAFX,CAAP;IAID;;IAED,OAAOb,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,YAA9B,CAAf,CAAP;EACD,CAvBD,CAuBE,OAAOC,KAAP,EAAc;IACdC,OAAO,CAACD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;IACA,OAAO;MAAER,IAAI,EAAE,EAAR;MAAYC,KAAK,EAAE;IAAnB,CAAP;EACD;AACF;AAED;AACA;AACA;;AACA,OAAO,eAAea,uBAAf,CAAuCvB,QAAvC,EAAiD;EACtD,MAAMI,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,4DAAb,EAA2E;IAC3FL,QAAQ,EAAEA;EADiF,CAA3E,CAAlB;;EAGA,IAAII,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAeQ,6BAAf,CAA6CC,iBAA7C,EAAgE;EACrE,MAAMrB,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,yEAAb,EAAwF;IACxGoB,iBAAiB,EAAEA;EADqF,CAAxF,CAAlB;;EAGA,IAAIrB,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAeU,mBAAf,CAAmCnB,IAAnC,EAAyC;EAC9C,MAAMH,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,iDAAb,EAAgEE,IAAhE,CAAlB;;EACA,IAAIH,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAeW,wBAAf,CAAwCC,QAAxC,EAAkD;EACvD,MAAMxB,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,sDAAb,EAAqEuB,QAArE,CAAlB;;EACA,IAAIxB,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAea,yBAAf,CAAyCtB,IAAzC,EAA+C;EACpD,MAAMH,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,wDAAb,EAAuEE,IAAvE,CAAlB;;EACA,IAAIH,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAec,8BAAf,CAA8CF,QAA9C,EAAwD;EAC7D,MAAMxB,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,6DAAb,EAA4EuB,QAA5E,CAAlB;;EACA,IAAIxB,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAee,0BAAf,CAA0CxB,IAA1C,EAAgD;EACrD,MAAMH,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,6EAAb,EAA4FE,IAA5F,CAAlB;;EACA,IAAIH,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAegB,+BAAf,CAA+CJ,QAA/C,EAAyD;EAC9D,MAAMxB,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,kFAAb,EAAiGuB,QAAjG,CAAlB;;EACA,IAAIxB,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAeiB,gBAAf,CAAgC1B,IAAhC,EAAsC;EAC3C,MAAMH,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,kEAAb,EAAiFE,IAAjF,CAAlB;;EACA,IAAIH,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAekB,qBAAf,CAAqCN,QAArC,EAA+C;EACpD,MAAMxB,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,uEAAb,EAAsFuB,QAAtF,CAAlB;;EACA,IAAIxB,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAemB,8BAAf,CAA8CC,uBAA9C,EAAuE;EAC5E,MAAMhC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,+FAAb,EAChB;IAAE+B;EAAF,CADgB,EAEhB;IAAEC,OAAO,EAAE;MAAE,gBAAgB;IAAlB;EAAX,CAFgB,CAAlB;;EAKA,IAAIjC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;;AACA,OAAO,eAAesB,oBAAf,CAAoCF,uBAApC,EAA6D;EAClE,MAAMhC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,0EAAb,EAAyF;IACzG+B,uBAAuB,EAAEA;EADgF,CAAzF,CAAlB;;EAGA,IAAIhC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeuB,qBAAf,CAAqCC,EAArC,EAAyC;EAC9C,MAAMpC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,2CAAb,EAA0D;IAAEmC;EAAF,CAA1D,CAAlB;;EACA,IAAIpC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA9B,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeyB,2BAAf,CAA2CD,EAA3C,EAA+C;EACpD,MAAMpC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,kDAAb,EAAiE;IAAEmC;EAAF,CAAjE,CAAlB;;EACA,IAAIpC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA9B,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAe0B,4BAAf,CAA4CF,EAA5C,EAAgD;EACrD,MAAMpC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,uEAAb,EAAsF;IAAEmC;EAAF,CAAtF,CAAlB;;EACA,IAAIpC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA9B,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAe2B,kBAAf,CAAkCH,EAAlC,EAAsC;EAC3C,MAAMpC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,4DAAb,EAA2E;IAAEmC;EAAF,CAA3E,CAAlB;;EACA,IAAIpC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,MAA9B,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAe4B,0BAAf,CAA0CC,GAA1C,EAA+C;EACpD,MAAMzC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,gDAAb,EAA+D;IAAEwC;EAAF,CAA/D,CAAlB;;EACA,IAAIzC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA9B,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAe8B,gCAAf,CAAgDD,GAAhD,EAAqD;EAC1D,MAAMzC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,uDAAb,EAAsE;IAAEwC;EAAF,CAAtE,CAAlB;;EACA,IAAIzC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA9B,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAe+B,iCAAf,CAAiDF,GAAjD,EAAsD;EAC3D,MAAMzC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,4EAAb,EAA2F;IAAEwC;EAAF,CAA3F,CAAlB;;EACA,IAAIzC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA9B,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAegC,uBAAf,CAAuCH,GAAvC,EAA4C;EACjD,MAAMzC,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,iEAAb,EAAgF;IAAEwC;EAAF,CAAhF,CAAlB;;EACA,IAAIzC,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA3B;EACD;;EACD,OAAOlB,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAT,IAAoB,QAA9B,CAAf,CAAP;AACD;AAED,OAAO,eAAeiC,eAAf,CAA+BC,UAA/B,EAA2C;EAChD,MAAM9C,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,qEAAb,EAAoF6C,UAApF,CAAlB;;EACA,IAAI9C,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD,C,CAED;;AACA,OAAO,eAAemC,iBAAf,CAAiCD,UAAjC,EAA6C;EAClD,MAAM9C,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,gFAAb,EAA+F6C,UAA/F,CAAlB;;EACA,IAAI9C,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAeoC,6BAAf,CAA6CC,SAA7C,EAAwD;EAC7D,MAAMjD,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,kEAAb,EAAiF;IACjGgD,SAAS,EAAEA;EADsF,CAAjF,CAAlB;;EAGA,IAAIjD,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAesC,mCAAf,CAAmDC,kBAAnD,EAAuE;EAC5E,MAAMnD,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,+EAAb,EAA8F;IAC9GkD,kBAAkB,EAAEA;EAD0F,CAA9F,CAAlB;;EAGA,IAAInD,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAewC,oCAAf,CAAoDC,YAApD,EAAkE;EACvE,MAAMrD,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,qGAAb,EAAoH;IACpIoD,YAAY,EAAEA;EADsH,CAApH,CAAlB;;EAGA,IAAIrD,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,eAAe0C,0BAAf,CAA0CD,YAA1C,EAAwD;EAC7D,MAAMrD,GAAG,GAAG,MAAMV,OAAO,CAACW,IAAR,CAAa,gFAAb,EAA+F;IAC/GoD,YAAY,EAAEA;EADiG,CAA/F,CAAlB;;EAGA,IAAIrD,GAAG,CAACG,IAAJ,CAASC,IAAT,KAAkB,CAAtB,EAAyB;IACvB,OAAOJ,GAAG,CAACG,IAAJ,CAASA,IAAhB;EACD;;EACD,OAAOT,OAAO,CAACgB,MAAR,CAAe,IAAIC,KAAJ,CAAUX,GAAG,CAACG,IAAJ,CAASS,OAAnB,CAAf,CAAP;AACD"}, "metadata": {}, "sourceType": "module"}