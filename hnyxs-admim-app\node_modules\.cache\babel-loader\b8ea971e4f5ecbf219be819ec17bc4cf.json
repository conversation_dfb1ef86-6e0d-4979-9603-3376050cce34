{"ast": null, "code": "import \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/es.array.sort.js\";\n\n/**\n * 关联配置管理优化工具类\n * 解决循环调用接口和保存速度慢的问题\n * <AUTHOR> Assistant\n * @date 2025-01-01\n */\nimport { batchGetCategoriesByCityCodes, batchGetModulesByCityCategoryRefIds, batchGetAttributeTypesByModuleRefIds, batchGetTagsByModuleRefIds, batchSaveCityCategoryRef, batchSaveCityCategoryModuleRef, batchSaveModuleAttributeTypeRef, batchSaveModuleTagRef, batchDeleteCityCategoryRef, batchDeleteCityCategoryModuleRef, batchDeleteModuleAttributeTypeRef, batchDeleteModuleTagRef, getCategoriesByCityCode, getModulesByCityCategoryRefId, getAttributeTypesByModuleRefId, getTagsByModuleRefId } from '@/api/hnzsxH5/configRelation';\n/**\n * 关联配置数据加载器\n */\n\nexport class ConfigRelationDataLoader {\n  constructor() {\n    this.cache = new Map(); // 数据缓存\n\n    this.loadingPromises = new Map(); // 防止重复加载\n  }\n  /**\n   * 批量加载地市分类关系数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 地市分类关系数据\n   */\n\n\n  async batchLoadCityCategories(cityCodes) {\n    const cacheKey = `city_categories_${cityCodes.sort().join(',')}`; // 检查缓存\n\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    } // 检查是否正在加载\n\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    } // 开始加载\n\n\n    const loadingPromise = this._loadCityCategoriesData(cityCodes);\n\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n  /**\n   * 内部方法：加载地市分类数据\n   */\n\n\n  async _loadCityCategoriesData(cityCodes) {\n    try {\n      // 尝试使用批量接口\n      const batchResult = await batchGetCategoriesByCityCodes(cityCodes);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error); // 降级为单个接口调用\n\n      const result = {};\n      const promises = cityCodes.map(async cityCode => {\n        const categoryRefs = await getCategoriesByCityCode(cityCode);\n        result[cityCode] = categoryRefs;\n      });\n      await Promise.all(promises);\n      return result;\n    }\n  }\n  /**\n   * 批量加载模块关系数据\n   * @param {number[]} cityCategoryRefIds 地市分类关系ID数组\n   * @returns {Promise<Object>} 模块关系数据\n   */\n\n\n  async batchLoadCategoryModules(cityCategoryRefIds) {\n    const cacheKey = `category_modules_${cityCategoryRefIds.sort().join(',')}`;\n\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadCategoryModulesData(cityCategoryRefIds);\n\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n  /**\n   * 内部方法：加载模块数据\n   */\n\n\n  async _loadCategoryModulesData(cityCategoryRefIds) {\n    try {\n      const batchResult = await batchGetModulesByCityCategoryRefIds(cityCategoryRefIds);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      const result = {};\n      const promises = cityCategoryRefIds.map(async refId => {\n        const moduleRefs = await getModulesByCityCategoryRefId(refId);\n        result[refId] = moduleRefs;\n      });\n      await Promise.all(promises);\n      return result;\n    }\n  }\n  /**\n   * 批量加载模块属性和标签数据\n   * @param {number[]} moduleRefIds 模块关系ID数组\n   * @returns {Promise<{attributes: Object, tags: Object}>} 属性和标签数据\n   */\n\n\n  async batchLoadModuleAttributesAndTags(moduleRefIds) {\n    const cacheKey = `module_attrs_tags_${moduleRefIds.sort().join(',')}`;\n\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadModuleAttributesAndTagsData(moduleRefIds);\n\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n  /**\n   * 内部方法：加载模块属性和标签数据\n   */\n\n\n  async _loadModuleAttributesAndTagsData(moduleRefIds) {\n    try {\n      // 并行调用批量接口\n      const [attributesResult, tagsResult] = await Promise.all([batchGetAttributeTypesByModuleRefIds(moduleRefIds), batchGetTagsByModuleRefIds(moduleRefIds)]); // 转换数据结构：从 {attributes: Map, tags: Map} 转换为 {moduleRefId: {attributes: [], tags: []}}\n\n      const result = {}; // 初始化所有模块的数据结构\n\n      for (const moduleRefId of moduleRefIds) {\n        result[moduleRefId] = {\n          attributes: attributesResult[moduleRefId] || [],\n          tags: tagsResult[moduleRefId] || []\n        };\n      }\n\n      return result;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      const result = {};\n      const promises = moduleRefIds.map(async refId => {\n        const [attrTypes, tagList] = await Promise.all([getAttributeTypesByModuleRefId(refId), getTagsByModuleRefId(refId)]);\n        result[refId] = {\n          attributes: attrTypes,\n          tags: tagList\n        };\n      });\n      await Promise.all(promises);\n      return result;\n    }\n  }\n  /**\n   * 一次性加载所有关联数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 完整的关联数据结构\n   */\n\n\n  async loadAllRelationData(cityCodes) {\n    console.log('开始批量加载关联数据，地市数量:', cityCodes.length);\n    const startTime = Date.now(); // 第一步：加载地市分类关系\n\n    const cityCategories = await this.batchLoadCityCategories(cityCodes);\n    console.log('地市分类关系加载完成，耗时:', Date.now() - startTime, 'ms'); // 收集所有地市分类关系ID\n\n    const cityCategoryRefIds = [];\n    const cityCategoryRefMap = {};\n\n    for (const cityCode of cityCodes) {\n      const categoryRefs = cityCategories[cityCode] || [];\n\n      for (const ref of categoryRefs) {\n        cityCategoryRefIds.push(ref.refId);\n        cityCategoryRefMap[`${cityCode}_${ref.categoryId}`] = ref.refId;\n      }\n    } // 第二步：批量加载模块关系\n\n\n    let categoryModules = {};\n\n    if (cityCategoryRefIds.length > 0) {\n      categoryModules = await this.batchLoadCategoryModules(cityCategoryRefIds);\n      console.log('模块关系加载完成，耗时:', Date.now() - startTime, 'ms');\n    } // 收集所有模块关系ID\n\n\n    const moduleRefIds = [];\n    const cityCategoryModuleRefMap = {};\n\n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n\n      for (const moduleRef of moduleRefs) {\n        moduleRefIds.push(moduleRef.refId);\n        cityCategoryModuleRefMap[`${refId}_${moduleRef.moduleId}`] = moduleRef.refId;\n      }\n    } // 第三步：批量加载属性和标签\n\n\n    let moduleAttributesAndTags = {\n      attributes: {},\n      tags: {}\n    };\n\n    if (moduleRefIds.length > 0) {\n      moduleAttributesAndTags = await this.batchLoadModuleAttributesAndTags(moduleRefIds);\n      console.log('属性标签加载完成，总耗时:', Date.now() - startTime, 'ms');\n    }\n\n    return {\n      cityCategories,\n      categoryModules,\n      moduleAttributesAndTags,\n      cityCategoryRefMap,\n      cityCategoryModuleRefMap\n    };\n  }\n  /**\n   * 清除缓存\n   */\n\n\n  clearCache() {\n    this.cache.clear();\n    this.loadingPromises.clear();\n  }\n  /**\n   * 获取缓存统计信息\n   */\n\n\n  getCacheStats() {\n    return {\n      cacheSize: this.cache.size,\n      loadingCount: this.loadingPromises.size\n    };\n  }\n\n} // 创建单例实例\n\nexport const configRelationDataLoader = new ConfigRelationDataLoader();\n/**\n * 关联配置保存优化器\n */\n\nexport class ConfigRelationSaveOptimizer {\n  constructor() {\n    this.allRelationData = null; // 缓存的完整关联数据\n  }\n  /**\n   * 优化的保存配置流程\n   * @param {Object} componentInstance 组件实例\n   * @returns {Promise<void>}\n   */\n\n\n  async optimizedSaveConfiguration(componentInstance) {\n    const startTime = Date.now();\n    console.log('开始优化的保存配置流程...');\n\n    try {\n      // 1. 一次性加载所有现有关联数据（如果还没有加载）\n      if (!this.allRelationData) {\n        await this.loadAllExistingRelations(componentInstance);\n      } // 2. 并行执行所有保存操作\n\n\n      await Promise.all([this.optimizedSaveCityCategoryRelations(componentInstance), this.optimizedSaveCityCategoryModuleRelations(componentInstance), this.optimizedSaveModuleAttributeTagRelations(componentInstance)]); // 3. 最后执行清理操作\n\n      await this.optimizedDeleteUnusedRelations(componentInstance);\n      console.log(`优化的保存配置完成，总耗时: ${Date.now() - startTime}ms`);\n    } catch (error) {\n      console.error('优化的保存配置失败:', error);\n      throw error;\n    }\n  }\n  /**\n   * 一次性加载所有现有关联数据\n   */\n\n\n  async loadAllExistingRelations(componentInstance) {\n    console.log('开始加载所有现有关联数据...');\n    const startTime = Date.now();\n    this.allRelationData = await configRelationDataLoader.loadAllRelationData(componentInstance.selectedCities);\n    console.log(`所有现有关联数据加载完成，耗时: ${Date.now() - startTime}ms`);\n  }\n  /**\n   * 优化的地市-分类关系保存\n   */\n\n\n  async optimizedSaveCityCategoryRelations(componentInstance) {\n    const cityCategoryRefs = [];\n    const existingRelations = this.allRelationData.cityCategories;\n    console.log('开始处理地市-分类关系保存...');\n    console.log('选中的地市:', componentInstance.selectedCities);\n    console.log('地市分类数据:', componentInstance.cityCategories);\n    console.log('现有关系数据:', existingRelations);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categories = componentInstance.cityCategories[cityCode] || [];\n      console.log(`地市 ${cityCode} 的分类:`, categories);\n\n      for (const categoryId of categories) {\n        const key = `${cityCode}_${categoryId}`; // 检查是否已存在关联关系\n\n        const existingRefs = existingRelations[cityCode] || [];\n        const exists = existingRefs.some(ref => ref.categoryId === categoryId);\n        console.log(`检查关系 ${key}: 已存在=${exists}`);\n\n        if (!exists) {\n          cityCategoryRefs.push({\n            cityCode: cityCode,\n            categoryId: categoryId,\n            status: 1,\n            sort: 1\n          });\n          console.log(`添加新的地市-分类关系: ${key}`);\n        }\n      }\n    }\n\n    if (cityCategoryRefs.length > 0) {\n      console.log(`批量保存${cityCategoryRefs.length}个地市-模块分类关系`);\n      await batchSaveCityCategoryRef(cityCategoryRefs); // 更新组件的映射关系\n\n      await this.updateCityCategoryRefMap(componentInstance);\n    }\n  }\n  /**\n   * 优化的地市分类-模块关系保存\n   */\n\n\n  async optimizedSaveCityCategoryModuleRelations(componentInstance) {\n    const cityCategoryModuleRefs = [];\n    const existingModuleRelations = this.allRelationData.categoryModules;\n    console.log('开始处理地市分类-模块关系保存...');\n    console.log('地市模块数据:', componentInstance.cityModules);\n    console.log('地市分类关系映射:', componentInstance.cityCategoryRefMap);\n    console.log('现有模块关系数据:', existingModuleRelations);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n        console.log(`处理组合 ${cityCategoryKey}, refId: ${refId}`);\n\n        if (!refId) {\n          console.log(`跳过组合 ${cityCategoryKey}: 没有找到refId`);\n          continue;\n        }\n\n        const modules = componentInstance.cityModules[cityCategoryKey] || [];\n        const existingModuleRefs = existingModuleRelations[refId] || [];\n        console.log(`组合 ${cityCategoryKey} 的模块:`, modules);\n        console.log(`组合 ${cityCategoryKey} 的现有模块关系:`, existingModuleRefs);\n\n        for (const moduleId of modules) {\n          const exists = existingModuleRefs.some(ref => ref.moduleId === moduleId);\n          console.log(`检查模块关系 ${cityCategoryKey}-${moduleId}: 已存在=${exists}`);\n\n          if (!exists) {\n            cityCategoryModuleRefs.push({\n              cityCategoryRefId: refId,\n              moduleId: moduleId,\n              status: 1,\n              isOneBeat: 1\n            });\n            console.log(`添加新的模块关系: ${cityCategoryKey}-${moduleId}`);\n          }\n        }\n      }\n    }\n\n    if (cityCategoryModuleRefs.length > 0) {\n      console.log(`批量保存${cityCategoryModuleRefs.length}个地市分类-模块关系`);\n      await batchSaveCityCategoryModuleRef(cityCategoryModuleRefs); // 更新组件的映射关系\n\n      await this.updateCityCategoryModuleRefMap(componentInstance);\n    }\n  }\n  /**\n   * 优化的模块-属性标签关系保存\n   */\n\n\n  async optimizedSaveModuleAttributeTagRelations(componentInstance) {\n    const moduleAttributeTypeRefs = [];\n    const moduleTagRefs = [];\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n        if (!cityCategoryRefId) continue;\n\n        for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n          const key = `${cityCode}-${categoryId}-${moduleId}`;\n          const moduleRefId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n          if (!moduleRefId) continue; // 处理属性类别\n\n          const selectedAttributes = componentInstance.moduleAttributes[key] || [];\n          const existingAttributes = existingAttributesAndTags.attributes[moduleRefId] || [];\n          const existingAttributeIds = existingAttributes.map(item => item.attributeTypeId);\n\n          for (const attributeTypeId of selectedAttributes) {\n            if (!existingAttributeIds.includes(attributeTypeId)) {\n              moduleAttributeTypeRefs.push({\n                cityCategoryModuleRefId: moduleRefId,\n                goodsAttributeTypeId: attributeTypeId,\n                status: 1,\n                sort: 1\n              });\n            }\n          } // 处理标签\n\n\n          const selectedTags = componentInstance.moduleTags[key] || [];\n          const existingTags = existingAttributesAndTags.tags[moduleRefId] || [];\n          const existingTagIds = existingTags.map(item => item.tagId);\n\n          for (const tagId of selectedTags) {\n            if (!existingTagIds.includes(tagId)) {\n              moduleTagRefs.push({\n                goodsTagId: tagId,\n                cityCategoryModuleRefId: moduleRefId,\n                status: 1,\n                sort: 1\n              });\n            }\n          }\n        }\n      }\n    } // 并行保存属性和标签关系\n\n\n    const savePromises = [];\n\n    if (moduleAttributeTypeRefs.length > 0) {\n      console.log(`批量保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);\n      savePromises.push(batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs));\n    }\n\n    if (moduleTagRefs.length > 0) {\n      console.log(`批量保存${moduleTagRefs.length}个模块-商品标签关系`);\n      savePromises.push(batchSaveModuleTagRef(moduleTagRefs));\n    }\n\n    await Promise.all(savePromises);\n  }\n  /**\n   * 优化的删除无用关系\n   */\n\n\n  async optimizedDeleteUnusedRelations(componentInstance) {\n    console.log('开始处理删除无用关系...'); // 基于已加载的数据进行删除操作，避免重复查询\n\n    const toDeleteCategoryRefIds = [];\n    const toDeleteModuleRefIds = [];\n    const toDeleteAttrRefIds = [];\n    const toDeleteTagRefIds = []; // 1. 找出需要删除的地市-分类关系\n\n    this.findUnusedCityCategoryRelations(componentInstance, toDeleteCategoryRefIds); // 2. 找出需要删除的地市分类-模块关系\n\n    this.findUnusedCityCategoryModuleRelations(componentInstance, toDeleteModuleRefIds); // 3. 找出需要删除的模块-属性关系\n\n    this.findUnusedModuleAttributeRelations(componentInstance, toDeleteAttrRefIds); // 4. 找出需要删除的模块-标签关系\n\n    this.findUnusedModuleTagRelations(componentInstance, toDeleteTagRefIds);\n    console.log('待删除的关系统计:');\n    console.log(`地市-分类关系: ${toDeleteCategoryRefIds.length}个`);\n    console.log(`地市分类-模块关系: ${toDeleteModuleRefIds.length}个`);\n    console.log(`模块-属性关系: ${toDeleteAttrRefIds.length}个`);\n    console.log(`模块-标签关系: ${toDeleteTagRefIds.length}个`); // 并行执行删除操作\n\n    const deletePromises = [];\n\n    if (toDeleteCategoryRefIds.length > 0) {\n      console.log(`批量删除${toDeleteCategoryRefIds.length}个地市-分类关系:`, toDeleteCategoryRefIds);\n      deletePromises.push(batchDeleteCityCategoryRef(toDeleteCategoryRefIds));\n    }\n\n    if (toDeleteModuleRefIds.length > 0) {\n      console.log(`批量删除${toDeleteModuleRefIds.length}个地市分类-模块关系:`, toDeleteModuleRefIds);\n      deletePromises.push(batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds));\n    }\n\n    if (toDeleteAttrRefIds.length > 0) {\n      console.log(`批量删除${toDeleteAttrRefIds.length}个模块-属性关系:`, toDeleteAttrRefIds);\n      deletePromises.push(batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds));\n    }\n\n    if (toDeleteTagRefIds.length > 0) {\n      console.log(`批量删除${toDeleteTagRefIds.length}个模块-标签关系:`, toDeleteTagRefIds);\n      deletePromises.push(batchDeleteModuleTagRef(toDeleteTagRefIds));\n    }\n\n    await Promise.all(deletePromises);\n    console.log('删除无用关系完成');\n  }\n  /**\n   * 找出需要删除的地市-分类关系\n   */\n\n\n  findUnusedCityCategoryRelations(componentInstance, toDeleteRefIds) {\n    const existingRelations = this.allRelationData.cityCategories;\n\n    for (const cityCode in existingRelations) {\n      const existingRefs = existingRelations[cityCode] || [];\n      const currentCategories = componentInstance.cityCategories[cityCode] || [];\n\n      for (const ref of existingRefs) {\n        // 如果现有的关系在当前选择中不存在，则标记为删除\n        if (!currentCategories.includes(ref.categoryId)) {\n          toDeleteRefIds.push(ref.refId);\n          console.log(`标记删除地市-分类关系: ${cityCode}-${ref.categoryId} (refId: ${ref.refId})`);\n        }\n      }\n    }\n  }\n  /**\n   * 找出需要删除的地市分类-模块关系\n   */\n\n\n  findUnusedCityCategoryModuleRelations(componentInstance, toDeleteRefIds) {\n    const existingModuleRelations = this.allRelationData.categoryModules;\n\n    for (const cityCategoryRefId in existingModuleRelations) {\n      const existingModuleRefs = existingModuleRelations[cityCategoryRefId] || []; // 找到对应的地市-分类组合\n\n      let cityCategoryKey = null;\n\n      for (const cityCode of componentInstance.selectedCities) {\n        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n          const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n          if (refId == cityCategoryRefId) {\n            cityCategoryKey = `${cityCode}-${categoryId}`;\n            break;\n          }\n        }\n\n        if (cityCategoryKey) break;\n      }\n\n      if (!cityCategoryKey) {\n        // 如果找不到对应的组合，说明整个地市-分类关系都被删除了，删除所有模块关系\n        for (const moduleRef of existingModuleRefs) {\n          toDeleteRefIds.push(moduleRef.refId);\n          console.log(`标记删除模块关系(地市分类已删除): refId ${moduleRef.refId}`);\n        }\n\n        continue;\n      }\n\n      const currentModules = componentInstance.cityModules[cityCategoryKey] || [];\n\n      for (const moduleRef of existingModuleRefs) {\n        // 如果现有的模块关系在当前选择中不存在，则标记为删除\n        if (!currentModules.includes(moduleRef.moduleId)) {\n          toDeleteRefIds.push(moduleRef.refId);\n          console.log(`标记删除模块关系: ${cityCategoryKey}-${moduleRef.moduleId} (refId: ${moduleRef.refId})`);\n        }\n      }\n    }\n  }\n  /**\n   * 找出需要删除的模块-属性关系\n   */\n\n\n  findUnusedModuleAttributeRelations(componentInstance, toDeleteRefIds) {\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n\n    for (const moduleRefId in existingAttributesAndTags) {\n      const moduleData = existingAttributesAndTags[moduleRefId];\n      const existingAttributes = moduleData.attributes || []; // 找到对应的模块组合\n\n      let moduleKey = null;\n\n      for (const cityCode of componentInstance.selectedCities) {\n        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n          const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n          if (!cityCategoryRefId) continue;\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n\n          for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n            const refId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n            if (refId == moduleRefId) {\n              moduleKey = `${cityCode}-${categoryId}-${moduleId}`;\n              break;\n            }\n          }\n\n          if (moduleKey) break;\n        }\n\n        if (moduleKey) break;\n      }\n\n      if (!moduleKey) {\n        // 如果找不到对应的模块组合，说明模块关系被删除了，删除所有属性关系\n        for (const attrRef of existingAttributes) {\n          toDeleteRefIds.push(attrRef.refId);\n        }\n\n        continue;\n      }\n\n      const currentAttributes = componentInstance.moduleAttributes[moduleKey] || [];\n\n      for (const attrRef of existingAttributes) {\n        // 如果现有的属性关系在当前选择中不存在，则标记为删除\n        if (!currentAttributes.includes(attrRef.attributeTypeId)) {\n          toDeleteRefIds.push(attrRef.refId);\n        }\n      }\n    }\n  }\n  /**\n   * 找出需要删除的模块-标签关系\n   */\n\n\n  findUnusedModuleTagRelations(componentInstance, toDeleteRefIds) {\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n    console.log('开始查找需要删除的标签关系...');\n\n    for (const moduleRefId in existingAttributesAndTags) {\n      const moduleData = existingAttributesAndTags[moduleRefId];\n      const existingTags = moduleData.tags || [];\n      console.log(`检查模块关系 ${moduleRefId} 的标签:`, existingTags.map(tag => `${tag.tagId}(refId:${tag.refId})`)); // 找到对应的模块组合\n\n      let moduleKey = null;\n\n      for (const cityCode of componentInstance.selectedCities) {\n        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n          const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n          if (!cityCategoryRefId) continue;\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n\n          for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n            const refId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n            if (refId == moduleRefId) {\n              moduleKey = `${cityCode}-${categoryId}-${moduleId}`;\n              break;\n            }\n          }\n\n          if (moduleKey) break;\n        }\n\n        if (moduleKey) break;\n      }\n\n      if (!moduleKey) {\n        // 如果找不到对应的模块组合，说明模块关系被删除了，删除所有标签关系\n        console.log(`模块关系 ${moduleRefId} 对应的组合不存在，删除所有标签关系`);\n\n        for (const tagRef of existingTags) {\n          toDeleteRefIds.push(tagRef.refId);\n          console.log(`标记删除标签关系(模块已删除): refId ${tagRef.refId}`);\n        }\n\n        continue;\n      }\n\n      const currentTags = componentInstance.moduleTags[moduleKey] || [];\n      console.log(`模块组合 ${moduleKey} 当前选择的标签:`, currentTags);\n\n      for (const tagRef of existingTags) {\n        // 如果现有的标签关系在当前选择中不存在，则标记为删除\n        if (!currentTags.includes(tagRef.tagId)) {\n          toDeleteRefIds.push(tagRef.refId);\n          console.log(`标记删除标签关系: ${moduleKey}-${tagRef.tagId} (refId: ${tagRef.refId})`);\n        }\n      }\n    }\n  }\n  /**\n   * 更新地市分类关系映射\n   */\n\n\n  async updateCityCategoryRefMap(componentInstance) {\n    // 重新获取关联ID\n    const cityCategories = await configRelationDataLoader.batchLoadCityCategories(componentInstance.selectedCities);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categoryRefs = cityCategories[cityCode] || [];\n\n      for (const ref of categoryRefs) {\n        const key = `${cityCode}_${ref.categoryId}`;\n        componentInstance.cityCategoryRefMap[key] = ref.refId;\n      }\n    }\n  }\n  /**\n   * 更新地市分类模块关系映射\n   */\n\n\n  async updateCityCategoryModuleRefMap(componentInstance) {\n    const cityCategoryRefIds = Object.values(componentInstance.cityCategoryRefMap);\n    const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds);\n\n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n\n      for (const moduleRef of moduleRefs) {\n        const key = `${refId}_${moduleRef.moduleId}`;\n        componentInstance.cityCategoryModuleRefMap[key] = moduleRef.refId;\n      }\n    }\n  }\n  /**\n   * 清除缓存\n   */\n\n\n  clearCache() {\n    this.allRelationData = null;\n  }\n\n} // 创建保存优化器实例\n\nexport const configRelationSaveOptimizer = new ConfigRelationSaveOptimizer();", "map": {"version": 3, "names": ["batchGetCategoriesByCityCodes", "batchGetModulesByCityCategoryRefIds", "batchGetAttributeTypesByModuleRefIds", "batchGetTagsByModuleRefIds", "batchSaveCityCategoryRef", "batchSaveCityCategoryModuleRef", "batchSaveModuleAttributeTypeRef", "batchSaveModuleTagRef", "batchDeleteCityCategoryRef", "batchDeleteCityCategoryModuleRef", "batchDeleteModuleAttributeTypeRef", "batchDeleteModuleTagRef", "getCategoriesByCityCode", "getModulesByCityCategoryRefId", "getAttributeTypesByModuleRefId", "getTagsByModuleRefId", "ConfigRelationDataLoader", "constructor", "cache", "Map", "loadingPromises", "batchLoadCityCategories", "cityCodes", "cache<PERSON>ey", "sort", "join", "has", "get", "loadingPromise", "_loadCityCategoriesData", "set", "result", "delete", "batchResult", "error", "console", "warn", "promises", "map", "cityCode", "categoryRefs", "Promise", "all", "batchLoadCategoryModules", "cityCategoryRefIds", "_loadCategoryModulesData", "refId", "moduleRefs", "batchLoadModuleAttributesAndTags", "moduleRefIds", "_loadModuleAttributesAndTagsData", "attributesResult", "tagsResult", "moduleRefId", "attributes", "tags", "attrTypes", "tagList", "loadAllRelationData", "log", "length", "startTime", "Date", "now", "cityCategories", "cityCategoryRefMap", "ref", "push", "categoryId", "categoryModules", "cityCategoryModuleRefMap", "moduleRef", "moduleId", "moduleAttributesAndTags", "clearCache", "clear", "getCacheStats", "cacheSize", "size", "loadingCount", "configRelationDataLoader", "ConfigRelationSaveOptimizer", "allRelationData", "optimizedSaveConfiguration", "componentInstance", "loadAllExistingRelations", "optimizedSaveCityCategoryRelations", "optimizedSaveCityCategoryModuleRelations", "optimizedSaveModuleAttributeTagRelations", "optimizedDeleteUnusedRelations", "selectedCities", "cityCategoryRefs", "existingRelations", "categories", "key", "existingRefs", "exists", "some", "status", "updateCityCategoryRefMap", "cityCategoryModuleRefs", "existingModuleRelations", "cityModules", "cityCategoryKey", "modules", "existingModuleRefs", "cityCategoryRefId", "isOneBeat", "updateCityCategoryModuleRefMap", "moduleAttributeTypeRefs", "moduleTagRefs", "existingAttributesAndTags", "selectedAttributes", "moduleAttributes", "existingAttributes", "existingAttributeIds", "item", "attributeTypeId", "includes", "cityCategoryModuleRefId", "goodsAttributeTypeId", "selectedTags", "moduleTags", "existingTags", "existingTagIds", "tagId", "goodsTagId", "savePromises", "toDeleteCategoryRefIds", "toDeleteModuleRefIds", "toDeleteAttrRefIds", "toDeleteTagRefIds", "findUnusedCityCategoryRelations", "findUnusedCityCategoryModuleRelations", "findUnusedModuleAttributeRelations", "findUnusedModuleTagRelations", "deletePromises", "toDeleteRefIds", "currentCategories", "currentModules", "moduleData", "module<PERSON>ey", "attrRef", "currentAttributes", "tag", "tagRef", "currentTags", "Object", "values", "configRelationSaveOptimizer"], "sources": ["D:/code/dianxinCode/新版省集约项目/hnyxs-admim-app/src/utils/configRelationOptimizer.js"], "sourcesContent": ["/**\n * 关联配置管理优化工具类\n * 解决循环调用接口和保存速度慢的问题\n * <AUTHOR> Assistant\n * @date 2025-01-01\n */\n\nimport {\n  batchGetCategoriesByCityCodes,\n  batchGetModulesByCityCategoryRefIds,\n  batchGetAttributeTypesByModuleRefIds,\n  batchGetTagsByModuleRefIds,\n  batchSaveCityCategoryRef,\n  batchSaveCityCategoryModuleRef,\n  batchSaveModuleAttributeTypeRef,\n  batchSaveModuleTagRef,\n  batchDeleteCityCategoryRef,\n  batchDeleteCityCategoryModuleRef,\n  batchDeleteModuleAttributeTypeRef,\n  batchDeleteModuleTagRef,\n  getCategoriesByCityCode,\n  getModulesByCityCategoryRefId,\n  getAttributeTypesByModuleRefId,\n  getTagsByModuleRefId\n} from '@/api/hnzsxH5/configRelation';\n\n/**\n * 关联配置数据加载器\n */\nexport class ConfigRelationDataLoader {\n  constructor() {\n    this.cache = new Map(); // 数据缓存\n    this.loadingPromises = new Map(); // 防止重复加载\n  }\n\n  /**\n   * 批量加载地市分类关系数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 地市分类关系数据\n   */\n  async batchLoadCityCategories(cityCodes) {\n    const cacheKey = `city_categories_${cityCodes.sort().join(',')}`;\n    \n    // 检查缓存\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    // 检查是否正在加载\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    // 开始加载\n    const loadingPromise = this._loadCityCategoriesData(cityCodes);\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n\n  /**\n   * 内部方法：加载地市分类数据\n   */\n  async _loadCityCategoriesData(cityCodes) {\n    try {\n      // 尝试使用批量接口\n      const batchResult = await batchGetCategoriesByCityCodes(cityCodes);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      \n      // 降级为单个接口调用\n      const result = {};\n      const promises = cityCodes.map(async cityCode => {\n        const categoryRefs = await getCategoriesByCityCode(cityCode);\n        result[cityCode] = categoryRefs;\n      });\n      \n      await Promise.all(promises);\n      return result;\n    }\n  }\n\n  /**\n   * 批量加载模块关系数据\n   * @param {number[]} cityCategoryRefIds 地市分类关系ID数组\n   * @returns {Promise<Object>} 模块关系数据\n   */\n  async batchLoadCategoryModules(cityCategoryRefIds) {\n    const cacheKey = `category_modules_${cityCategoryRefIds.sort().join(',')}`;\n    \n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadCategoryModulesData(cityCategoryRefIds);\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n\n  /**\n   * 内部方法：加载模块数据\n   */\n  async _loadCategoryModulesData(cityCategoryRefIds) {\n    try {\n      const batchResult = await batchGetModulesByCityCategoryRefIds(cityCategoryRefIds);\n      return batchResult;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n      \n      const result = {};\n      const promises = cityCategoryRefIds.map(async refId => {\n        const moduleRefs = await getModulesByCityCategoryRefId(refId);\n        result[refId] = moduleRefs;\n      });\n      \n      await Promise.all(promises);\n      return result;\n    }\n  }\n\n  /**\n   * 批量加载模块属性和标签数据\n   * @param {number[]} moduleRefIds 模块关系ID数组\n   * @returns {Promise<{attributes: Object, tags: Object}>} 属性和标签数据\n   */\n  async batchLoadModuleAttributesAndTags(moduleRefIds) {\n    const cacheKey = `module_attrs_tags_${moduleRefIds.sort().join(',')}`;\n    \n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    if (this.loadingPromises.has(cacheKey)) {\n      return this.loadingPromises.get(cacheKey);\n    }\n\n    const loadingPromise = this._loadModuleAttributesAndTagsData(moduleRefIds);\n    this.loadingPromises.set(cacheKey, loadingPromise);\n\n    try {\n      const result = await loadingPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.loadingPromises.delete(cacheKey);\n    }\n  }\n\n  /**\n   * 内部方法：加载模块属性和标签数据\n   */\n  async _loadModuleAttributesAndTagsData(moduleRefIds) {\n    try {\n      // 并行调用批量接口\n      const [attributesResult, tagsResult] = await Promise.all([\n        batchGetAttributeTypesByModuleRefIds(moduleRefIds),\n        batchGetTagsByModuleRefIds(moduleRefIds)\n      ]);\n\n      // 转换数据结构：从 {attributes: Map, tags: Map} 转换为 {moduleRefId: {attributes: [], tags: []}}\n      const result = {};\n\n      // 初始化所有模块的数据结构\n      for (const moduleRefId of moduleRefIds) {\n        result[moduleRefId] = {\n          attributes: attributesResult[moduleRefId] || [],\n          tags: tagsResult[moduleRefId] || []\n        };\n      }\n\n      return result;\n    } catch (error) {\n      console.warn('批量接口调用失败，降级为单个接口调用:', error);\n\n      const result = {};\n\n      const promises = moduleRefIds.map(async refId => {\n        const [attrTypes, tagList] = await Promise.all([\n          getAttributeTypesByModuleRefId(refId),\n          getTagsByModuleRefId(refId)\n        ]);\n\n        result[refId] = {\n          attributes: attrTypes,\n          tags: tagList\n        };\n      });\n\n      await Promise.all(promises);\n      return result;\n    }\n  }\n\n  /**\n   * 一次性加载所有关联数据\n   * @param {string[]} cityCodes 地市编码数组\n   * @returns {Promise<Object>} 完整的关联数据结构\n   */\n  async loadAllRelationData(cityCodes) {\n    console.log('开始批量加载关联数据，地市数量:', cityCodes.length);\n    const startTime = Date.now();\n\n    // 第一步：加载地市分类关系\n    const cityCategories = await this.batchLoadCityCategories(cityCodes);\n    console.log('地市分类关系加载完成，耗时:', Date.now() - startTime, 'ms');\n\n    // 收集所有地市分类关系ID\n    const cityCategoryRefIds = [];\n    const cityCategoryRefMap = {};\n    \n    for (const cityCode of cityCodes) {\n      const categoryRefs = cityCategories[cityCode] || [];\n      for (const ref of categoryRefs) {\n        cityCategoryRefIds.push(ref.refId);\n        cityCategoryRefMap[`${cityCode}_${ref.categoryId}`] = ref.refId;\n      }\n    }\n\n    // 第二步：批量加载模块关系\n    let categoryModules = {};\n    if (cityCategoryRefIds.length > 0) {\n      categoryModules = await this.batchLoadCategoryModules(cityCategoryRefIds);\n      console.log('模块关系加载完成，耗时:', Date.now() - startTime, 'ms');\n    }\n\n    // 收集所有模块关系ID\n    const moduleRefIds = [];\n    const cityCategoryModuleRefMap = {};\n    \n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n      for (const moduleRef of moduleRefs) {\n        moduleRefIds.push(moduleRef.refId);\n        cityCategoryModuleRefMap[`${refId}_${moduleRef.moduleId}`] = moduleRef.refId;\n      }\n    }\n\n    // 第三步：批量加载属性和标签\n    let moduleAttributesAndTags = { attributes: {}, tags: {} };\n    if (moduleRefIds.length > 0) {\n      moduleAttributesAndTags = await this.batchLoadModuleAttributesAndTags(moduleRefIds);\n      console.log('属性标签加载完成，总耗时:', Date.now() - startTime, 'ms');\n    }\n\n    return {\n      cityCategories,\n      categoryModules,\n      moduleAttributesAndTags,\n      cityCategoryRefMap,\n      cityCategoryModuleRefMap\n    };\n  }\n\n  /**\n   * 清除缓存\n   */\n  clearCache() {\n    this.cache.clear();\n    this.loadingPromises.clear();\n  }\n\n  /**\n   * 获取缓存统计信息\n   */\n  getCacheStats() {\n    return {\n      cacheSize: this.cache.size,\n      loadingCount: this.loadingPromises.size\n    };\n  }\n}\n\n// 创建单例实例\nexport const configRelationDataLoader = new ConfigRelationDataLoader();\n\n/**\n * 关联配置保存优化器\n */\nexport class ConfigRelationSaveOptimizer {\n  constructor() {\n    this.allRelationData = null; // 缓存的完整关联数据\n  }\n\n  /**\n   * 优化的保存配置流程\n   * @param {Object} componentInstance 组件实例\n   * @returns {Promise<void>}\n   */\n  async optimizedSaveConfiguration(componentInstance) {\n    const startTime = Date.now();\n    console.log('开始优化的保存配置流程...');\n\n    try {\n      // 1. 一次性加载所有现有关联数据（如果还没有加载）\n      if (!this.allRelationData) {\n        await this.loadAllExistingRelations(componentInstance);\n      }\n\n      // 2. 并行执行所有保存操作\n      await Promise.all([\n        this.optimizedSaveCityCategoryRelations(componentInstance),\n        this.optimizedSaveCityCategoryModuleRelations(componentInstance),\n        this.optimizedSaveModuleAttributeTagRelations(componentInstance)\n      ]);\n\n      // 3. 最后执行清理操作\n      await this.optimizedDeleteUnusedRelations(componentInstance);\n\n      console.log(`优化的保存配置完成，总耗时: ${Date.now() - startTime}ms`);\n    } catch (error) {\n      console.error('优化的保存配置失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 一次性加载所有现有关联数据\n   */\n  async loadAllExistingRelations(componentInstance) {\n    console.log('开始加载所有现有关联数据...');\n    const startTime = Date.now();\n\n    this.allRelationData = await configRelationDataLoader.loadAllRelationData(componentInstance.selectedCities);\n\n    console.log(`所有现有关联数据加载完成，耗时: ${Date.now() - startTime}ms`);\n  }\n\n  /**\n   * 优化的地市-分类关系保存\n   */\n  async optimizedSaveCityCategoryRelations(componentInstance) {\n    const cityCategoryRefs = [];\n    const existingRelations = this.allRelationData.cityCategories;\n\n    console.log('开始处理地市-分类关系保存...');\n    console.log('选中的地市:', componentInstance.selectedCities);\n    console.log('地市分类数据:', componentInstance.cityCategories);\n    console.log('现有关系数据:', existingRelations);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categories = componentInstance.cityCategories[cityCode] || [];\n      console.log(`地市 ${cityCode} 的分类:`, categories);\n\n      for (const categoryId of categories) {\n        const key = `${cityCode}_${categoryId}`;\n\n        // 检查是否已存在关联关系\n        const existingRefs = existingRelations[cityCode] || [];\n        const exists = existingRefs.some(ref => ref.categoryId === categoryId);\n\n        console.log(`检查关系 ${key}: 已存在=${exists}`);\n\n        if (!exists) {\n          cityCategoryRefs.push({\n            cityCode: cityCode,\n            categoryId: categoryId,\n            status: 1,\n            sort: 1\n          });\n          console.log(`添加新的地市-分类关系: ${key}`);\n        }\n      }\n    }\n\n    if (cityCategoryRefs.length > 0) {\n      console.log(`批量保存${cityCategoryRefs.length}个地市-模块分类关系`);\n      await batchSaveCityCategoryRef(cityCategoryRefs);\n\n      // 更新组件的映射关系\n      await this.updateCityCategoryRefMap(componentInstance);\n    }\n  }\n\n  /**\n   * 优化的地市分类-模块关系保存\n   */\n  async optimizedSaveCityCategoryModuleRelations(componentInstance) {\n    const cityCategoryModuleRefs = [];\n    const existingModuleRelations = this.allRelationData.categoryModules;\n\n    console.log('开始处理地市分类-模块关系保存...');\n    console.log('地市模块数据:', componentInstance.cityModules);\n    console.log('地市分类关系映射:', componentInstance.cityCategoryRefMap);\n    console.log('现有模块关系数据:', existingModuleRelations);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        console.log(`处理组合 ${cityCategoryKey}, refId: ${refId}`);\n\n        if (!refId) {\n          console.log(`跳过组合 ${cityCategoryKey}: 没有找到refId`);\n          continue;\n        }\n\n        const modules = componentInstance.cityModules[cityCategoryKey] || [];\n        const existingModuleRefs = existingModuleRelations[refId] || [];\n\n        console.log(`组合 ${cityCategoryKey} 的模块:`, modules);\n        console.log(`组合 ${cityCategoryKey} 的现有模块关系:`, existingModuleRefs);\n\n        for (const moduleId of modules) {\n          const exists = existingModuleRefs.some(ref => ref.moduleId === moduleId);\n\n          console.log(`检查模块关系 ${cityCategoryKey}-${moduleId}: 已存在=${exists}`);\n\n          if (!exists) {\n            cityCategoryModuleRefs.push({\n              cityCategoryRefId: refId,\n              moduleId: moduleId,\n              status: 1,\n              isOneBeat: 1\n            });\n            console.log(`添加新的模块关系: ${cityCategoryKey}-${moduleId}`);\n          }\n        }\n      }\n    }\n\n    if (cityCategoryModuleRefs.length > 0) {\n      console.log(`批量保存${cityCategoryModuleRefs.length}个地市分类-模块关系`);\n      await batchSaveCityCategoryModuleRef(cityCategoryModuleRefs);\n\n      // 更新组件的映射关系\n      await this.updateCityCategoryModuleRefMap(componentInstance);\n    }\n  }\n\n  /**\n   * 优化的模块-属性标签关系保存\n   */\n  async optimizedSaveModuleAttributeTagRelations(componentInstance) {\n    const moduleAttributeTypeRefs = [];\n    const moduleTagRefs = [];\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n\n    for (const cityCode of componentInstance.selectedCities) {\n      for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n        const cityCategoryKey = `${cityCode}-${categoryId}`;\n        const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n\n        if (!cityCategoryRefId) continue;\n\n        for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n          const key = `${cityCode}-${categoryId}-${moduleId}`;\n          const moduleRefId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n\n          if (!moduleRefId) continue;\n\n          // 处理属性类别\n          const selectedAttributes = componentInstance.moduleAttributes[key] || [];\n          const existingAttributes = existingAttributesAndTags.attributes[moduleRefId] || [];\n          const existingAttributeIds = existingAttributes.map(item => item.attributeTypeId);\n\n          for (const attributeTypeId of selectedAttributes) {\n            if (!existingAttributeIds.includes(attributeTypeId)) {\n              moduleAttributeTypeRefs.push({\n                cityCategoryModuleRefId: moduleRefId,\n                goodsAttributeTypeId: attributeTypeId,\n                status: 1,\n                sort: 1\n              });\n            }\n          }\n\n          // 处理标签\n          const selectedTags = componentInstance.moduleTags[key] || [];\n          const existingTags = existingAttributesAndTags.tags[moduleRefId] || [];\n          const existingTagIds = existingTags.map(item => item.tagId);\n\n          for (const tagId of selectedTags) {\n            if (!existingTagIds.includes(tagId)) {\n              moduleTagRefs.push({\n                goodsTagId: tagId,\n                cityCategoryModuleRefId: moduleRefId,\n                status: 1,\n                sort: 1\n              });\n            }\n          }\n        }\n      }\n    }\n\n    // 并行保存属性和标签关系\n    const savePromises = [];\n\n    if (moduleAttributeTypeRefs.length > 0) {\n      console.log(`批量保存${moduleAttributeTypeRefs.length}个模块-商品属性类别关系`);\n      savePromises.push(batchSaveModuleAttributeTypeRef(moduleAttributeTypeRefs));\n    }\n\n    if (moduleTagRefs.length > 0) {\n      console.log(`批量保存${moduleTagRefs.length}个模块-商品标签关系`);\n      savePromises.push(batchSaveModuleTagRef(moduleTagRefs));\n    }\n\n    await Promise.all(savePromises);\n  }\n\n  /**\n   * 优化的删除无用关系\n   */\n  async optimizedDeleteUnusedRelations(componentInstance) {\n    console.log('开始处理删除无用关系...');\n\n    // 基于已加载的数据进行删除操作，避免重复查询\n    const toDeleteCategoryRefIds = [];\n    const toDeleteModuleRefIds = [];\n    const toDeleteAttrRefIds = [];\n    const toDeleteTagRefIds = [];\n\n    // 1. 找出需要删除的地市-分类关系\n    this.findUnusedCityCategoryRelations(componentInstance, toDeleteCategoryRefIds);\n\n    // 2. 找出需要删除的地市分类-模块关系\n    this.findUnusedCityCategoryModuleRelations(componentInstance, toDeleteModuleRefIds);\n\n    // 3. 找出需要删除的模块-属性关系\n    this.findUnusedModuleAttributeRelations(componentInstance, toDeleteAttrRefIds);\n\n    // 4. 找出需要删除的模块-标签关系\n    this.findUnusedModuleTagRelations(componentInstance, toDeleteTagRefIds);\n\n    console.log('待删除的关系统计:');\n    console.log(`地市-分类关系: ${toDeleteCategoryRefIds.length}个`);\n    console.log(`地市分类-模块关系: ${toDeleteModuleRefIds.length}个`);\n    console.log(`模块-属性关系: ${toDeleteAttrRefIds.length}个`);\n    console.log(`模块-标签关系: ${toDeleteTagRefIds.length}个`);\n\n    // 并行执行删除操作\n    const deletePromises = [];\n\n    if (toDeleteCategoryRefIds.length > 0) {\n      console.log(`批量删除${toDeleteCategoryRefIds.length}个地市-分类关系:`, toDeleteCategoryRefIds);\n      deletePromises.push(batchDeleteCityCategoryRef(toDeleteCategoryRefIds));\n    }\n\n    if (toDeleteModuleRefIds.length > 0) {\n      console.log(`批量删除${toDeleteModuleRefIds.length}个地市分类-模块关系:`, toDeleteModuleRefIds);\n      deletePromises.push(batchDeleteCityCategoryModuleRef(toDeleteModuleRefIds));\n    }\n\n    if (toDeleteAttrRefIds.length > 0) {\n      console.log(`批量删除${toDeleteAttrRefIds.length}个模块-属性关系:`, toDeleteAttrRefIds);\n      deletePromises.push(batchDeleteModuleAttributeTypeRef(toDeleteAttrRefIds));\n    }\n\n    if (toDeleteTagRefIds.length > 0) {\n      console.log(`批量删除${toDeleteTagRefIds.length}个模块-标签关系:`, toDeleteTagRefIds);\n      deletePromises.push(batchDeleteModuleTagRef(toDeleteTagRefIds));\n    }\n\n    await Promise.all(deletePromises);\n    console.log('删除无用关系完成');\n  }\n\n  /**\n   * 找出需要删除的地市-分类关系\n   */\n  findUnusedCityCategoryRelations(componentInstance, toDeleteRefIds) {\n    const existingRelations = this.allRelationData.cityCategories;\n\n    for (const cityCode in existingRelations) {\n      const existingRefs = existingRelations[cityCode] || [];\n      const currentCategories = componentInstance.cityCategories[cityCode] || [];\n\n      for (const ref of existingRefs) {\n        // 如果现有的关系在当前选择中不存在，则标记为删除\n        if (!currentCategories.includes(ref.categoryId)) {\n          toDeleteRefIds.push(ref.refId);\n          console.log(`标记删除地市-分类关系: ${cityCode}-${ref.categoryId} (refId: ${ref.refId})`);\n        }\n      }\n    }\n  }\n\n  /**\n   * 找出需要删除的地市分类-模块关系\n   */\n  findUnusedCityCategoryModuleRelations(componentInstance, toDeleteRefIds) {\n    const existingModuleRelations = this.allRelationData.categoryModules;\n\n    for (const cityCategoryRefId in existingModuleRelations) {\n      const existingModuleRefs = existingModuleRelations[cityCategoryRefId] || [];\n\n      // 找到对应的地市-分类组合\n      let cityCategoryKey = null;\n      for (const cityCode of componentInstance.selectedCities) {\n        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n          const refId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n          if (refId == cityCategoryRefId) {\n            cityCategoryKey = `${cityCode}-${categoryId}`;\n            break;\n          }\n        }\n        if (cityCategoryKey) break;\n      }\n\n      if (!cityCategoryKey) {\n        // 如果找不到对应的组合，说明整个地市-分类关系都被删除了，删除所有模块关系\n        for (const moduleRef of existingModuleRefs) {\n          toDeleteRefIds.push(moduleRef.refId);\n          console.log(`标记删除模块关系(地市分类已删除): refId ${moduleRef.refId}`);\n        }\n        continue;\n      }\n\n      const currentModules = componentInstance.cityModules[cityCategoryKey] || [];\n\n      for (const moduleRef of existingModuleRefs) {\n        // 如果现有的模块关系在当前选择中不存在，则标记为删除\n        if (!currentModules.includes(moduleRef.moduleId)) {\n          toDeleteRefIds.push(moduleRef.refId);\n          console.log(`标记删除模块关系: ${cityCategoryKey}-${moduleRef.moduleId} (refId: ${moduleRef.refId})`);\n        }\n      }\n    }\n  }\n\n  /**\n   * 找出需要删除的模块-属性关系\n   */\n  findUnusedModuleAttributeRelations(componentInstance, toDeleteRefIds) {\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n\n    for (const moduleRefId in existingAttributesAndTags) {\n      const moduleData = existingAttributesAndTags[moduleRefId];\n      const existingAttributes = moduleData.attributes || [];\n\n      // 找到对应的模块组合\n      let moduleKey = null;\n      for (const cityCode of componentInstance.selectedCities) {\n        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n          const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n          if (!cityCategoryRefId) continue;\n\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n          for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n            const refId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n            if (refId == moduleRefId) {\n              moduleKey = `${cityCode}-${categoryId}-${moduleId}`;\n              break;\n            }\n          }\n          if (moduleKey) break;\n        }\n        if (moduleKey) break;\n      }\n\n      if (!moduleKey) {\n        // 如果找不到对应的模块组合，说明模块关系被删除了，删除所有属性关系\n        for (const attrRef of existingAttributes) {\n          toDeleteRefIds.push(attrRef.refId);\n        }\n        continue;\n      }\n\n      const currentAttributes = componentInstance.moduleAttributes[moduleKey] || [];\n\n      for (const attrRef of existingAttributes) {\n        // 如果现有的属性关系在当前选择中不存在，则标记为删除\n        if (!currentAttributes.includes(attrRef.attributeTypeId)) {\n          toDeleteRefIds.push(attrRef.refId);\n        }\n      }\n    }\n  }\n\n  /**\n   * 找出需要删除的模块-标签关系\n   */\n  findUnusedModuleTagRelations(componentInstance, toDeleteRefIds) {\n    const existingAttributesAndTags = this.allRelationData.moduleAttributesAndTags;\n\n    console.log('开始查找需要删除的标签关系...');\n\n    for (const moduleRefId in existingAttributesAndTags) {\n      const moduleData = existingAttributesAndTags[moduleRefId];\n      const existingTags = moduleData.tags || [];\n\n      console.log(`检查模块关系 ${moduleRefId} 的标签:`, existingTags.map(tag => `${tag.tagId}(refId:${tag.refId})`));\n\n      // 找到对应的模块组合\n      let moduleKey = null;\n      for (const cityCode of componentInstance.selectedCities) {\n        for (const categoryId of componentInstance.cityCategories[cityCode] || []) {\n          const cityCategoryRefId = componentInstance.cityCategoryRefMap[`${cityCode}_${categoryId}`];\n          if (!cityCategoryRefId) continue;\n\n          const cityCategoryKey = `${cityCode}-${categoryId}`;\n          for (const moduleId of componentInstance.cityModules[cityCategoryKey] || []) {\n            const refId = componentInstance.cityCategoryModuleRefMap[`${cityCategoryRefId}_${moduleId}`];\n            if (refId == moduleRefId) {\n              moduleKey = `${cityCode}-${categoryId}-${moduleId}`;\n              break;\n            }\n          }\n          if (moduleKey) break;\n        }\n        if (moduleKey) break;\n      }\n\n      if (!moduleKey) {\n        // 如果找不到对应的模块组合，说明模块关系被删除了，删除所有标签关系\n        console.log(`模块关系 ${moduleRefId} 对应的组合不存在，删除所有标签关系`);\n        for (const tagRef of existingTags) {\n          toDeleteRefIds.push(tagRef.refId);\n          console.log(`标记删除标签关系(模块已删除): refId ${tagRef.refId}`);\n        }\n        continue;\n      }\n\n      const currentTags = componentInstance.moduleTags[moduleKey] || [];\n      console.log(`模块组合 ${moduleKey} 当前选择的标签:`, currentTags);\n\n      for (const tagRef of existingTags) {\n        // 如果现有的标签关系在当前选择中不存在，则标记为删除\n        if (!currentTags.includes(tagRef.tagId)) {\n          toDeleteRefIds.push(tagRef.refId);\n          console.log(`标记删除标签关系: ${moduleKey}-${tagRef.tagId} (refId: ${tagRef.refId})`);\n        }\n      }\n    }\n  }\n\n  /**\n   * 更新地市分类关系映射\n   */\n  async updateCityCategoryRefMap(componentInstance) {\n    // 重新获取关联ID\n    const cityCategories = await configRelationDataLoader.batchLoadCityCategories(componentInstance.selectedCities);\n\n    for (const cityCode of componentInstance.selectedCities) {\n      const categoryRefs = cityCategories[cityCode] || [];\n      for (const ref of categoryRefs) {\n        const key = `${cityCode}_${ref.categoryId}`;\n        componentInstance.cityCategoryRefMap[key] = ref.refId;\n      }\n    }\n  }\n\n  /**\n   * 更新地市分类模块关系映射\n   */\n  async updateCityCategoryModuleRefMap(componentInstance) {\n    const cityCategoryRefIds = Object.values(componentInstance.cityCategoryRefMap);\n    const categoryModules = await configRelationDataLoader.batchLoadCategoryModules(cityCategoryRefIds);\n\n    for (const refId of cityCategoryRefIds) {\n      const moduleRefs = categoryModules[refId] || [];\n      for (const moduleRef of moduleRefs) {\n        const key = `${refId}_${moduleRef.moduleId}`;\n        componentInstance.cityCategoryModuleRefMap[key] = moduleRef.refId;\n      }\n    }\n  }\n\n  /**\n   * 清除缓存\n   */\n  clearCache() {\n    this.allRelationData = null;\n  }\n}\n\n// 创建保存优化器实例\nexport const configRelationSaveOptimizer = new ConfigRelationSaveOptimizer();\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA,SACEA,6BADF,EAEEC,mCAFF,EAGEC,oCAHF,EAIEC,0BAJF,EAKEC,wBALF,EAMEC,8BANF,EAOEC,+BAPF,EAQEC,qBARF,EASEC,0BATF,EAUEC,gCAVF,EAWEC,iCAXF,EAYEC,uBAZF,EAaEC,uBAbF,EAcEC,6BAdF,EAeEC,8BAfF,EAgBEC,oBAhBF,QAiBO,8BAjBP;AAmBA;AACA;AACA;;AACA,OAAO,MAAMC,wBAAN,CAA+B;EACpCC,WAAW,GAAG;IACZ,KAAKC,KAAL,GAAa,IAAIC,GAAJ,EAAb,CADY,CACY;;IACxB,KAAKC,eAAL,GAAuB,IAAID,GAAJ,EAAvB,CAFY,CAEsB;EACnC;EAED;AACF;AACA;AACA;AACA;;;EAC+B,MAAvBE,uBAAuB,CAACC,SAAD,EAAY;IACvC,MAAMC,QAAQ,GAAI,mBAAkBD,SAAS,CAACE,IAAV,GAAiBC,IAAjB,CAAsB,GAAtB,CAA2B,EAA/D,CADuC,CAGvC;;IACA,IAAI,KAAKP,KAAL,CAAWQ,GAAX,CAAeH,QAAf,CAAJ,EAA8B;MAC5B,OAAO,KAAKL,KAAL,CAAWS,GAAX,CAAeJ,QAAf,CAAP;IACD,CANsC,CAQvC;;;IACA,IAAI,KAAKH,eAAL,CAAqBM,GAArB,CAAyBH,QAAzB,CAAJ,EAAwC;MACtC,OAAO,KAAKH,eAAL,CAAqBO,GAArB,CAAyBJ,QAAzB,CAAP;IACD,CAXsC,CAavC;;;IACA,MAAMK,cAAc,GAAG,KAAKC,uBAAL,CAA6BP,SAA7B,CAAvB;;IACA,KAAKF,eAAL,CAAqBU,GAArB,CAAyBP,QAAzB,EAAmCK,cAAnC;;IAEA,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,cAArB;MACA,KAAKV,KAAL,CAAWY,GAAX,CAAeP,QAAf,EAAyBQ,MAAzB;MACA,OAAOA,MAAP;IACD,CAJD,SAIU;MACR,KAAKX,eAAL,CAAqBY,MAArB,CAA4BT,QAA5B;IACD;EACF;EAED;AACF;AACA;;;EAC+B,MAAvBM,uBAAuB,CAACP,SAAD,EAAY;IACvC,IAAI;MACF;MACA,MAAMW,WAAW,GAAG,MAAMjC,6BAA6B,CAACsB,SAAD,CAAvD;MACA,OAAOW,WAAP;IACD,CAJD,CAIE,OAAOC,KAAP,EAAc;MACdC,OAAO,CAACC,IAAR,CAAa,qBAAb,EAAoCF,KAApC,EADc,CAGd;;MACA,MAAMH,MAAM,GAAG,EAAf;MACA,MAAMM,QAAQ,GAAGf,SAAS,CAACgB,GAAV,CAAc,MAAMC,QAAN,IAAkB;QAC/C,MAAMC,YAAY,GAAG,MAAM5B,uBAAuB,CAAC2B,QAAD,CAAlD;QACAR,MAAM,CAACQ,QAAD,CAAN,GAAmBC,YAAnB;MACD,CAHgB,CAAjB;MAKA,MAAMC,OAAO,CAACC,GAAR,CAAYL,QAAZ,CAAN;MACA,OAAON,MAAP;IACD;EACF;EAED;AACF;AACA;AACA;AACA;;;EACgC,MAAxBY,wBAAwB,CAACC,kBAAD,EAAqB;IACjD,MAAMrB,QAAQ,GAAI,oBAAmBqB,kBAAkB,CAACpB,IAAnB,GAA0BC,IAA1B,CAA+B,GAA/B,CAAoC,EAAzE;;IAEA,IAAI,KAAKP,KAAL,CAAWQ,GAAX,CAAeH,QAAf,CAAJ,EAA8B;MAC5B,OAAO,KAAKL,KAAL,CAAWS,GAAX,CAAeJ,QAAf,CAAP;IACD;;IAED,IAAI,KAAKH,eAAL,CAAqBM,GAArB,CAAyBH,QAAzB,CAAJ,EAAwC;MACtC,OAAO,KAAKH,eAAL,CAAqBO,GAArB,CAAyBJ,QAAzB,CAAP;IACD;;IAED,MAAMK,cAAc,GAAG,KAAKiB,wBAAL,CAA8BD,kBAA9B,CAAvB;;IACA,KAAKxB,eAAL,CAAqBU,GAArB,CAAyBP,QAAzB,EAAmCK,cAAnC;;IAEA,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,cAArB;MACA,KAAKV,KAAL,CAAWY,GAAX,CAAeP,QAAf,EAAyBQ,MAAzB;MACA,OAAOA,MAAP;IACD,CAJD,SAIU;MACR,KAAKX,eAAL,CAAqBY,MAArB,CAA4BT,QAA5B;IACD;EACF;EAED;AACF;AACA;;;EACgC,MAAxBsB,wBAAwB,CAACD,kBAAD,EAAqB;IACjD,IAAI;MACF,MAAMX,WAAW,GAAG,MAAMhC,mCAAmC,CAAC2C,kBAAD,CAA7D;MACA,OAAOX,WAAP;IACD,CAHD,CAGE,OAAOC,KAAP,EAAc;MACdC,OAAO,CAACC,IAAR,CAAa,qBAAb,EAAoCF,KAApC;MAEA,MAAMH,MAAM,GAAG,EAAf;MACA,MAAMM,QAAQ,GAAGO,kBAAkB,CAACN,GAAnB,CAAuB,MAAMQ,KAAN,IAAe;QACrD,MAAMC,UAAU,GAAG,MAAMlC,6BAA6B,CAACiC,KAAD,CAAtD;QACAf,MAAM,CAACe,KAAD,CAAN,GAAgBC,UAAhB;MACD,CAHgB,CAAjB;MAKA,MAAMN,OAAO,CAACC,GAAR,CAAYL,QAAZ,CAAN;MACA,OAAON,MAAP;IACD;EACF;EAED;AACF;AACA;AACA;AACA;;;EACwC,MAAhCiB,gCAAgC,CAACC,YAAD,EAAe;IACnD,MAAM1B,QAAQ,GAAI,qBAAoB0B,YAAY,CAACzB,IAAb,GAAoBC,IAApB,CAAyB,GAAzB,CAA8B,EAApE;;IAEA,IAAI,KAAKP,KAAL,CAAWQ,GAAX,CAAeH,QAAf,CAAJ,EAA8B;MAC5B,OAAO,KAAKL,KAAL,CAAWS,GAAX,CAAeJ,QAAf,CAAP;IACD;;IAED,IAAI,KAAKH,eAAL,CAAqBM,GAArB,CAAyBH,QAAzB,CAAJ,EAAwC;MACtC,OAAO,KAAKH,eAAL,CAAqBO,GAArB,CAAyBJ,QAAzB,CAAP;IACD;;IAED,MAAMK,cAAc,GAAG,KAAKsB,gCAAL,CAAsCD,YAAtC,CAAvB;;IACA,KAAK7B,eAAL,CAAqBU,GAArB,CAAyBP,QAAzB,EAAmCK,cAAnC;;IAEA,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,cAArB;MACA,KAAKV,KAAL,CAAWY,GAAX,CAAeP,QAAf,EAAyBQ,MAAzB;MACA,OAAOA,MAAP;IACD,CAJD,SAIU;MACR,KAAKX,eAAL,CAAqBY,MAArB,CAA4BT,QAA5B;IACD;EACF;EAED;AACF;AACA;;;EACwC,MAAhC2B,gCAAgC,CAACD,YAAD,EAAe;IACnD,IAAI;MACF;MACA,MAAM,CAACE,gBAAD,EAAmBC,UAAnB,IAAiC,MAAMX,OAAO,CAACC,GAAR,CAAY,CACvDxC,oCAAoC,CAAC+C,YAAD,CADmB,EAEvD9C,0BAA0B,CAAC8C,YAAD,CAF6B,CAAZ,CAA7C,CAFE,CAOF;;MACA,MAAMlB,MAAM,GAAG,EAAf,CARE,CAUF;;MACA,KAAK,MAAMsB,WAAX,IAA0BJ,YAA1B,EAAwC;QACtClB,MAAM,CAACsB,WAAD,CAAN,GAAsB;UACpBC,UAAU,EAAEH,gBAAgB,CAACE,WAAD,CAAhB,IAAiC,EADzB;UAEpBE,IAAI,EAAEH,UAAU,CAACC,WAAD,CAAV,IAA2B;QAFb,CAAtB;MAID;;MAED,OAAOtB,MAAP;IACD,CAnBD,CAmBE,OAAOG,KAAP,EAAc;MACdC,OAAO,CAACC,IAAR,CAAa,qBAAb,EAAoCF,KAApC;MAEA,MAAMH,MAAM,GAAG,EAAf;MAEA,MAAMM,QAAQ,GAAGY,YAAY,CAACX,GAAb,CAAiB,MAAMQ,KAAN,IAAe;QAC/C,MAAM,CAACU,SAAD,EAAYC,OAAZ,IAAuB,MAAMhB,OAAO,CAACC,GAAR,CAAY,CAC7C5B,8BAA8B,CAACgC,KAAD,CADe,EAE7C/B,oBAAoB,CAAC+B,KAAD,CAFyB,CAAZ,CAAnC;QAKAf,MAAM,CAACe,KAAD,CAAN,GAAgB;UACdQ,UAAU,EAAEE,SADE;UAEdD,IAAI,EAAEE;QAFQ,CAAhB;MAID,CAVgB,CAAjB;MAYA,MAAMhB,OAAO,CAACC,GAAR,CAAYL,QAAZ,CAAN;MACA,OAAON,MAAP;IACD;EACF;EAED;AACF;AACA;AACA;AACA;;;EAC2B,MAAnB2B,mBAAmB,CAACpC,SAAD,EAAY;IACnCa,OAAO,CAACwB,GAAR,CAAY,kBAAZ,EAAgCrC,SAAS,CAACsC,MAA1C;IACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB,CAFmC,CAInC;;IACA,MAAMC,cAAc,GAAG,MAAM,KAAK3C,uBAAL,CAA6BC,SAA7B,CAA7B;IACAa,OAAO,CAACwB,GAAR,CAAY,gBAAZ,EAA8BG,IAAI,CAACC,GAAL,KAAaF,SAA3C,EAAsD,IAAtD,EANmC,CAQnC;;IACA,MAAMjB,kBAAkB,GAAG,EAA3B;IACA,MAAMqB,kBAAkB,GAAG,EAA3B;;IAEA,KAAK,MAAM1B,QAAX,IAAuBjB,SAAvB,EAAkC;MAChC,MAAMkB,YAAY,GAAGwB,cAAc,CAACzB,QAAD,CAAd,IAA4B,EAAjD;;MACA,KAAK,MAAM2B,GAAX,IAAkB1B,YAAlB,EAAgC;QAC9BI,kBAAkB,CAACuB,IAAnB,CAAwBD,GAAG,CAACpB,KAA5B;QACAmB,kBAAkB,CAAE,GAAE1B,QAAS,IAAG2B,GAAG,CAACE,UAAW,EAA/B,CAAlB,GAAsDF,GAAG,CAACpB,KAA1D;MACD;IACF,CAlBkC,CAoBnC;;;IACA,IAAIuB,eAAe,GAAG,EAAtB;;IACA,IAAIzB,kBAAkB,CAACgB,MAAnB,GAA4B,CAAhC,EAAmC;MACjCS,eAAe,GAAG,MAAM,KAAK1B,wBAAL,CAA8BC,kBAA9B,CAAxB;MACAT,OAAO,CAACwB,GAAR,CAAY,cAAZ,EAA4BG,IAAI,CAACC,GAAL,KAAaF,SAAzC,EAAoD,IAApD;IACD,CAzBkC,CA2BnC;;;IACA,MAAMZ,YAAY,GAAG,EAArB;IACA,MAAMqB,wBAAwB,GAAG,EAAjC;;IAEA,KAAK,MAAMxB,KAAX,IAAoBF,kBAApB,EAAwC;MACtC,MAAMG,UAAU,GAAGsB,eAAe,CAACvB,KAAD,CAAf,IAA0B,EAA7C;;MACA,KAAK,MAAMyB,SAAX,IAAwBxB,UAAxB,EAAoC;QAClCE,YAAY,CAACkB,IAAb,CAAkBI,SAAS,CAACzB,KAA5B;QACAwB,wBAAwB,CAAE,GAAExB,KAAM,IAAGyB,SAAS,CAACC,QAAS,EAAhC,CAAxB,GAA6DD,SAAS,CAACzB,KAAvE;MACD;IACF,CArCkC,CAuCnC;;;IACA,IAAI2B,uBAAuB,GAAG;MAAEnB,UAAU,EAAE,EAAd;MAAkBC,IAAI,EAAE;IAAxB,CAA9B;;IACA,IAAIN,YAAY,CAACW,MAAb,GAAsB,CAA1B,EAA6B;MAC3Ba,uBAAuB,GAAG,MAAM,KAAKzB,gCAAL,CAAsCC,YAAtC,CAAhC;MACAd,OAAO,CAACwB,GAAR,CAAY,eAAZ,EAA6BG,IAAI,CAACC,GAAL,KAAaF,SAA1C,EAAqD,IAArD;IACD;;IAED,OAAO;MACLG,cADK;MAELK,eAFK;MAGLI,uBAHK;MAILR,kBAJK;MAKLK;IALK,CAAP;EAOD;EAED;AACF;AACA;;;EACEI,UAAU,GAAG;IACX,KAAKxD,KAAL,CAAWyD,KAAX;IACA,KAAKvD,eAAL,CAAqBuD,KAArB;EACD;EAED;AACF;AACA;;;EACEC,aAAa,GAAG;IACd,OAAO;MACLC,SAAS,EAAE,KAAK3D,KAAL,CAAW4D,IADjB;MAELC,YAAY,EAAE,KAAK3D,eAAL,CAAqB0D;IAF9B,CAAP;EAID;;AAlQmC,C,CAqQtC;;AACA,OAAO,MAAME,wBAAwB,GAAG,IAAIhE,wBAAJ,EAAjC;AAEP;AACA;AACA;;AACA,OAAO,MAAMiE,2BAAN,CAAkC;EACvChE,WAAW,GAAG;IACZ,KAAKiE,eAAL,GAAuB,IAAvB,CADY,CACiB;EAC9B;EAED;AACF;AACA;AACA;AACA;;;EACkC,MAA1BC,0BAA0B,CAACC,iBAAD,EAAoB;IAClD,MAAMvB,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB;IACA5B,OAAO,CAACwB,GAAR,CAAY,gBAAZ;;IAEA,IAAI;MACF;MACA,IAAI,CAAC,KAAKuB,eAAV,EAA2B;QACzB,MAAM,KAAKG,wBAAL,CAA8BD,iBAA9B,CAAN;MACD,CAJC,CAMF;;;MACA,MAAM3C,OAAO,CAACC,GAAR,CAAY,CAChB,KAAK4C,kCAAL,CAAwCF,iBAAxC,CADgB,EAEhB,KAAKG,wCAAL,CAA8CH,iBAA9C,CAFgB,EAGhB,KAAKI,wCAAL,CAA8CJ,iBAA9C,CAHgB,CAAZ,CAAN,CAPE,CAaF;;MACA,MAAM,KAAKK,8BAAL,CAAoCL,iBAApC,CAAN;MAEAjD,OAAO,CAACwB,GAAR,CAAa,kBAAiBG,IAAI,CAACC,GAAL,KAAaF,SAAU,IAArD;IACD,CAjBD,CAiBE,OAAO3B,KAAP,EAAc;MACdC,OAAO,CAACD,KAAR,CAAc,YAAd,EAA4BA,KAA5B;MACA,MAAMA,KAAN;IACD;EACF;EAED;AACF;AACA;;;EACgC,MAAxBmD,wBAAwB,CAACD,iBAAD,EAAoB;IAChDjD,OAAO,CAACwB,GAAR,CAAY,iBAAZ;IACA,MAAME,SAAS,GAAGC,IAAI,CAACC,GAAL,EAAlB;IAEA,KAAKmB,eAAL,GAAuB,MAAMF,wBAAwB,CAACtB,mBAAzB,CAA6C0B,iBAAiB,CAACM,cAA/D,CAA7B;IAEAvD,OAAO,CAACwB,GAAR,CAAa,oBAAmBG,IAAI,CAACC,GAAL,KAAaF,SAAU,IAAvD;EACD;EAED;AACF;AACA;;;EAC0C,MAAlCyB,kCAAkC,CAACF,iBAAD,EAAoB;IAC1D,MAAMO,gBAAgB,GAAG,EAAzB;IACA,MAAMC,iBAAiB,GAAG,KAAKV,eAAL,CAAqBlB,cAA/C;IAEA7B,OAAO,CAACwB,GAAR,CAAY,kBAAZ;IACAxB,OAAO,CAACwB,GAAR,CAAY,QAAZ,EAAsByB,iBAAiB,CAACM,cAAxC;IACAvD,OAAO,CAACwB,GAAR,CAAY,SAAZ,EAAuByB,iBAAiB,CAACpB,cAAzC;IACA7B,OAAO,CAACwB,GAAR,CAAY,SAAZ,EAAuBiC,iBAAvB;;IAEA,KAAK,MAAMrD,QAAX,IAAuB6C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,MAAMG,UAAU,GAAGT,iBAAiB,CAACpB,cAAlB,CAAiCzB,QAAjC,KAA8C,EAAjE;MACAJ,OAAO,CAACwB,GAAR,CAAa,MAAKpB,QAAS,OAA3B,EAAmCsD,UAAnC;;MAEA,KAAK,MAAMzB,UAAX,IAAyByB,UAAzB,EAAqC;QACnC,MAAMC,GAAG,GAAI,GAAEvD,QAAS,IAAG6B,UAAW,EAAtC,CADmC,CAGnC;;QACA,MAAM2B,YAAY,GAAGH,iBAAiB,CAACrD,QAAD,CAAjB,IAA+B,EAApD;QACA,MAAMyD,MAAM,GAAGD,YAAY,CAACE,IAAb,CAAkB/B,GAAG,IAAIA,GAAG,CAACE,UAAJ,KAAmBA,UAA5C,CAAf;QAEAjC,OAAO,CAACwB,GAAR,CAAa,QAAOmC,GAAI,SAAQE,MAAO,EAAvC;;QAEA,IAAI,CAACA,MAAL,EAAa;UACXL,gBAAgB,CAACxB,IAAjB,CAAsB;YACpB5B,QAAQ,EAAEA,QADU;YAEpB6B,UAAU,EAAEA,UAFQ;YAGpB8B,MAAM,EAAE,CAHY;YAIpB1E,IAAI,EAAE;UAJc,CAAtB;UAMAW,OAAO,CAACwB,GAAR,CAAa,gBAAemC,GAAI,EAAhC;QACD;MACF;IACF;;IAED,IAAIH,gBAAgB,CAAC/B,MAAjB,GAA0B,CAA9B,EAAiC;MAC/BzB,OAAO,CAACwB,GAAR,CAAa,OAAMgC,gBAAgB,CAAC/B,MAAO,YAA3C;MACA,MAAMxD,wBAAwB,CAACuF,gBAAD,CAA9B,CAF+B,CAI/B;;MACA,MAAM,KAAKQ,wBAAL,CAA8Bf,iBAA9B,CAAN;IACD;EACF;EAED;AACF;AACA;;;EACgD,MAAxCG,wCAAwC,CAACH,iBAAD,EAAoB;IAChE,MAAMgB,sBAAsB,GAAG,EAA/B;IACA,MAAMC,uBAAuB,GAAG,KAAKnB,eAAL,CAAqBb,eAArD;IAEAlC,OAAO,CAACwB,GAAR,CAAY,oBAAZ;IACAxB,OAAO,CAACwB,GAAR,CAAY,SAAZ,EAAuByB,iBAAiB,CAACkB,WAAzC;IACAnE,OAAO,CAACwB,GAAR,CAAY,WAAZ,EAAyByB,iBAAiB,CAACnB,kBAA3C;IACA9B,OAAO,CAACwB,GAAR,CAAY,WAAZ,EAAyB0C,uBAAzB;;IAEA,KAAK,MAAM9D,QAAX,IAAuB6C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,KAAK,MAAMtB,UAAX,IAAyBgB,iBAAiB,CAACpB,cAAlB,CAAiCzB,QAAjC,KAA8C,EAAvE,EAA2E;QACzE,MAAMgE,eAAe,GAAI,GAAEhE,QAAS,IAAG6B,UAAW,EAAlD;QACA,MAAMtB,KAAK,GAAGsC,iBAAiB,CAACnB,kBAAlB,CAAsC,GAAE1B,QAAS,IAAG6B,UAAW,EAA/D,CAAd;QAEAjC,OAAO,CAACwB,GAAR,CAAa,QAAO4C,eAAgB,YAAWzD,KAAM,EAArD;;QAEA,IAAI,CAACA,KAAL,EAAY;UACVX,OAAO,CAACwB,GAAR,CAAa,QAAO4C,eAAgB,aAApC;UACA;QACD;;QAED,MAAMC,OAAO,GAAGpB,iBAAiB,CAACkB,WAAlB,CAA8BC,eAA9B,KAAkD,EAAlE;QACA,MAAME,kBAAkB,GAAGJ,uBAAuB,CAACvD,KAAD,CAAvB,IAAkC,EAA7D;QAEAX,OAAO,CAACwB,GAAR,CAAa,MAAK4C,eAAgB,OAAlC,EAA0CC,OAA1C;QACArE,OAAO,CAACwB,GAAR,CAAa,MAAK4C,eAAgB,WAAlC,EAA8CE,kBAA9C;;QAEA,KAAK,MAAMjC,QAAX,IAAuBgC,OAAvB,EAAgC;UAC9B,MAAMR,MAAM,GAAGS,kBAAkB,CAACR,IAAnB,CAAwB/B,GAAG,IAAIA,GAAG,CAACM,QAAJ,KAAiBA,QAAhD,CAAf;UAEArC,OAAO,CAACwB,GAAR,CAAa,UAAS4C,eAAgB,IAAG/B,QAAS,SAAQwB,MAAO,EAAjE;;UAEA,IAAI,CAACA,MAAL,EAAa;YACXI,sBAAsB,CAACjC,IAAvB,CAA4B;cAC1BuC,iBAAiB,EAAE5D,KADO;cAE1B0B,QAAQ,EAAEA,QAFgB;cAG1B0B,MAAM,EAAE,CAHkB;cAI1BS,SAAS,EAAE;YAJe,CAA5B;YAMAxE,OAAO,CAACwB,GAAR,CAAa,aAAY4C,eAAgB,IAAG/B,QAAS,EAArD;UACD;QACF;MACF;IACF;;IAED,IAAI4B,sBAAsB,CAACxC,MAAvB,GAAgC,CAApC,EAAuC;MACrCzB,OAAO,CAACwB,GAAR,CAAa,OAAMyC,sBAAsB,CAACxC,MAAO,YAAjD;MACA,MAAMvD,8BAA8B,CAAC+F,sBAAD,CAApC,CAFqC,CAIrC;;MACA,MAAM,KAAKQ,8BAAL,CAAoCxB,iBAApC,CAAN;IACD;EACF;EAED;AACF;AACA;;;EACgD,MAAxCI,wCAAwC,CAACJ,iBAAD,EAAoB;IAChE,MAAMyB,uBAAuB,GAAG,EAAhC;IACA,MAAMC,aAAa,GAAG,EAAtB;IACA,MAAMC,yBAAyB,GAAG,KAAK7B,eAAL,CAAqBT,uBAAvD;;IAEA,KAAK,MAAMlC,QAAX,IAAuB6C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,KAAK,MAAMtB,UAAX,IAAyBgB,iBAAiB,CAACpB,cAAlB,CAAiCzB,QAAjC,KAA8C,EAAvE,EAA2E;QACzE,MAAMgE,eAAe,GAAI,GAAEhE,QAAS,IAAG6B,UAAW,EAAlD;QACA,MAAMsC,iBAAiB,GAAGtB,iBAAiB,CAACnB,kBAAlB,CAAsC,GAAE1B,QAAS,IAAG6B,UAAW,EAA/D,CAA1B;QAEA,IAAI,CAACsC,iBAAL,EAAwB;;QAExB,KAAK,MAAMlC,QAAX,IAAuBY,iBAAiB,CAACkB,WAAlB,CAA8BC,eAA9B,KAAkD,EAAzE,EAA6E;UAC3E,MAAMT,GAAG,GAAI,GAAEvD,QAAS,IAAG6B,UAAW,IAAGI,QAAS,EAAlD;UACA,MAAMnB,WAAW,GAAG+B,iBAAiB,CAACd,wBAAlB,CAA4C,GAAEoC,iBAAkB,IAAGlC,QAAS,EAA5E,CAApB;UAEA,IAAI,CAACnB,WAAL,EAAkB,SAJyD,CAM3E;;UACA,MAAM2D,kBAAkB,GAAG5B,iBAAiB,CAAC6B,gBAAlB,CAAmCnB,GAAnC,KAA2C,EAAtE;UACA,MAAMoB,kBAAkB,GAAGH,yBAAyB,CAACzD,UAA1B,CAAqCD,WAArC,KAAqD,EAAhF;UACA,MAAM8D,oBAAoB,GAAGD,kBAAkB,CAAC5E,GAAnB,CAAuB8E,IAAI,IAAIA,IAAI,CAACC,eAApC,CAA7B;;UAEA,KAAK,MAAMA,eAAX,IAA8BL,kBAA9B,EAAkD;YAChD,IAAI,CAACG,oBAAoB,CAACG,QAArB,CAA8BD,eAA9B,CAAL,EAAqD;cACnDR,uBAAuB,CAAC1C,IAAxB,CAA6B;gBAC3BoD,uBAAuB,EAAElE,WADE;gBAE3BmE,oBAAoB,EAAEH,eAFK;gBAG3BnB,MAAM,EAAE,CAHmB;gBAI3B1E,IAAI,EAAE;cAJqB,CAA7B;YAMD;UACF,CApB0E,CAsB3E;;;UACA,MAAMiG,YAAY,GAAGrC,iBAAiB,CAACsC,UAAlB,CAA6B5B,GAA7B,KAAqC,EAA1D;UACA,MAAM6B,YAAY,GAAGZ,yBAAyB,CAACxD,IAA1B,CAA+BF,WAA/B,KAA+C,EAApE;UACA,MAAMuE,cAAc,GAAGD,YAAY,CAACrF,GAAb,CAAiB8E,IAAI,IAAIA,IAAI,CAACS,KAA9B,CAAvB;;UAEA,KAAK,MAAMA,KAAX,IAAoBJ,YAApB,EAAkC;YAChC,IAAI,CAACG,cAAc,CAACN,QAAf,CAAwBO,KAAxB,CAAL,EAAqC;cACnCf,aAAa,CAAC3C,IAAd,CAAmB;gBACjB2D,UAAU,EAAED,KADK;gBAEjBN,uBAAuB,EAAElE,WAFR;gBAGjB6C,MAAM,EAAE,CAHS;gBAIjB1E,IAAI,EAAE;cAJW,CAAnB;YAMD;UACF;QACF;MACF;IACF,CAnD+D,CAqDhE;;;IACA,MAAMuG,YAAY,GAAG,EAArB;;IAEA,IAAIlB,uBAAuB,CAACjD,MAAxB,GAAiC,CAArC,EAAwC;MACtCzB,OAAO,CAACwB,GAAR,CAAa,OAAMkD,uBAAuB,CAACjD,MAAO,cAAlD;MACAmE,YAAY,CAAC5D,IAAb,CAAkB7D,+BAA+B,CAACuG,uBAAD,CAAjD;IACD;;IAED,IAAIC,aAAa,CAAClD,MAAd,GAAuB,CAA3B,EAA8B;MAC5BzB,OAAO,CAACwB,GAAR,CAAa,OAAMmD,aAAa,CAAClD,MAAO,YAAxC;MACAmE,YAAY,CAAC5D,IAAb,CAAkB5D,qBAAqB,CAACuG,aAAD,CAAvC;IACD;;IAED,MAAMrE,OAAO,CAACC,GAAR,CAAYqF,YAAZ,CAAN;EACD;EAED;AACF;AACA;;;EACsC,MAA9BtC,8BAA8B,CAACL,iBAAD,EAAoB;IACtDjD,OAAO,CAACwB,GAAR,CAAY,eAAZ,EADsD,CAGtD;;IACA,MAAMqE,sBAAsB,GAAG,EAA/B;IACA,MAAMC,oBAAoB,GAAG,EAA7B;IACA,MAAMC,kBAAkB,GAAG,EAA3B;IACA,MAAMC,iBAAiB,GAAG,EAA1B,CAPsD,CAStD;;IACA,KAAKC,+BAAL,CAAqChD,iBAArC,EAAwD4C,sBAAxD,EAVsD,CAYtD;;IACA,KAAKK,qCAAL,CAA2CjD,iBAA3C,EAA8D6C,oBAA9D,EAbsD,CAetD;;IACA,KAAKK,kCAAL,CAAwClD,iBAAxC,EAA2D8C,kBAA3D,EAhBsD,CAkBtD;;IACA,KAAKK,4BAAL,CAAkCnD,iBAAlC,EAAqD+C,iBAArD;IAEAhG,OAAO,CAACwB,GAAR,CAAY,WAAZ;IACAxB,OAAO,CAACwB,GAAR,CAAa,YAAWqE,sBAAsB,CAACpE,MAAO,GAAtD;IACAzB,OAAO,CAACwB,GAAR,CAAa,cAAasE,oBAAoB,CAACrE,MAAO,GAAtD;IACAzB,OAAO,CAACwB,GAAR,CAAa,YAAWuE,kBAAkB,CAACtE,MAAO,GAAlD;IACAzB,OAAO,CAACwB,GAAR,CAAa,YAAWwE,iBAAiB,CAACvE,MAAO,GAAjD,EAzBsD,CA2BtD;;IACA,MAAM4E,cAAc,GAAG,EAAvB;;IAEA,IAAIR,sBAAsB,CAACpE,MAAvB,GAAgC,CAApC,EAAuC;MACrCzB,OAAO,CAACwB,GAAR,CAAa,OAAMqE,sBAAsB,CAACpE,MAAO,WAAjD,EAA6DoE,sBAA7D;MACAQ,cAAc,CAACrE,IAAf,CAAoB3D,0BAA0B,CAACwH,sBAAD,CAA9C;IACD;;IAED,IAAIC,oBAAoB,CAACrE,MAArB,GAA8B,CAAlC,EAAqC;MACnCzB,OAAO,CAACwB,GAAR,CAAa,OAAMsE,oBAAoB,CAACrE,MAAO,aAA/C,EAA6DqE,oBAA7D;MACAO,cAAc,CAACrE,IAAf,CAAoB1D,gCAAgC,CAACwH,oBAAD,CAApD;IACD;;IAED,IAAIC,kBAAkB,CAACtE,MAAnB,GAA4B,CAAhC,EAAmC;MACjCzB,OAAO,CAACwB,GAAR,CAAa,OAAMuE,kBAAkB,CAACtE,MAAO,WAA7C,EAAyDsE,kBAAzD;MACAM,cAAc,CAACrE,IAAf,CAAoBzD,iCAAiC,CAACwH,kBAAD,CAArD;IACD;;IAED,IAAIC,iBAAiB,CAACvE,MAAlB,GAA2B,CAA/B,EAAkC;MAChCzB,OAAO,CAACwB,GAAR,CAAa,OAAMwE,iBAAiB,CAACvE,MAAO,WAA5C,EAAwDuE,iBAAxD;MACAK,cAAc,CAACrE,IAAf,CAAoBxD,uBAAuB,CAACwH,iBAAD,CAA3C;IACD;;IAED,MAAM1F,OAAO,CAACC,GAAR,CAAY8F,cAAZ,CAAN;IACArG,OAAO,CAACwB,GAAR,CAAY,UAAZ;EACD;EAED;AACF;AACA;;;EACEyE,+BAA+B,CAAChD,iBAAD,EAAoBqD,cAApB,EAAoC;IACjE,MAAM7C,iBAAiB,GAAG,KAAKV,eAAL,CAAqBlB,cAA/C;;IAEA,KAAK,MAAMzB,QAAX,IAAuBqD,iBAAvB,EAA0C;MACxC,MAAMG,YAAY,GAAGH,iBAAiB,CAACrD,QAAD,CAAjB,IAA+B,EAApD;MACA,MAAMmG,iBAAiB,GAAGtD,iBAAiB,CAACpB,cAAlB,CAAiCzB,QAAjC,KAA8C,EAAxE;;MAEA,KAAK,MAAM2B,GAAX,IAAkB6B,YAAlB,EAAgC;QAC9B;QACA,IAAI,CAAC2C,iBAAiB,CAACpB,QAAlB,CAA2BpD,GAAG,CAACE,UAA/B,CAAL,EAAiD;UAC/CqE,cAAc,CAACtE,IAAf,CAAoBD,GAAG,CAACpB,KAAxB;UACAX,OAAO,CAACwB,GAAR,CAAa,gBAAepB,QAAS,IAAG2B,GAAG,CAACE,UAAW,YAAWF,GAAG,CAACpB,KAAM,GAA5E;QACD;MACF;IACF;EACF;EAED;AACF;AACA;;;EACEuF,qCAAqC,CAACjD,iBAAD,EAAoBqD,cAApB,EAAoC;IACvE,MAAMpC,uBAAuB,GAAG,KAAKnB,eAAL,CAAqBb,eAArD;;IAEA,KAAK,MAAMqC,iBAAX,IAAgCL,uBAAhC,EAAyD;MACvD,MAAMI,kBAAkB,GAAGJ,uBAAuB,CAACK,iBAAD,CAAvB,IAA8C,EAAzE,CADuD,CAGvD;;MACA,IAAIH,eAAe,GAAG,IAAtB;;MACA,KAAK,MAAMhE,QAAX,IAAuB6C,iBAAiB,CAACM,cAAzC,EAAyD;QACvD,KAAK,MAAMtB,UAAX,IAAyBgB,iBAAiB,CAACpB,cAAlB,CAAiCzB,QAAjC,KAA8C,EAAvE,EAA2E;UACzE,MAAMO,KAAK,GAAGsC,iBAAiB,CAACnB,kBAAlB,CAAsC,GAAE1B,QAAS,IAAG6B,UAAW,EAA/D,CAAd;;UACA,IAAItB,KAAK,IAAI4D,iBAAb,EAAgC;YAC9BH,eAAe,GAAI,GAAEhE,QAAS,IAAG6B,UAAW,EAA5C;YACA;UACD;QACF;;QACD,IAAImC,eAAJ,EAAqB;MACtB;;MAED,IAAI,CAACA,eAAL,EAAsB;QACpB;QACA,KAAK,MAAMhC,SAAX,IAAwBkC,kBAAxB,EAA4C;UAC1CgC,cAAc,CAACtE,IAAf,CAAoBI,SAAS,CAACzB,KAA9B;UACAX,OAAO,CAACwB,GAAR,CAAa,4BAA2BY,SAAS,CAACzB,KAAM,EAAxD;QACD;;QACD;MACD;;MAED,MAAM6F,cAAc,GAAGvD,iBAAiB,CAACkB,WAAlB,CAA8BC,eAA9B,KAAkD,EAAzE;;MAEA,KAAK,MAAMhC,SAAX,IAAwBkC,kBAAxB,EAA4C;QAC1C;QACA,IAAI,CAACkC,cAAc,CAACrB,QAAf,CAAwB/C,SAAS,CAACC,QAAlC,CAAL,EAAkD;UAChDiE,cAAc,CAACtE,IAAf,CAAoBI,SAAS,CAACzB,KAA9B;UACAX,OAAO,CAACwB,GAAR,CAAa,aAAY4C,eAAgB,IAAGhC,SAAS,CAACC,QAAS,YAAWD,SAAS,CAACzB,KAAM,GAA1F;QACD;MACF;IACF;EACF;EAED;AACF;AACA;;;EACEwF,kCAAkC,CAAClD,iBAAD,EAAoBqD,cAApB,EAAoC;IACpE,MAAM1B,yBAAyB,GAAG,KAAK7B,eAAL,CAAqBT,uBAAvD;;IAEA,KAAK,MAAMpB,WAAX,IAA0B0D,yBAA1B,EAAqD;MACnD,MAAM6B,UAAU,GAAG7B,yBAAyB,CAAC1D,WAAD,CAA5C;MACA,MAAM6D,kBAAkB,GAAG0B,UAAU,CAACtF,UAAX,IAAyB,EAApD,CAFmD,CAInD;;MACA,IAAIuF,SAAS,GAAG,IAAhB;;MACA,KAAK,MAAMtG,QAAX,IAAuB6C,iBAAiB,CAACM,cAAzC,EAAyD;QACvD,KAAK,MAAMtB,UAAX,IAAyBgB,iBAAiB,CAACpB,cAAlB,CAAiCzB,QAAjC,KAA8C,EAAvE,EAA2E;UACzE,MAAMmE,iBAAiB,GAAGtB,iBAAiB,CAACnB,kBAAlB,CAAsC,GAAE1B,QAAS,IAAG6B,UAAW,EAA/D,CAA1B;UACA,IAAI,CAACsC,iBAAL,EAAwB;UAExB,MAAMH,eAAe,GAAI,GAAEhE,QAAS,IAAG6B,UAAW,EAAlD;;UACA,KAAK,MAAMI,QAAX,IAAuBY,iBAAiB,CAACkB,WAAlB,CAA8BC,eAA9B,KAAkD,EAAzE,EAA6E;YAC3E,MAAMzD,KAAK,GAAGsC,iBAAiB,CAACd,wBAAlB,CAA4C,GAAEoC,iBAAkB,IAAGlC,QAAS,EAA5E,CAAd;;YACA,IAAI1B,KAAK,IAAIO,WAAb,EAA0B;cACxBwF,SAAS,GAAI,GAAEtG,QAAS,IAAG6B,UAAW,IAAGI,QAAS,EAAlD;cACA;YACD;UACF;;UACD,IAAIqE,SAAJ,EAAe;QAChB;;QACD,IAAIA,SAAJ,EAAe;MAChB;;MAED,IAAI,CAACA,SAAL,EAAgB;QACd;QACA,KAAK,MAAMC,OAAX,IAAsB5B,kBAAtB,EAA0C;UACxCuB,cAAc,CAACtE,IAAf,CAAoB2E,OAAO,CAAChG,KAA5B;QACD;;QACD;MACD;;MAED,MAAMiG,iBAAiB,GAAG3D,iBAAiB,CAAC6B,gBAAlB,CAAmC4B,SAAnC,KAAiD,EAA3E;;MAEA,KAAK,MAAMC,OAAX,IAAsB5B,kBAAtB,EAA0C;QACxC;QACA,IAAI,CAAC6B,iBAAiB,CAACzB,QAAlB,CAA2BwB,OAAO,CAACzB,eAAnC,CAAL,EAA0D;UACxDoB,cAAc,CAACtE,IAAf,CAAoB2E,OAAO,CAAChG,KAA5B;QACD;MACF;IACF;EACF;EAED;AACF;AACA;;;EACEyF,4BAA4B,CAACnD,iBAAD,EAAoBqD,cAApB,EAAoC;IAC9D,MAAM1B,yBAAyB,GAAG,KAAK7B,eAAL,CAAqBT,uBAAvD;IAEAtC,OAAO,CAACwB,GAAR,CAAY,kBAAZ;;IAEA,KAAK,MAAMN,WAAX,IAA0B0D,yBAA1B,EAAqD;MACnD,MAAM6B,UAAU,GAAG7B,yBAAyB,CAAC1D,WAAD,CAA5C;MACA,MAAMsE,YAAY,GAAGiB,UAAU,CAACrF,IAAX,IAAmB,EAAxC;MAEApB,OAAO,CAACwB,GAAR,CAAa,UAASN,WAAY,OAAlC,EAA0CsE,YAAY,CAACrF,GAAb,CAAiB0G,GAAG,IAAK,GAAEA,GAAG,CAACnB,KAAM,UAASmB,GAAG,CAAClG,KAAM,GAAxD,CAA1C,EAJmD,CAMnD;;MACA,IAAI+F,SAAS,GAAG,IAAhB;;MACA,KAAK,MAAMtG,QAAX,IAAuB6C,iBAAiB,CAACM,cAAzC,EAAyD;QACvD,KAAK,MAAMtB,UAAX,IAAyBgB,iBAAiB,CAACpB,cAAlB,CAAiCzB,QAAjC,KAA8C,EAAvE,EAA2E;UACzE,MAAMmE,iBAAiB,GAAGtB,iBAAiB,CAACnB,kBAAlB,CAAsC,GAAE1B,QAAS,IAAG6B,UAAW,EAA/D,CAA1B;UACA,IAAI,CAACsC,iBAAL,EAAwB;UAExB,MAAMH,eAAe,GAAI,GAAEhE,QAAS,IAAG6B,UAAW,EAAlD;;UACA,KAAK,MAAMI,QAAX,IAAuBY,iBAAiB,CAACkB,WAAlB,CAA8BC,eAA9B,KAAkD,EAAzE,EAA6E;YAC3E,MAAMzD,KAAK,GAAGsC,iBAAiB,CAACd,wBAAlB,CAA4C,GAAEoC,iBAAkB,IAAGlC,QAAS,EAA5E,CAAd;;YACA,IAAI1B,KAAK,IAAIO,WAAb,EAA0B;cACxBwF,SAAS,GAAI,GAAEtG,QAAS,IAAG6B,UAAW,IAAGI,QAAS,EAAlD;cACA;YACD;UACF;;UACD,IAAIqE,SAAJ,EAAe;QAChB;;QACD,IAAIA,SAAJ,EAAe;MAChB;;MAED,IAAI,CAACA,SAAL,EAAgB;QACd;QACA1G,OAAO,CAACwB,GAAR,CAAa,QAAON,WAAY,oBAAhC;;QACA,KAAK,MAAM4F,MAAX,IAAqBtB,YAArB,EAAmC;UACjCc,cAAc,CAACtE,IAAf,CAAoB8E,MAAM,CAACnG,KAA3B;UACAX,OAAO,CAACwB,GAAR,CAAa,0BAAyBsF,MAAM,CAACnG,KAAM,EAAnD;QACD;;QACD;MACD;;MAED,MAAMoG,WAAW,GAAG9D,iBAAiB,CAACsC,UAAlB,CAA6BmB,SAA7B,KAA2C,EAA/D;MACA1G,OAAO,CAACwB,GAAR,CAAa,QAAOkF,SAAU,WAA9B,EAA0CK,WAA1C;;MAEA,KAAK,MAAMD,MAAX,IAAqBtB,YAArB,EAAmC;QACjC;QACA,IAAI,CAACuB,WAAW,CAAC5B,QAAZ,CAAqB2B,MAAM,CAACpB,KAA5B,CAAL,EAAyC;UACvCY,cAAc,CAACtE,IAAf,CAAoB8E,MAAM,CAACnG,KAA3B;UACAX,OAAO,CAACwB,GAAR,CAAa,aAAYkF,SAAU,IAAGI,MAAM,CAACpB,KAAM,YAAWoB,MAAM,CAACnG,KAAM,GAA3E;QACD;MACF;IACF;EACF;EAED;AACF;AACA;;;EACgC,MAAxBqD,wBAAwB,CAACf,iBAAD,EAAoB;IAChD;IACA,MAAMpB,cAAc,GAAG,MAAMgB,wBAAwB,CAAC3D,uBAAzB,CAAiD+D,iBAAiB,CAACM,cAAnE,CAA7B;;IAEA,KAAK,MAAMnD,QAAX,IAAuB6C,iBAAiB,CAACM,cAAzC,EAAyD;MACvD,MAAMlD,YAAY,GAAGwB,cAAc,CAACzB,QAAD,CAAd,IAA4B,EAAjD;;MACA,KAAK,MAAM2B,GAAX,IAAkB1B,YAAlB,EAAgC;QAC9B,MAAMsD,GAAG,GAAI,GAAEvD,QAAS,IAAG2B,GAAG,CAACE,UAAW,EAA1C;QACAgB,iBAAiB,CAACnB,kBAAlB,CAAqC6B,GAArC,IAA4C5B,GAAG,CAACpB,KAAhD;MACD;IACF;EACF;EAED;AACF;AACA;;;EACsC,MAA9B8D,8BAA8B,CAACxB,iBAAD,EAAoB;IACtD,MAAMxC,kBAAkB,GAAGuG,MAAM,CAACC,MAAP,CAAchE,iBAAiB,CAACnB,kBAAhC,CAA3B;IACA,MAAMI,eAAe,GAAG,MAAMW,wBAAwB,CAACrC,wBAAzB,CAAkDC,kBAAlD,CAA9B;;IAEA,KAAK,MAAME,KAAX,IAAoBF,kBAApB,EAAwC;MACtC,MAAMG,UAAU,GAAGsB,eAAe,CAACvB,KAAD,CAAf,IAA0B,EAA7C;;MACA,KAAK,MAAMyB,SAAX,IAAwBxB,UAAxB,EAAoC;QAClC,MAAM+C,GAAG,GAAI,GAAEhD,KAAM,IAAGyB,SAAS,CAACC,QAAS,EAA3C;QACAY,iBAAiB,CAACd,wBAAlB,CAA2CwB,GAA3C,IAAkDvB,SAAS,CAACzB,KAA5D;MACD;IACF;EACF;EAED;AACF;AACA;;;EACE4B,UAAU,GAAG;IACX,KAAKQ,eAAL,GAAuB,IAAvB;EACD;;AAvesC,C,CA0ezC;;AACA,OAAO,MAAMmE,2BAA2B,GAAG,IAAIpE,2BAAJ,EAApC"}, "metadata": {}, "sourceType": "module"}