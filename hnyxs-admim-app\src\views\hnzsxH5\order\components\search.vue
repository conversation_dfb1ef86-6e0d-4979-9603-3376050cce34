<!-- 订单管理搜索 -->
<template>
  <div class="search-container">
    <el-form ref="form" :model="form" class="ele-form-search" size="small">
      <el-row :gutter="20">
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="订单号:">
            <el-input
              v-model="form.orderNo"
              placeholder="请输入订单号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="用户姓名:">
            <el-input
              v-model="form.userName"
              placeholder="请输入用户姓名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="身份证号:">
            <el-input
              v-model="form.userCard"
              placeholder="请输入身份证号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="手机号码:">
            <el-input
              v-model="form.userMoble"
              placeholder="请输入手机号码"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="87单号:">
            <el-input
              v-model="form.custOrderId"
              placeholder="请输入87单号"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="商品名称:">
            <el-input
              v-model="form.goodsName"
              placeholder="请输入商品名称"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="地市:">
            <el-select v-model="form.cityCode" clearable placeholder="请选择" style="width: 100%">
              <el-option
                v-for="item in cityOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="订单状态:">
            <el-select v-model="form.state" clearable placeholder="请选择" style="width: 100%">
              <el-option
                v-for="(val, key) in statusMap"
                :key="key"
                :label="val.text"
                :value="Number(key)"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="支付状态:">
            <el-select v-model="form.paymentStatus" clearable placeholder="请选择" style="width: 100%">
              <el-option
                v-for="(val, key) in paymentStatusMap"
                :key="key"
                :label="val.text"
                :value="Number(key)"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="失败原因:">
            <el-input
              v-model="form.failReason"
              placeholder="请输入失败原因关键词"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="状态变更原因:">
            <el-input
              v-model="form.stateChangeReason"
              placeholder="请输入状态变更原因关键词"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="8" :sm="12" :xs="24">
          <el-form-item label="创建时间:">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="12" :md="8" :sm="24" :xs="24">
          <div class="search-buttons">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="search"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-left"
              @click="reset"
            >
              重置
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getOrderStatusMap, getPaymentStatusMap } from '@/api/hnzsxH5/order';

export default {
  name: 'OrderSearch',
  data() {
    return {
      // 查询表单
      form: {
        orderNo: '',
        userName: '',
        userCard: '',
        userMoble: '',
        custOrderId: '',
        goodsName: '',
        cityCode: '',
        state: null,
        paymentStatus: null,
        failReason: '',
        stateChangeReason: '',
      },
      // 日期范围选择
      dateRange: [],
      // 订单状态映射
      statusMap: getOrderStatusMap(),
      // 支付状态映射
      paymentStatusMap:{
        1: { text: '支付成功' },
        2: { text: '支付失败' },
        3: { text: '费用0，无需支付' }
      },

     // 地市选项
     cityOptions: [
        { value: '730', label: '岳阳' },
        { value: '731', label: '长沙' },
        { value: '732', label: '湘潭' },
        { value: '733', label: '株洲' },
        { value: '734', label: '衡阳' },
        { value: '735', label: '郴州' },
        { value: '736', label: '常德' },
        { value: '737', label: '益阳' },
        { value: '738', label: '娄底' },
        { value: '739', label: '邵阳' },
        { value: '743', label: '湘西' },
        { value: '744', label: '张家界' },
        { value: '745', label: '怀化' },
        { value: '746', label: '永州' }
      ]
    };
  },
  methods: {
    // 搜索按钮点击事件
    search() {
      // 处理日期范围
      const searchForm = { ...this.form };
      
      // 确保状态值为数字类型
      if (searchForm.state !== null && searchForm.state !== undefined && searchForm.state !== '') {
        searchForm.state = Number(searchForm.state);
      }
      
      // 确保支付状态为数字类型
      if (searchForm.paymentStatus !== null && searchForm.paymentStatus !== undefined && searchForm.paymentStatus !== '') {
        searchForm.paymentStatus = Number(searchForm.paymentStatus);
      }
      
      if (this.dateRange && this.dateRange.length === 2) {
        searchForm.createdDateStart = this.dateRange[0] + ' 00:00:00';
        searchForm.createdDateEnd = this.dateRange[1] + ' 23:59:59';
      }
      
      // 移除可能的空字符串，避免后端处理问题
      Object.keys(searchForm).forEach(key => {
        if (searchForm[key] === '') {
          searchForm[key] = null;
        }
      });
      
      console.log('发送搜索条件:', JSON.stringify(searchForm));
      this.$emit('search', searchForm);
    },
    // 重置按钮点击事件
    reset() {
      // 先清空表单数据
      this.form = {
        orderNo: '',
        userName: '',
        userCard: '',
        userMoble: '',
        custOrderId: '',
        goodsName: '',
        cityCode: '',
        state: null,
        paymentStatus: null,
        failReason: '',
        stateChangeReason: '',
      };
      this.dateRange = [];
      
      // 然后重置表单，确保UI也刷新
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        
        // 发送空查询条件，触发刷新
        console.log('重置查询条件');
        this.$emit('search', {});
      });
    },
    // 获取当前查询参数，供外部组件调用
    getParams() {
      const searchForm = { ...this.form };
      
      // 确保状态值为数字类型
      if (searchForm.state !== null && searchForm.state !== undefined && searchForm.state !== '') {
        searchForm.state = Number(searchForm.state);
      }
      
      // 确保支付状态为数字类型
      if (searchForm.paymentStatus !== null && searchForm.paymentStatus !== undefined && searchForm.paymentStatus !== '') {
        searchForm.paymentStatus = Number(searchForm.paymentStatus);
      }
      
      if (this.dateRange && this.dateRange.length === 2) {
        searchForm.createdDateStart = this.dateRange[0] + ' 00:00:00';
        searchForm.createdDateEnd = this.dateRange[1] + ' 23:59:59';
      }
      
      // 移除可能的空字符串，避免后端处理问题
      Object.keys(searchForm).forEach(key => {
        if (searchForm[key] === '' || searchForm[key] === null || searchForm[key] === undefined) {
          delete searchForm[key];
        }
      });
      
      return searchForm;
    }
  }
};
</script>

<style lang="scss" scoped>
.search-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  .ele-form-search {
    .el-row {
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-form-item {
      margin-bottom: 0;
      
      :deep(.el-form-item__label) {
        padding-right: 8px;
        color: #606266;
        font-weight: 500;
      }
    }
    
    .search-buttons {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 8px;
      height: 32px;
      
      .el-button {
        margin-left: 15px;
        padding-left: 15px;
        padding-right: 15px;
      }
    }
  }
}

@media screen and (max-width: 1400px) {
  .search-container {
    .ele-form-search {
      .el-col {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style> 